import { PrismaClient } from '@prisma/client';

const prisma = new PrismaClient();

export default async function handler(req, res) {
    if (req.method === 'PUT') {
        const { id, stageId, name, description, redirect, date, gateDate, vendorId } = req.body;

        try {
            const updatedConcert = await prisma.concert.update({
                where: {
                    id: parseInt(id),
                },
                data: {
                    vendorId,
                    stageId,
                    name,
                    description,
                    redirect,
                    date,
                    gateDate,
                },
            });

            res.status(200).json({ message: 'Konser güncellendi!', updatedItem: updatedConcert });
        } catch (error) {
            console.error(error);
            res.status(500).json({ message: 'Internal server error', error });
        }
    } else {
        res.status(405).json({ message: 'Method not allowed' });
    }
}
