import React, { useState, useMemo, useCallback, useEffect } from "react";
import {
    Table,
    TableHeader,
    TableColumn,
    TableBody,
    TableRow,
    TableCell,
    Input,
    Button,
    Pagination,
    Spinner,
    getKeyValue,
    User,
    Card,
    CardBody
} from "@nextui-org/react";
import { Container, Grid, Typography } from '@mui/material';
import { SearchIcon } from "../../src/components/SearchIcon";
import { withRouter } from 'next/router';
import Swal from 'sweetalert2';

function PaymentTransferTable() {
    const [filterValue, setFilterValue] = useState("");
    const [page, setPage] = useState(1);
    const [total, setTotal] = useState(0);
    const [isLoading, setIsLoading] = useState(true);
    const [transferLoading, setTransferLoading] = useState(false);
    const [items, setItems] = useState([]);

    const rowsPerPage = 10;

    const loadList = async (pageInput: number, filter: string) => {
        const url = `/api/payment-transfer?page=${pageInput}&pageSize=${rowsPerPage}&filter=${filter}`;

        try {
            const res = await fetch(url);

            if (res.status === 200) {
                const json = await res.json();

                setTotal(json.totalCount);
                setIsLoading(false);
                setItems(json.results);

                console.log(json.results);
            } else {
                setIsLoading(false);
                setItems([]);
            }
        } catch (error) {
            setIsLoading(false);
            setItems([]);
            console.error(error);
            throw error;
        }
    };

    const handleTransfer = async (id: number) => {
        setTransferLoading(true);
        try {
            const response = await fetch('https://api.hollystone.com.tr/api/payment/transfer-payment', {
                method: 'POST',
                headers: {
                    'Authorization': `Bearer ${localStorage.getItem('token')}`,
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({ concertId: id }),
            });

            const data = await response.json();
            if (data.type == "success") {
                Swal.fire({
                    icon: 'success',
                    title: 'Başarılı',
                    text: data.message,
                });
                loadList(page, filterValue);
            } else {
                Swal.fire({
                    icon: 'error',
                    title: 'Hata',
                    text: data.error,
                });
            }
        } catch (error) {
            Swal.fire({
                icon: 'error',
                title: 'Hata',
                text: 'Bir sorun meydana geldi!',
            });
        }
        setTransferLoading(false);
    }

    useEffect(() => {
        loadList(1, filterValue);
    }, []);

    const pages = Math.ceil(total / rowsPerPage);

    const onPaginationChange = useCallback(
        async (pageInput) => {
            setIsLoading(true);
            setPage(pageInput);
            await loadList(pageInput, filterValue);
        },
        []
    );

    const onSearchChange = useCallback(
        (value: string) => {
            if (value) {
                setIsLoading(true);
                setFilterValue(value);
                loadList(page, value);
                setPage(1);
            } else {
                setFilterValue("");
                loadList(page, "");
            }
        },
        []
    );

    const onClear = useCallback(() => {
        setFilterValue("");
        setPage(1);
    }, []);

    const topContent = useMemo(() => {
        return (
            <div className="flex flex-col gap-4">
                <div className="flex justify-between gap-3 items-end">
                    <Input
                        isClearable
                        className="w-full sm:max-w-[44%]"
                        placeholder="Arayın..."
                        startContent={<SearchIcon />}
                        value={filterValue}
                        onClear={() => onClear()}
                        onValueChange={onSearchChange}
                    />
                </div>
            </div>
        );
    }, [
        filterValue,
        onSearchChange,
        onClear,
    ]);

    const renderCell = useCallback((cellValue: any, columnKey: any, rowId: any, item: any) => {
        switch (columnKey) {
            case "image":
                return (
                    <User name=""
                        avatarProps={{ radius: "lg", src: `https://api.hollystone.com.tr/resources/images/${cellValue}` }}
                    >
                    </User>
                );
            case "actions":
                return (
                    item.totalPayment === item.totalTickets ? (
                        <Button color="default" disabled>Aktarıldı</Button>
                    ) : (
                        <Button color="primary" onClick={() => {
                            handleTransfer(rowId);
                        }}>Aktar</Button>
                    )
                );
            case "date":
                const date = new Date(cellValue);
                const formattedDate = `${String(date.getUTCDate()).padStart(2, '0')}-${String(date.getUTCMonth() + 1).padStart(2, '0')}-${date.getUTCFullYear()} ${String(date.getUTCHours()).padStart(2, '0')}:${String(date.getUTCMinutes()).padStart(2, '0')}`;
                return (
                    formattedDate
                );
            case "transmission":
                return `${item.totalPayment} / ${item.totalTickets}`;
            case "totalPrice":
                return `${item.totalTicketsPrice} ₺`
            default:
                return cellValue;
        }
    }, []);

    if (transferLoading) return (
        <Container maxWidth="lg">
            <Grid
                container
                direction="row"
                justifyContent="center"
                alignItems="stretch"
                spacing={4}
            >
                <Grid item xs={12}>
                    <Card
                        isBlurred
                        className="border-none bg-white dark:bg-default-100/50 max-w-[610px]"
                        shadow="sm"
                    >
                        <CardBody>
                            <div className="flex">
                                <div className="p-4 w-full flex flex-col justify-center items-center">
                                    <Typography variant="h2" sx={{ mb: 5 }}>
                                        Ödeme aktarılıyor. Lütfen bekleyin...
                                    </Typography>
                                    <Spinner />
                                </div>
                            </div>
                        </CardBody>
                    </Card>
                </Grid>
            </Grid>
        </Container>
    );

    return (
        <Table
            aria-label="Example table with client async pagination"
            topContent={topContent}
            topContentPlacement="inside"
            bottomContent={
                pages > 0 ? (
                    <div className="flex w-full justify-center">
                        <Pagination
                            isCompact
                            showControls
                            showShadow
                            color="primary"
                            page={page}
                            total={pages}
                            onChange={onPaginationChange}
                        />
                    </div>
                ) : null
            }
            classNames={{
                table: "min-h-[400px]",
            }}
        >
            <TableHeader>
                <TableColumn key="image">Fotoğraf</TableColumn>
                <TableColumn key="name">Başlık</TableColumn>
                <TableColumn key="date">Tarih</TableColumn>
                <TableColumn key="totalPrice">Toplam Tutar</TableColumn>
                <TableColumn key="transmission">Aktarılan</TableColumn>
                <TableColumn key="guestTicketsCount">Misafir</TableColumn>
                <TableColumn key="actions">İşlemler</TableColumn>
            </TableHeader>
            <TableBody
                isLoading={isLoading && !items.length}
                items={items}
                loadingContent={<Spinner />}
                emptyContent={"Kayıt bulunamadı!"}
            >
                {(item) => (
                    <TableRow key={item.id}>
                        {(columnKey) => (
                            <TableCell key={columnKey+item.id}>{renderCell(getKeyValue(item, columnKey), columnKey, item.id, item)}</TableCell>
                        )}
                    </TableRow>
                )}
            </TableBody>
        </Table>
    );
}

export default withRouter(PaymentTransferTable);
