import React from 'react';
import Head from 'next/head';
import { authMiddleware } from '../../middleware';
import SidebarLayout from '@/layouts/SidebarLayout';
import { Container, Grid, Typography } from '@mui/material';
import Footer from '@/components/Footer';
import AnnouncementsTable from 'pages/components/announcements-table';

const AnnouncementsPage = () => {
        return (
            <>
                <Head>
                    <title>Duyurular</title>
                </Head>
                <Container maxWidth="lg">
                    <Grid
                        container
                        direction="row"
                        justifyContent="center"
                        alignItems="stretch"
                        spacing={4}
                    >
                        <Grid item xs={12}>
                            <AnnouncementsTable></AnnouncementsTable>
                        </Grid>
                    </Grid>
                </Container>
                <Footer />
            </>
        );
}

AnnouncementsPage.getLayout = (page: any) => <SidebarLayout>{page}</SidebarLayout>;

export const getServerSideProps = authMiddleware(async (_context: any) => {
    // Your page-specific logic...
    return {
        props: {}, // You can add any props you need for the page
    };
});

export default AnnouncementsPage;
