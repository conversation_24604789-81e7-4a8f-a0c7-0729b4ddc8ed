import React, { useState, useMemo, useCallback, useEffect } from "react";
import {
    Table,
    TableHeader,
    TableColumn,
    TableBody,
    TableRow,
    TableCell,
    Input,
    Button,
    DropdownTrigger,
    Dropdown,
    DropdownMenu,
    DropdownItem,
    Pagination,
    Spinner,
    get<PERSON>eyValue,
    User,
} from "@nextui-org/react";
import { SearchIcon } from "../../src/components/SearchIcon";
import { PlusIcon } from "../../src/components/PlusIcon";
import { VerticalDotsIcon } from "../../src/components/VerticalDotsIcon";
import { withRouter } from 'next/router';
import Swal from 'sweetalert2';
import dayjs from "dayjs";

function UsersTable({ router }) {
    const [filterValue, setFilterValue] = useState("");
    const [page, setPage] = useState(1);
    const [total, setTotal] = useState(0);
    const [isLoading, setIsLoading] = useState(true);
    const [items, setItems] = useState([]);

    const rowsPerPage = 10;

    const loadList = async (pageInput: number, filter: string) => {
        const url = `/api/users?page=${pageInput}&pageSize=${rowsPerPage}&filter=${filter}`;

        try {
            const res = await fetch(url);

            if (res.status === 200) {
                const json = await res.json();

                setTotal(json.totalCount);
                setIsLoading(false);
                setItems(json.results);
            } else {
                setIsLoading(false);
                setItems([]);
            }
        } catch (error) {
            setIsLoading(false);
            setItems([]);
            console.error(error);
            throw error;
        }
    };

    const handleNew = () => {
        router.push('/users/new');
    };

    const handleEdit = (rowId) => {
        router.push('/users/edit?id=' + rowId);
    };

    const handleDelete = async (rowId) => {
        const shouldDelete = await Swal.fire({
            title: 'Dikkat!',
            text: 'Bu kaydı silmek istediğine gerçekten emin misin?',
            icon: 'warning',
            showCancelButton: true,
            confirmButtonText: 'Evet, sil!',
            cancelButtonText: 'Vazgeç',
        });

        if (shouldDelete.isConfirmed) {
            try {
                const response = await fetch(`/api/users/delete`, {
                    method: 'DELETE',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({ id: rowId }),
                });

                if (response.ok) {
                    await loadList(1, filterValue);
                    Swal.fire({
                        icon: 'success',
                        title: 'Başarılı',
                        text: 'Kayıt silindi!',
                    });
                } else {
                    Swal.fire({
                        icon: 'error',
                        title: 'Hata',
                        text: 'Bir sorun meydana geldi!',
                    });
                }
            } catch (error) {
                console.error('An error occurred:', error);
            }
        }
    };

    const handleRemoveBlock = async (rowId) => {
        try {
            const response = await fetch(`/api/users/remove-block`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({ id: rowId }),
            });

            if (response.ok) {
                await loadList(1, filterValue);
                Swal.fire({
                    icon: 'success',
                    title: 'Başarılı',
                    text: 'Engel kaldırıldı!',
                });
            } else {
                Swal.fire({
                    icon: 'error',
                    title: 'Hata',
                    text: 'Bir sorun meydana geldi!',
                });
            }
        } catch (error) {
            console.error('An error occurred:', error);
        }
    }

    const handleBlock = async (rowId) => {
        const shouldDelete = await Swal.fire({
            title: 'Dikkat!',
            text: 'Bu kaydı engellemek istediğine gerçekten emin misin?',
            icon: 'warning',
            showCancelButton: true,
            confirmButtonText: 'Evet, engelle!',
            cancelButtonText: 'Vazgeç',
        });

        if (shouldDelete.isConfirmed) {
            try {
                const response = await fetch(`/api/users/block`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({ id: rowId }),
                });

                if (response.ok) {
                    await loadList(1, filterValue);
                    Swal.fire({
                        icon: 'success',
                        title: 'Başarılı',
                        text: 'Kullanıcı engellendi!',
                    });
                } else {
                    Swal.fire({
                        icon: 'error',
                        title: 'Hata',
                        text: 'Bir sorun meydana geldi!',
                    });
                }
            } catch (error) {
                console.error('An error occurred:', error);
            }
        }
    }

    useEffect(() => {
        loadList(1, filterValue);
    }, []);

    const pages = Math.ceil(total / rowsPerPage);

    const onPaginationChange = useCallback(
        async (pageInput) => {
            setIsLoading(true);
            setPage(pageInput);
            await loadList(pageInput, filterValue);
        },
        []
    );

    const onSearchChange = useCallback(
        (value: string) => {
            if (value) {
                setIsLoading(true);
                setFilterValue(value);
                loadList(page, value);
                setPage(1);
            } else {
                setFilterValue("");
                loadList(page, "");
            }
        },
        []
    );

    const onClear = useCallback(() => {
        setFilterValue("");
        setPage(1);
    }, []);

    const topContent = useMemo(() => {
        return (
            <div className="flex flex-col gap-4">
                <div className="flex justify-between gap-3 items-end">
                    <Input
                        isClearable
                        className="w-full sm:max-w-[44%]"
                        placeholder="Arayın..."
                        startContent={<SearchIcon />}
                        value={filterValue}
                        onClear={() => onClear()}
                        onValueChange={onSearchChange}
                    />
                    <div className="flex gap-3">
                        <Button color="primary" onClick={handleNew} endContent={<PlusIcon width={undefined} height={undefined} />}>
                            Yeni
                        </Button>
                    </div>
                </div>
            </div>
        );
    }, [
        filterValue,
        onSearchChange,
        onClear,
    ]);

    const renderCell = useCallback((cellValue: any, columnKey: any, rowId: any) => {
        const cell = items.find(item => item.id === rowId);
        const birthDateObj: Date = new Date(cell.dateOfBirth);
        const currentDate: Date = new Date();
        const ageInMilliseconds: number = currentDate.getTime() - birthDateObj.getTime();
        const ageInYears = Math.floor(ageInMilliseconds / (365.25 * 24 * 60 * 60 * 1000));
        switch (columnKey) {
            case "image":
                return (
                    <User name=""
                        avatarProps={{ radius: "lg", src: `https://api.hollystone.com.tr/resources/images/${cellValue}` }}
                    >
                    </User>
                );
            case "actions":
                return (
                    <div className="relative flex justify-end items-center gap-2">
                        <Dropdown>
                            <DropdownTrigger>
                                <Button isIconOnly size="sm" variant="light">
                                    <VerticalDotsIcon className="text-default-300" width={undefined} height={undefined} />
                                </Button>
                            </DropdownTrigger>
                            <DropdownMenu>
                                <DropdownItem onClick={() => handleEdit(rowId)}>Düzenle</DropdownItem>
                                <DropdownItem onClick={() => handleDelete(rowId)}>Sil</DropdownItem>
                                {
                                    cell.status == true ?
                                    (
                                        <DropdownItem onClick={() => handleBlock(rowId)}>Engelle</DropdownItem>  
                                    )
                                    :
                                    (
                                        <DropdownItem onClick={() => handleRemoveBlock(rowId)}>Engeli Kaldır</DropdownItem>
                                    )
                                }
                            </DropdownMenu>
                        </Dropdown>
                    </div>
                );
            case "registrationDate":
                return (
                    dayjs(cell?.createdAt).format("DD/MM/YYYY")
                );
            case "age":
                return (
                    ageInYears
                );
            default:
                return cellValue;
        }
    }, [items]);

    return (
        <Table
            aria-label="Example table with client async pagination"
            topContent={topContent}
            topContentPlacement="inside"
            bottomContent={
                pages > 0 ? (
                    <div className="flex w-full justify-center">
                        <Pagination
                            isCompact
                            showControls
                            showShadow
                            color="primary"
                            page={page}
                            total={pages}
                            onChange={onPaginationChange}
                        />
                    </div>
                ) : null
            }
            classNames={{
                table: "min-h-[400px]",
            }}
        >
            <TableHeader>
                <TableColumn key="image">Fotoğraf</TableColumn>
                <TableColumn key="firstName">İsim</TableColumn>
                <TableColumn key="lastName">Soyisim</TableColumn>
                <TableColumn key="age">Yaş</TableColumn>
                <TableColumn key="phoneNumber">Telefon</TableColumn>
                <TableColumn key="email">E-mail</TableColumn>
                <TableColumn key="registrationDate">Kayıt Tarihi</TableColumn>
                <TableColumn key="actions">İşlemler</TableColumn>
            </TableHeader>
            <TableBody
                isLoading={isLoading && !items.length}
                items={items}
                loadingContent={<Spinner />}
                emptyContent={"Kayıt bulunamadı!"}
            >
                {(item) => (
                    <TableRow key={item.id}>
                        {(columnKey) => (
                            <TableCell key={columnKey+item.id}>{renderCell(getKeyValue(item, columnKey), columnKey, item.id)}</TableCell>
                        )}
                    </TableRow>
                )}
            </TableBody>
        </Table>
    );
}

export default withRouter(UsersTable);
