import { PrismaClient } from '@prisma/client';

const prisma = new PrismaClient();

export default async function handler(req, res) {
    if (req.method === 'POST') {
        const { stageId, name, date, gateDate, description, redirect, vendorId } = req.body;

        try {
            // gateDate'i doğru bir Date nesnesine çevir
            const gateDateTime = new Date(date); // date temel alınarak başlatılıyor
            gateDateTime.setHours(gateDate.hour || 0);
            gateDateTime.setMinutes(gateDate.minute || 0);
            gateDateTime.setSeconds(gateDate.second || 0);
            gateDateTime.setMilliseconds(gateDate.millisecond || 0);

            // Konseri oluştur
            const createdConcert = await prisma.concert.create({
                data: {
                    vendorId,
                    stageId,
                    name,
                    description,
                    redirect,
                    date: new Date(date),
                    gateDate: gateDateTime, // Burada düzeltilmiş Date nesnesi kull<PERSON>ıyor
                    image: "concerts/default.webp", // Varsayılan resim
                    isDeleted: false,
                },
            });

            // Re<PERSON>i g<PERSON>
            await prisma.concert.update({
                where: {
                    id: createdConcert.id,
                },
                data: {
                    image: `concerts/${createdConcert.id}.webp`,
                },
            });

            res.status(201).json({ message: 'Konser eklendi!', createdItem: createdConcert });
        } catch (error) {
            console.error(error);
            res.status(500).json({ message: 'Internal server error', error });
        }
    } else {
        res.status(405).json({ message: 'Method not allowed' });
    }
}

