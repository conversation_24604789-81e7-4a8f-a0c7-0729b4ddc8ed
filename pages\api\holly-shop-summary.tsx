import { PrismaClient } from '@prisma/client';

const prisma = new PrismaClient();

export default async function handler(req, res) {
    if (req.method !== 'GET') {
        return res.status(405).json({ message: 'Method not allowed' });
    }

    try {
        const totalIncome = await prisma.$queryRaw`SELECT SUM(price) as total, COUNT(price) as totalCount FROM orders WHERE DATE_FORMAT(createdAt, '%Y-%m') = DATE_FORMAT(CURRENT_DATE, '%Y-%m') AND payment = true AND status IN (1,2,3);`;
        const total = await prisma.$queryRaw`SELECT SUM(price) as total, COUNT(price) as totalCount FROM orders WHERE DATE_FORMAT(createdAt, '%Y-%m') = DATE_FORMAT(CURRENT_DATE, '%Y-%m') AND payment = true;`;
        const totalIncomeVendors = await prisma.$queryRaw`SELECT v.name AS vendorName, SUM(o.price) AS totalIncome FROM orders o INNER JOIN vendors v ON v.id = o.vendorId WHERE DATE_FORMAT(o.createdAt, '%Y-%m') = DATE_FORMAT(CURRENT_DATE, '%Y-%m') AND o.payment = true AND o.status IN (1,2,3) GROUP BY v.name;`;
        const totalRefund = await prisma.$queryRaw`SELECT SUM(price) as total, COUNT(price) as totalCount FROM orders WHERE DATE_FORMAT(createdAt, '%Y-%m') = DATE_FORMAT(CURRENT_DATE, '%Y-%m') AND payment = true AND status = 4;`;
        const bestSeller = await prisma.$queryRaw`SELECT sp.name, sp.images FROM ( SELECT oi.productId, SUM(oi.quantity) AS total_quantity FROM orders o INNER JOIN order_items oi ON oi.orderId = o.id WHERE o.payment = true AND o.status IN (1,2,3) GROUP BY oi.productId ORDER BY total_quantity DESC LIMIT 1 ) AS most_sold_product INNER JOIN shop_products sp ON sp.id = most_sold_product.productId;`;
        const maxRating = await prisma.$queryRaw`SELECT sp.name, sp.images, sr.rating AS maxRating FROM shop_products sp INNER JOIN ( SELECT productId, MAX(rating) AS rating FROM shop_ratings GROUP BY productId ) sr ON sp.id = sr.productId ORDER BY maxRating DESC LIMIT 1;`;

        // Close the Prisma client connection
        await prisma.$disconnect();

        res.status(200).json({ totalIncome: totalIncome[0].total, totalIncomeCount: parseInt(totalIncome[0].totalCount), totalRefund: totalRefund[0].total, totalRefundCount: parseInt(totalRefund[0].totalCount), total: total[0].total, totalCount: parseInt(total[0].totalCount), bestSeller: bestSeller[0], maxRating: maxRating[0], totalIncomeVendors });
    } catch (error) {
        console.error(error);
        // Close the Prisma client connection
        await prisma.$disconnect();
        res.status(500).json({ message: 'Internal server error' });
    }
}
