import { PrismaClient } from '@prisma/client';

const prisma = new PrismaClient();

export default async function handler(req, res) {
    if (req.method !== 'GET') {
        return res.status(405).json({ message: 'Method not allowed' });
    }

    const { id, amount } = req.query;

    try {
        const user: any = await prisma.user.findFirst({
            where: {
                id: parseInt(id),
            }
        });

        const settings: any = await prisma.setting.findFirst({
            where: {
                id: 1
            }
        })

        // Close the Prisma client connection
        await prisma.$disconnect();

        if (!user || !settings) {
            return res.status(404).json({ message: 'Kullanıcı bulunamadı' });
        }

        const balance = user.hollyPoints - amount;
        if (balance < 0) {
            return res.status(400).json({ message: 'Yet<PERSON><PERSON> bakiye' });
        }

        res.status(200).json({ result: true, message: `${amount} Holly Puan (${settings?.hollyPointsValue * amount} ₺) kullanıma uygun. Kullanmak istiyor musunuz?` });
    } catch (error) {
        console.error(error);
        // Close the Prisma client connection
        await prisma.$disconnect();
        res.status(500).json({ message: 'Internal server error' });
    }
}
