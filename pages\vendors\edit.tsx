import React, { useState, useEffect } from 'react';
import Head from 'next/head';
import { useRouter } from 'next/router';
import { authMiddleware } from 'middleware';
import Swal from 'sweetalert2';
import { Container, Grid, Typography } from '@mui/material';
import { Card, CardBody, CardHeader, Button, Input, Textarea, Divider } from '@nextui-org/react';
import { Select, SelectItem } from "@nextui-org/select";
import SidebarLayout from '@/layouts/SidebarLayout';
import PageTitleWrapper from '@/components/PageTitleWrapper';
import Footer from '@/components/Footer';
import ArrowBackIcon from '@mui/icons-material/ArrowBack';
import Link from 'next/link';

const moduleList = [
    "GÜNLÜK ETKİNLİK", "HOLLY PUAN", "KONSERLER", "HOLLYTİCET",
    "HOLLYSHOP", "HOLLY CHAT", "HOLLY SNAP", "<PERSON><PERSON><PERSON>",
    "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"
];

const EditStagePage = () => {
    const router = useRouter();
    const [item, setItem]: any = useState({});
    const [cities, setCities] = useState([]);
    const { id } = router.query;
    const [loading, setLoading] = useState(true);
    const [selectedModules, setSelectedModules] = useState([]);


    const modules = [
        "Holly Shop"
    ]

    const loadList = async () => {
        const url = `/api/vendors?id=${id}`;

        try {
            const res = await fetch(url);

            if (res.status === 200) {
                const json = await res.json();
                setItem(json.results[0]);
                setSelectedModules(json.results[0].modules?.split(',').map(Number) || []);
            } else {
                Swal.fire({
                    icon: 'error',
                    title: 'Hata',
                    text: 'Bir sorun meydana geldi!',
                });
            }
        } catch (error) {
            console.error(error);
        } finally {
            setLoading(false);
        }
    };

    const loadCities = async () => {
        const url = `/api/cities`;

        try {
            const res = await fetch(url);

            if (res.status === 200) {
                const json = await res.json();
                setCities(json.results);
            } else {
                Swal.fire({
                    icon: 'error',
                    title: 'Hata',
                    text: 'Bir sorun meydana geldi!',
                });
            }
        } catch (error) {
            console.error(error);
        } finally {
            setLoading(false);
        }
    };

    useEffect(() => {
        loadList();
        loadCities();
    }, []);

    const handleDelete = async () => {
        const shouldDelete = await Swal.fire({
            title: 'Dikkat!',
            text: 'Bu kaydı silmek istediğine gerçekten emin misin?',
            icon: 'warning',
            showCancelButton: true,
            confirmButtonText: 'Evet, sil!',
            cancelButtonText: 'Vazgeç',
        });

        if (shouldDelete.isConfirmed) {
            try {
                const response = await fetch(`/api/vendors/delete`, {
                    method: 'DELETE',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({ id: id }),
                });

                if (response.ok) {
                    router.push('/vendors');
                    Swal.fire({
                        icon: 'success',
                        title: 'Başarılı',
                        text: 'Kayıt silindi!',
                    });
                } else {
                    Swal.fire({
                        icon: 'error',
                        title: 'Hata',
                        text: 'Bir sorun meydana geldi!',
                    });
                }
            } catch (error) {
                console.error('An error occurred:', error);
            }
        }
    };

    const handleUpdate = async (event) => {
        event.preventDefault();
        try {
            const response = await fetch(`/api/vendors/update`, {
                method: 'PUT',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({ ...item, modules: selectedModules.join(',') })
            });

            if (response.ok) {
                await loadList();
                Swal.fire({
                    icon: 'success',
                    title: 'Başarılı',
                    text: 'Kayıt güncellendi!',
                });
            } else {
                Swal.fire({
                    icon: 'error',
                    title: 'Hata',
                    text: 'Bir sorun meydana geldi!',
                });
            }
        } catch (error) {
            console.error('An error occurred:', error);
        }
    }

    if (loading) {
        return "Loading";
    }

    const handleModuleChange = (moduleId) => {
        setSelectedModules((prev) => {
            return prev.includes(moduleId) ? prev.filter(id => id !== moduleId) : [...prev, moduleId];
        });
    };


    return (
        <>
            <Head>
                <title>Bayi - Düzenle</title>
            </Head>
            <PageTitleWrapper>
                <Grid container alignItems="center">
                    <Grid item>
                        <Typography variant="h3" component="h3" gutterBottom>
                            Bayi Düzenle
                        </Typography>
                    </Grid>
                </Grid>
            </PageTitleWrapper>
            <Container maxWidth="lg">
                <Grid
                    container
                    direction="row"
                    justifyContent="center"
                    alignItems="stretch"
                    spacing={4}
                >
                    <Grid item xs={12}>
                        <Card
                            isBlurred
                            className="border-none bg-white dark:bg-default-100/50 "
                            shadow="sm"
                        >
                            <form onSubmit={handleUpdate}>
                                <CardHeader className="flex justify-between gap-3">
                                    <div>
                                        <Link href="/vendors">
                                            <Button type="button" color="default" startContent={<ArrowBackIcon />}>
                                                Bayiler
                                            </Button>
                                        </Link>
                                    </div>
                                    <div>Bayi Bilgileri</div>
                                    <div className="flex-end">
                                        <Button type="button" onClick={handleDelete} color="danger" className="mr-2">
                                            Sil
                                        </Button>
                                        <Button type="submit" color="primary">
                                            Güncelle
                                        </Button>
                                    </div>
                                </CardHeader>

                                <Divider />

                                <CardBody>
                                    <div className="flex">
                                        <div className="p-4 w-full">
                                            <div className="w-full mb-2 flex flex-row">
                                                <Input
                                                    type="text"
                                                    label="İsim"
                                                    name="name"
                                                    value={item.name}
                                                    className="mr-2"
                                                    onChange={(e: any) => setItem({ ...item, name: e.target.value })}
                                                />
                                                <Input
                                                    type="text"
                                                    label="E-mail"
                                                    name="email"
                                                    value={item.email}
                                                    className="mr-2"
                                                    onChange={(e: any) => setItem({ ...item, email: e.target.value })}
                                                />
                                                <Input
                                                    type="text"
                                                    label="Telefon Numarası"
                                                    name="phone"
                                                    value={item.phone}
                                                    onChange={(e: any) => setItem({ ...item, phone: e.target.value })}
                                                />
                                            </div>
                                            <div className="w-full mb-2 flex flex-row">
                                                <Input
                                                    type="text"
                                                    label="Banka Hesap Adı"
                                                    name="bankAccountName"
                                                    value={item.bankAccountName}
                                                    className="mr-2"
                                                    onChange={(e: any) => setItem({ ...item, bankAccountName: e.target.value })}
                                                />
                                                <Input
                                                    type="text"
                                                    label="IBAN"
                                                    name="iban"
                                                    value={item.iban}
                                                    className="mr-2"
                                                    onChange={(e: any) => setItem({ ...item, iban: e.target.value })}
                                                />
                                            </div>
                                            <div className="w-full mb-2 flex flex-row">
                                                <Input
                                                    type="text"
                                                    label="Vergi Dairesi"
                                                    name="taxOffice"
                                                    value={item.taxOffice}
                                                    className="mr-2"
                                                    onChange={(e: any) => setItem({ ...item, taxOffice: e.target.value })}
                                                />
                                                <Input
                                                    type="text"
                                                    label="Vergi Numarası"
                                                    name="taxNumber"
                                                    value={item.taxNumber}
                                                    className="mr-2"
                                                    onChange={(e: any) => setItem({ ...item, taxNumber: e.target.value })}
                                                />
                                                <Input
                                                    type="text"
                                                    label="Posta Kodu"
                                                    name="zipCode"
                                                    value={item.zipCode}
                                                    onChange={(e: any) => setItem({ ...item, zipCode: e.target.value })}
                                                />
                                            </div>
                                            <Textarea
                                                label="Adres"
                                                name="address"
                                                value={item.address}
                                                className="mb-2"
                                                onChange={(e: any) => setItem({ ...item, address: e.target.value })}
                                            />
                                            <Input
                                                type="text"
                                                label="Bir Fatura Token"
                                                name="invoiceToken"
                                                value={item.invoiceToken}
                                                className="mb-2"
                                                onChange={(e: any) => setItem({ ...item, invoiceToken: e.target.value })}
                                            />
                                            <div className="w-full mb-2 flex flex-row">
                                                
                                                <Select
                                                    label="Şehir"
                                                    placeholder="Bir seçim yapın..."
                                                    defaultSelectedKeys={item.cityId ? [item.cityId?.toString()] : []}
                                                    onChange={(e: any) => setItem({ ...item, cityId: parseInt(e.target.value) })}
                                                >
                                                    {cities.map((city, index) => {
                                                        return (
                                                            <SelectItem key={city.id} value={index}>
                                                                {city.title}
                                                            </SelectItem>
                                                        )
                                                    })}
                                                </Select>
                                            </div>
                                            
                                        </div>
                                        <CardBody>
                                    <Input label="İsim" value={item.name} onChange={(e) => setItem({ ...item, name: e.target.value })} />
                                    <Textarea label="Adres" value={item.address} onChange={(e) => setItem({ ...item, address: e.target.value })} />
                                    <Typography variant="h6">Modüller</Typography>
                                    {moduleList.map((mod, index) => (
                                        <div key={index}>
                                            <input
                                                type="checkbox"
                                                checked={selectedModules.includes(index)}
                                                onChange={() => handleModuleChange(index)}
                                            /> {mod}
                                        </div>
                                    ))}
                                </CardBody>
                                    </div>
                                </CardBody>
                            </form>
                        </Card>
                    </Grid>
                </Grid>
            </Container>
            <Footer />
        </>
    );
};

EditStagePage.getLayout = (page: React.ReactNode) => <SidebarLayout>{page}</SidebarLayout>;

export const getServerSideProps = authMiddleware(async (_context: any) => {
    return {
        props: {},
    };
});

export default EditStagePage;
