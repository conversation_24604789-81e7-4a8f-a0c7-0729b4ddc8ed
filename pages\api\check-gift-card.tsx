import { PrismaClient } from '@prisma/client';

const prisma = new PrismaClient();

export default async function handler(req, res) {
    if (req.method !== 'GET') {
        return res.status(405).json({ message: 'Method not allowed' });
    }

    const { code, amount } = req.query;

    try {
        const giftCard: any = await prisma.giftCard.findFirst({
            where: {
                code,
                status: true
            }
        });

        // Close the Prisma client connection
        await prisma.$disconnect();

        if (!giftCard) {
            return res.status(404).json({ message: 'Hediye kartı bulunamadı' });
        }

        if (giftCard.value < amount) {
            return res.status(400).json({ message: '<PERSON><PERSON><PERSON> bakiye' });
        }

        res.status(200).json({ result: true, message: `${amount} ₺ kullanıma uygun. Kullanmak istiyor musunuz?` });
    } catch (error) {
        console.error(error);
        // Close the Prisma client connection
        await prisma.$disconnect();
        res.status(500).json({ message: 'Internal server error' });
    }
}
