import React, { useState, useEffect } from 'react';
import Head from 'next/head';
import { useRouter } from 'next/router';
import { authMiddleware } from 'middleware';
import Swal from 'sweetalert2';
import { Container, Grid, Typography, CircularProgress } from '@mui/material';
import { Image, Card, CardBody, CardHeader, Button, Input, Divider, Accordion, AccordionItem } from '@nextui-org/react';
import { Select, SelectItem } from "@nextui-org/select";
import SidebarLayout from '@/layouts/SidebarLayout';
import PageTitleWrapper from '@/components/PageTitleWrapper';
import Footer from '@/components/Footer';
import ArrowBackIcon from '@mui/icons-material/ArrowBack';
import Link from 'next/link';

const EditMonthlyShamanPage = () => {
    const router = useRouter();
    const [item, setItem]: any = useState({});
    const { id } = router.query;
    const [loading, setLoading] = useState(true);
    const [imageLoading, setImageLoading] = useState(false);
    const [currentTimestamp, setCurrentTimestamp] = useState(new Date().getTime());
    const [dropdownConcerts, setDropdownConcerts] = useState([]);
    const dropdownItems = {
        product: "Ürün",
        hollyPoints: "Holly Puan",
        ticket: "Bilet"
    };

    const loadList = async () => {
        const url = `/api/monthly-shaman?id=${id}`;

        try {
            const res = await fetch(url);

            if (res.status === 200) {
                const json = await res.json();
                json.results[0].startDate = new Date(json.results[0]?.startDate);
                json.results[0].endDate = new Date(json.results[0]?.endDate);
                setItem(json.results[0]);
            } else {
                Swal.fire({
                    icon: 'error',
                    title: 'Hata',
                    text: 'Bir sorun meydana geldi!',
                });
            }
        } catch (error) {
            console.error(error);
        } finally {
            setLoading(false);
        }
    };

    const loadConcerts = async () => {
        const url = `/api/concerts`;

        try {
            const res = await fetch(url);

            if (res.status === 200) {
                const json = await res.json();
                setDropdownConcerts(json.results);
            } else {
                Swal.fire({
                    icon: 'error',
                    title: 'Hata',
                    text: 'Bir sorun meydana geldi!',
                });
            }
        } catch (error) {
            console.error(error);
        } finally {
            setLoading(false);
        }
    };

    useEffect(() => {
        loadList();
        loadConcerts();
    }, []);

    const handleImageChange = async (event, image) => {
        const file = event.target.files[0];
        const filePath = image.split('.');

        if (file) {
            if (!file.type.startsWith('image/')) {
                Swal.fire({
                    icon: 'error',
                    title: 'Hata',
                    text: 'Seçilen format desteklenmiyor!',
                });
                return;
            }
            setImageLoading(true);
            const formData = new FormData();
            formData.append('image', file);
            formData.append('fileName', filePath[0]);

            try {
                const response = await fetch(`https://api.hollystone.com.tr/api/functions/upload`, {
                    method: 'POST',
                    headers: {
                        'Authorization': `Bearer ${localStorage.getItem('token')}`
                    },
                    body: formData,
                });

                if (response.ok) {
                    const data = await response.json();

                    if (data.type == "success") {
                        Swal.fire({
                            icon: 'success',
                            title: 'Başarılı',
                            text: 'Resim güncellendi!',
                        });
                        setCurrentTimestamp(new Date().getTime());
                    } else {
                        Swal.fire({
                            icon: 'error',
                            title: 'Hata',
                            text: data.error,
                        });
                    }
                } else {
                    Swal.fire({
                        icon: 'error',
                        title: 'Hata',
                        text: 'Bir sorun meydana geldi!',
                    });
                }
            } catch (error) {
                Swal.fire({
                    icon: 'error',
                    title: 'Hata',
                    text: 'Bir sorun meydana geldi!',
                });
            }
            setImageLoading(false);
        }
    };

    const handleDelete = async () => {
        const shouldDelete = await Swal.fire({
            title: 'Dikkat!',
            text: 'Bu kaydı silmek istediğine gerçekten emin misin?',
            icon: 'warning',
            showCancelButton: true,
            confirmButtonText: 'Evet, sil!',
            cancelButtonText: 'Vazgeç',
        });

        if (shouldDelete.isConfirmed) {
            try {
                const response = await fetch(`/api/monthly-shaman/delete`, {
                    method: 'DELETE',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({ id: id }),
                });

                if (response.ok) {
                    router.push('/monthly-shaman');
                    Swal.fire({
                        icon: 'success',
                        title: 'Başarılı',
                        text: 'Kayıt silindi!',
                    });
                } else {
                    Swal.fire({
                        icon: 'error',
                        title: 'Hata',
                        text: 'Bir sorun meydana geldi!',
                    });
                }
            } catch (error) {
                console.error('An error occurred:', error);
            }
        }
    };

    const handleUpdate = async (event) => {
        event.preventDefault();
        try {
            const response = await fetch(`/api/monthly-shaman/update`, {
                method: 'PUT',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify(item),
            });

            if (response.ok) {
                await loadList();
                Swal.fire({
                    icon: 'success',
                    title: 'Başarılı',
                    text: 'Kayıt güncellendi!',
                });
            } else {
                Swal.fire({
                    icon: 'error',
                    title: 'Hata',
                    text: 'Bir sorun meydana geldi!',
                });
            }
        } catch (error) {
            console.error('An error occurred:', error);
        }
    }

    if (loading) {
        return "Loading";
    }

    return (
        <>
            <Head>
                <title>Şaman Aylık Ödül - Düzenle</title>
            </Head>
            <PageTitleWrapper>
                <Grid container alignItems="center">
                    <Grid item>
                        <Typography variant="h3" component="h3" gutterBottom>
                            Şaman Aylık Ödül Düzenle
                        </Typography>
                    </Grid>
                </Grid>
            </PageTitleWrapper>
            <Container maxWidth="lg">
                <Grid
                    container
                    direction="row"
                    justifyContent="center"
                    alignItems="stretch"
                    spacing={4}
                >
                    <Grid item xs={12}>
                        <Card
                            isBlurred
                            className="border-none bg-white dark:bg-default-100/50"
                            shadow="sm"
                        >
                            <form onSubmit={handleUpdate}>
                                <CardHeader className="flex justify-between gap-3">
                                    <div>
                                        <Link href="/monthly-shaman">
                                            <Button type="button" color="default" startContent={<ArrowBackIcon />}>
                                                Aylık Şaman
                                            </Button>
                                        </Link>
                                    </div>
                                    <div>Ödül Bilgileri</div>
                                    <div className="flex-end">
                                        <Button type="button" onClick={handleDelete} color="danger" className="mr-2">
                                            Sil
                                        </Button>
                                        <Button type="submit" color="primary">
                                            Güncelle
                                        </Button>
                                    </div>
                                </CardHeader>

                                <Divider />

                                <CardBody>
                                    <div className="flex">
                                        <div className="relative w-1/2" style={{
                                            minWidth: 400
                                        }}>
                                            <Accordion defaultExpandedKeys={["0"]}>
                                                <AccordionItem
                                                    key="0"
                                                    aria-label="Ürün Resim"
                                                    title="Ürün Resim"
                                                >
                                                    <label className="block cursor-pointer">
                                                        <div className="relative">
                                                            <Image
                                                                width={400}
                                                                height={400}
                                                                radius="none"
                                                                loading="eager"
                                                                style={{
                                                                    height: "100%"
                                                                }}
                                                                src={`https://api.hollystone.com.tr/resources/images/${item.prizeImage}?t=${currentTimestamp}`}
                                                                fallbackSrc="https://via.placeholder.com/400x400.png?text="
                                                            />
                                                            {imageLoading ? (
                                                                <CircularProgress
                                                                    size={48}
                                                                    style={{
                                                                        position: 'absolute',
                                                                        top: '40%',
                                                                        left: '40%',
                                                                        transform: 'translate(-40%, -40%)',
                                                                        zIndex: 999,
                                                                    }}
                                                                />
                                                            ) : null}
                                                        </div>
                                                        <Input
                                                            type="file"
                                                            label="Resim"
                                                            name="image"
                                                            className="hidden"
                                                            accept="image/*"
                                                            onChange={(e: any) => { handleImageChange(e, item.prizeImage) }}
                                                        />
                                                    </label>
                                                </AccordionItem>
                                                <AccordionItem
                                                    key="1"
                                                    aria-label="Sponsor Resim"
                                                    title="Sponsor Resim"
                                                >
                                                    <label className="block cursor-pointer">
                                                        <div className="relative">
                                                            <Image
                                                                width={400}
                                                                height={400}
                                                                radius="none"
                                                                loading="eager"
                                                                style={{
                                                                    height: "100%"
                                                                }}
                                                                src={`https://api.hollystone.com.tr/resources/images/${item.prizeSponsorImage}?t=${currentTimestamp}`}
                                                                fallbackSrc="https://via.placeholder.com/400x400.png?text="
                                                            />
                                                            {imageLoading ? (
                                                                <CircularProgress
                                                                    size={48}
                                                                    style={{
                                                                        position: 'absolute',
                                                                        top: '40%',
                                                                        left: '40%',
                                                                        transform: 'translate(-40%, -40%)',
                                                                        zIndex: 999,
                                                                    }}
                                                                />
                                                            ) : null}
                                                        </div>
                                                        <Input
                                                            type="file"
                                                            label="Resim"
                                                            name="image"
                                                            className="hidden"
                                                            accept="image/*"
                                                            onChange={(e: any) => { handleImageChange(e, item.prizeSponsorImage) }}
                                                        />
                                                    </label>
                                                </AccordionItem>
                                            </Accordion>
                                        </div>
                                        <div className="p-4 w-full">
                                            <Select
                                                label="Ödül Tipi"
                                                placeholder="Bir seçim yapın..."
                                                defaultSelectedKeys={[item.prizeType]}
                                                onChange={(e: any) => setItem({ ...item, prizeType: e.target.value })}
                                                className="mb-2"
                                            >
                                                {Object.keys(dropdownItems).map((key) => (
                                                    <SelectItem key={key} value={key}>
                                                        {dropdownItems[key]}
                                                    </SelectItem>
                                                ))}
                                            </Select>

                                            {
                                                item.prizeType === 'ticket' ? (
                                                    <Select
                                                        label="Ödül"
                                                        placeholder="Bir seçim yapın..."
                                                        defaultSelectedKeys={[item.prize]}
                                                        className="mb-2"
                                                        onChange={(e: any) => setItem({ ...item, prize: parseInt(e.target.value) })}
                                                    >
                                                        {Object.keys(dropdownConcerts).map((key) => (
                                                            <SelectItem key={dropdownConcerts[key].id} value={key}>
                                                                {dropdownConcerts[key].name}
                                                            </SelectItem>
                                                        ))}
                                                    </Select>
                                                )
                                                    : (
                                                        <Input
                                                            type="text"
                                                            label="Ödül"
                                                            name="prize"
                                                            value={item.prize}
                                                            onChange={(e: any) => setItem({ ...item, prize: e.target.value })}
                                                            className="mb-2"
                                                        />
                                                    )
                                            }
                                            <div className="w-full mb-2 flex flex-row">
                                                <Input
                                                    type="date"
                                                    label="Başlangıç Tarihi"
                                                    name="startDate"
                                                    className="mr-2"
                                                    value={item.startDate ? item.startDate.toISOString().split('T')[0] : ''}
                                                    onChange={(e: React.ChangeEvent<HTMLInputElement>) => {
                                                        const selectedDate = new Date(e.target.value + 'T00:00:00Z');
                                                        setItem({ ...item, startDate: selectedDate });
                                                    }}
                                                />
                                                <Input
                                                    type="date"
                                                    label="Bitiş Tarihi"
                                                    name="endDate"
                                                    value={item.endDate ? item.endDate.toISOString().split('T')[0] : ''}
                                                    onChange={(e: React.ChangeEvent<HTMLInputElement>) => {
                                                        const selectedDate = new Date(e.target.value + 'T00:00:00Z');
                                                        setItem({ ...item, endDate: selectedDate });
                                                    }}
                                                />
                                            </div>
                                        </div>
                                    </div>
                                </CardBody>
                            </form>
                        </Card>
                    </Grid>
                </Grid>
            </Container>
            <Footer />
        </>
    );
};

EditMonthlyShamanPage.getLayout = (page: React.ReactNode) => <SidebarLayout>{page}</SidebarLayout>;

export const getServerSideProps = authMiddleware(async (_context: any) => {
    return {
        props: {},
    };
});

export default EditMonthlyShamanPage;
