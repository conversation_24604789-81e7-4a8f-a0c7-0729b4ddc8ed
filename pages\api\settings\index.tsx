import { PrismaClient } from '@prisma/client';

const prisma = new PrismaClient();

export default async function handler(req, res) {
  if (req.method !== 'GET') {
    return res.status(405).json({ message: 'Method not allowed' });
  }

  try {
    const settings = await prisma.setting.findMany({
      where: {
        id: 1,
      },
      orderBy: {
          createdAt: 'desc'
      }
    });

    // Close the Prisma client connection
    await prisma.$disconnect();

    res.status(200).json({ results: settings });
  } catch (error) {
    console.error(error);
    // Close the Prisma client connection
    await prisma.$disconnect();
    res.status(500).json({ message: 'Internal server error' });
  }
}
