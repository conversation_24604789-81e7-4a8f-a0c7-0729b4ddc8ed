import { PrismaClient } from '@prisma/client';

const prisma = new PrismaClient();

export default async function handler(req, res) {
  if (req.method !== 'GET') {
    return res.status(405).json({ message: 'Method not allowed' });
  }

  const { id, page = 1, pageSize = 10, filter = "" } = req.query;

  let where = {};

  if (id) {
    where = {
      id: parseInt(id),
    };
  } else {
    where = {
      AND: [
        {
          OR: [
            { name: { contains: filter } },
          ],
        },
        { isDeleted: false },
      ],
    };
  }

  try {
    const vendors = await prisma.vendor.findMany({
      where,
      orderBy: {
          createdAt: 'desc'
      },
      skip: (parseInt(page) - 1) * parseInt(pageSize),
      take: parseInt(pageSize),
    });

    const totalCount = await prisma.vendor.count({ where });
    // Close the Prisma client connection
    await prisma.$disconnect();

    res.status(200).json({ results: vendors, totalCount });
  } catch (error) {
    console.error(error);
    // Close the Prisma client connection
    await prisma.$disconnect();
    res.status(500).json({ message: 'Internal server error' });
  }
}
