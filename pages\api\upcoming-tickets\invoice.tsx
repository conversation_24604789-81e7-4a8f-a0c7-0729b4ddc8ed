import { PrismaClient } from '@prisma/client';

const prisma = new PrismaClient();

export default async function handler(req, res) {
    if (req.method === 'POST') {
        const { id } = req.body;

        try {
            const allTickets: any = await prisma.userTicket.findMany({
                where: {
                    ticket: {
                        concertId: parseInt(id),
                    }
                }
            });

            const updatedTickets = await prisma.invoice.updateMany({
                where: {
                    orderId: {
                        in: allTickets.map((ticket: any) => ticket.id)
                    },
                    orderType: false,
                    orderStatusId: 0
                },
                data: {
                    orderStatusId: 1
                },
            });

            res.status(200).json({ message: 'Fatura kesildi!', updatedItem: updatedTickets });
        } catch (error) {
            console.error(error);
            res.status(500).json({ message: 'Internal server error', error });
        }
    } else {
        res.status(405).json({ message: 'Method not allowed' });
    }
}
