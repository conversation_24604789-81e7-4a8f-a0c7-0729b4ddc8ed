import { PrismaClient } from '@prisma/client';

const prisma = new PrismaClient();

export default async function handler(req, res) {
    if (req.method !== 'GET') {
        return res.status(405).json({ message: 'Method not allowed' });
    }

    const { id, status = 1, page = 1, pageSize = 10, filter = "" } = req.query;

    const where = {
        ticket: {
            concertId: parseInt(id),
        },
        user: {
            OR: [
                { firstName: { contains: filter } },
                { lastName: { contains: filter } },
                { phoneNumber: { contains: filter } },
                { email: { contains: filter } },
            ]
        },
        payment: true,
        status: parseInt(status)
    }

    try {
        const allTickets: any = await prisma.userTicket.findMany({
            where: {
                ticket: {
                    concertId: parseInt(id),
                }
            }
        });
        const allTicketsCount = allTickets.length;

        let userTickets: any = await prisma.userTicket.findMany({
            where,
            orderBy: {
                createdAt: 'desc'
            },
            include: {
                ticket: true,
                user: true
            },
            skip: (parseInt(page) - 1) * parseInt(pageSize),
            take: parseInt(pageSize),
        });

        const invoiceCount = await prisma.invoice.count({
            where: {
                orderId: {
                    in: allTickets.map((ticket: any) => ticket.id)
                },
                orderType: false,
                orderStatusId: 1
            }
        });

        userTickets.map((item: any, index: number) => {
            if (item.bondedTo != null) {
                const boundedTicket = userTickets.find((ticket: any) => ticket.id === item.bondedTo);
                const boundedIndex = userTickets.findIndex((ticket: any) => ticket.id === item.bondedTo);
                userTickets[index].boundedQr = boundedTicket.qrCode;
                userTickets.splice(boundedIndex, 1);
            } else {
                userTickets[index].boundedQr = null;
            }
        })

        const totalCount = await prisma.userTicket.count({ where });
        const vendor = await prisma.$queryRaw`SELECT v.name FROM concerts AS c INNER JOIN vendors AS v ON c.vendorId = v.id WHERE c.id = ${id}`;
        const totalSum = await prisma.$queryRaw`SELECT SUM(ut.paidPrice) as totalPrice FROM user_tickets ut INNER JOIN concert_tickets ct ON ct.id = ut.ticketId WHERE ct.concertId = ${id} AND status != 0`;
        const totalRefund = await prisma.$queryRaw`SELECT SUM(ut.paidPrice) as totalPrice, COUNT(ut.paidPrice) as totalCount FROM user_tickets ut INNER JOIN concert_tickets ct ON ct.id = ut.ticketId WHERE ct.concertId = ${id} AND status IN (3, 4)`;
        const totalRevenue = await prisma.$queryRaw`SELECT SUM(ut.paidPrice) as totalPrice, COUNT(ut.paidPrice) as totalCount FROM user_tickets ut INNER JOIN concert_tickets ct ON ct.id = ut.ticketId WHERE ct.concertId = ${id} AND status IN (1, 2)`;

        // Close the Prisma client connection
        await prisma.$disconnect();

        const invoiceStatus = allTicketsCount == invoiceCount ? true : false;

        res.status(200).json({
            results: userTickets,
            totalCount,
            invoiceStatus,
            totalSum: parseFloat(totalSum[0].totalPrice),
            totalRefund: parseFloat(totalRefund[0].totalPrice),
            totalRefundCount: parseInt(totalRefund[0].totalCount),
            totalRevenue: parseFloat(totalRevenue[0].totalPrice),
            totalRevenueCount: parseInt(totalRevenue[0].totalCount),
            vendor: vendor[0].name,
        });
    } catch (error) {
        console.error(error);
        // Close the Prisma client connection even in case of an error
        await prisma.$disconnect();
        res.status(500).json({ message: 'Internal server error' });
    }
}
