import { PrismaClient } from '@prisma/client';

const prisma = new PrismaClient();

const generateRandomReferralCode = (length) => {
    const characters = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789';
    let code = '';
    for (let i = 0; i < length; i++) {
        const randomIndex = Math.floor(Math.random() * characters.length);
        code += characters.charAt(randomIndex);
    }
    return code;
};

export default async function handler(req, res) {
    if (req.method === 'POST') {
        const { ticketId, firstName, lastName, email, phoneNumber, dateOfBirth } = req.body;

        try {
            const upsertUser = await prisma.user.upsert({
                where: {
                    phoneNumber,
                    email,
                },
                update: {},
                create: {
                    cityId: 1,
                    firstName,
                    lastName,
                    phoneNumber,
                    email,
                    password: "$2a$12$x7Nje0wdpEoDgME1tisTten4V.YVjT983LYPWeHCCF7klecxhsrcW",
                    dateOfBirth,
                    referralCode: generateRandomReferralCode(8),
                    image: "defaultUser.webp",
                    hollyPoints: 0,
                    status: true,
                    isDeleted: false
                },
            })

            const ticket = await prisma.userTicket.create({
                data: {
                    ticketId: parseInt(ticketId),
                    userId: upsertUser.id,
                    paidPrice: 0,
                    qrCode: "HLSTN123123123",
                    status: 1,
                    payment: true
                }
            })

            res.status(201).json({ message: 'Bilet eklendi!', createdItem: ticket });
        } catch (error) {
            console.error(error);
            res.status(500).json({ message: 'Internal server error', error });
        }
    } else {
        res.status(405).json({ message: 'Method not allowed' });
    }
}
