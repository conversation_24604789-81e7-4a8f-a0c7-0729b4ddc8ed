import { PrismaClient } from '@prisma/client';

const prisma = new PrismaClient();

export default async function handler(req, res) {
    if (req.method !== 'GET') {
        return res.status(405).json({ message: 'Method not allowed' });
    }

    try {
        const vendors = await prisma.$queryRaw`SELECT vendorName, SUM(totalIncome) AS combinedIncome FROM ( SELECT v.name AS vendorName, SUM(o.price) AS totalIncome FROM orders o INNER JOIN vendors v ON v.id = o.vendorId WHERE DATE_FORMAT(o.createdAt, '%Y-%m') = DATE_FORMAT(CURRENT_DATE, '%Y-%m') AND o.payment = true AND o.status IN (1, 2, 3) GROUP BY v.name UNION ALL SELECT v.name AS vendorName, SUM(paidPrice) AS totalIncome FROM user_tickets ut INNER JOIN concert_tickets ct ON ct.id = ut.ticketId INNER JOIN concerts c ON c.id = ct.concertId INNER JOIN vendors v ON v.id = c.vendorId WHERE DATE_FORMAT(c.date, '%Y-%m') = DATE_FORMAT(CURRENT_DATE, '%Y-%m') AND ut.payment = true AND ut.status IN (1, 2) GROUP BY v.name UNION ALL SELECT name AS vendorName, 0 AS totalIncome FROM vendors ) AS combinedData GROUP BY vendorName ORDER BY combinedIncome DESC;`;

        // Close the Prisma client connection
        await prisma.$disconnect();

        res.status(200).json({ vendors });
    } catch (error) {
        console.error(error);
        // Close the Prisma client connection
        await prisma.$disconnect();
        res.status(500).json({ message: 'Internal server error' });
    }
}
