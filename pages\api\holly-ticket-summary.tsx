import { PrismaClient } from '@prisma/client';
import { startOfMonth, endOfMonth } from 'date-fns';

const prisma = new PrismaClient();

export default async function handler(req, res) {
    if (req.method !== 'GET') {
        return res.status(405).json({ message: 'Method not allowed' });
    }

    const { month } = req.query;

    const currentDate = new Date();
    currentDate.setMonth(month);
    currentDate.setHours(0);
    currentDate.setMinutes(0);
    currentDate.setSeconds(0);
    currentDate.setMilliseconds(0);
    
    const startDate = startOfMonth(currentDate);
    const endDate = endOfMonth(currentDate);

    if (!month || isNaN(month)) {
        return res.status(400).json({ message: 'Invalid month parameter' });
    }

    try {
        const totalIncome = await prisma.userTicket.aggregate({
            _sum: {
                paidPrice: true
            },
            _count: {
                paidPrice: true
            },
            where: {
                ticket: {
                    concert: {
                        date: {
                            gte: startDate,
                            lt: endDate,
                        },
                    },
                },
                payment: true,
                status: { in: [1, 2] },
            },
        });

        const totalRefund = await prisma.userTicket.aggregate({
            _sum: {
                paidPrice: true,
            },
            _count: {
                paidPrice: true,
            },
            where: {
                ticket: {
                    concert: {
                        date: {
                            gte: startDate,
                            lte: endDate,
                        },
                    },
                },
                payment: true,
                status: {
                    in: [3, 4],
                },
            },
        });

        const total = await prisma.userTicket.aggregate({
            _sum: {
                paidPrice: true,
            },
            _count: {
                paidPrice: true,
            },
            where: {
                ticket: {
                    concert: {
                        date: {
                            gte: startDate,
                            lte: endDate,
                        },
                    },
                },
                payment: true,
            },
        });

        const totalIncomeVendors = await prisma.$queryRaw`
        SELECT
            vendor.name as vendorName,
            SUM(user_ticket.paidPrice) as totalIncome
        FROM
            user_tickets as user_ticket
        JOIN
            concert_tickets as concert_ticket ON user_ticket.ticketId = concert_ticket.id
        JOIN
            concerts as concert ON concert_ticket.concertId = concert.id
        JOIN
            vendors as vendor ON concert.vendorId = vendor.id
        WHERE
            user_ticket.payment = true
            AND concert.date >= ${startDate}
            AND concert.date <= ${endDate}
        GROUP BY
            concert.vendorId
        `;

        const bestSellers = await prisma.$queryRaw`
        WITH RankedTickets AS (
            SELECT
                c.name,
                c.image,
                v.name as vendorName,
                COUNT(ut.id) as salesCount,
                ROW_NUMBER() OVER (PARTITION BY v.id ORDER BY COUNT(ut.id) DESC) as rank
            FROM
                user_tickets ut
            INNER JOIN
                concert_tickets ct ON ct.id = ut.ticketId
            INNER JOIN
                concerts c ON c.id = ct.concertId
            INNER JOIN
                vendors v ON v.id = c.vendorId
            WHERE
                c.date >= ${startDate}
                AND c.date <= ${endDate}
                AND ut.payment = true
                AND ut.status IN (1, 2)
            GROUP BY
                c.id, c.name, c.image, c.vendorId
        )
        SELECT
            name,
            image,
            vendorName
        FROM
            RankedTickets
        WHERE
            RankedTickets.rank = 1;
        `;

        // Close the Prisma client connection
        await prisma.$disconnect();

        res.status(200).json({ totalIncome: totalIncome._sum.paidPrice, totalIncomeCount: totalIncome._count.paidPrice, totalRefund: totalRefund._sum.paidPrice, totalRefundCount: totalRefund._count.paidPrice, total: total._sum.paidPrice, totalCount: total._count.paidPrice, bestSellers, totalIncomeVendors });
    } catch (error) {
        console.error(error);
        // Close the Prisma client connection
        await prisma.$disconnect();
        res.status(500).json({ message: 'Internal server error' });
    }
}
