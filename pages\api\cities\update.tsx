import { PrismaClient } from '@prisma/client';

const prisma = new PrismaClient();

export default async function handler(req, res) {
    if (req.method === 'PUT') {
        const { id, title, rank, abbreviation } = req.body;

        try {
            const updatedCity = await prisma.city.update({
                where: {
                    id: parseInt(id),
                },
                data: {
                    title,
                    rank,
                    abbreviation
                },
            });

            res.status(200).json({ message: '<PERSON><PERSON><PERSON> g<PERSON>llen<PERSON>!', updatedItem: updatedCity });
        } catch (error) {
            console.error(error);
            res.status(500).json({ message: 'Internal server error', error });
        }
    } else {
        res.status(405).json({ message: 'Method not allowed' });
    }
}
