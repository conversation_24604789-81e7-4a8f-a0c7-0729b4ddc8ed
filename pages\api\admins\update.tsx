import { PrismaClient } from '@prisma/client';
import bcrypt from "bcrypt";

const prisma = new PrismaClient();

export default async function handler(req, res) {
    if (req.method === 'PUT') {
        const { id, vendorId, username, newPassword, email, phone, identityNumber, authorityLevel } = req.body;

        try {
            let hashedPassword = undefined;

            if (newPassword) {
                if (newPassword.length < 8) {
                    return res.status(400).json({ message: 'Şifre en az 8 karakter olmalıdır!' });
                } else {
                    hashedPassword = await bcrypt.hash(newPassword, 10);
                }
            }

            const updatedAdmin = await prisma.admin.update({
                where: {
                    id: parseInt(id),
                },
                data: {
                    vendorId,
                    username,
                    email,
                    phone,
                    identityNumber,
                    authorityLevel
                },
            });

            if (hashedPassword) {
                await prisma.admin.update({
                    where: {
                        id: parseInt(id),
                    },
                    data: {
                        password: hashedPassword
                    },
                });
            }

            const adminCount = await prisma.admin.count({ where: { authorityLevel: { equals: 1 } } });
            if (adminCount === 0) {
                await prisma.admin.update({
                    where: {
                        id: parseInt(id),
                    },
                    data: {
                        authorityLevel: 1
                    },
                });
            }

            res.status(200).json({ message: 'Admin güncellendi!', updatedItem: updatedAdmin });
        } catch (error) {
            console.error(error);
            res.status(500).json({ message: 'Internal server error', error });
        }
    } else {
        res.status(405).json({ message: 'Method not allowed' });
    }
}
