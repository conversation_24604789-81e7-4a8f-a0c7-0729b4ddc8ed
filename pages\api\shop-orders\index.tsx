import { PrismaClient } from '@prisma/client';

const prisma = new PrismaClient();

export default async function handler(req, res) {
    if (req.method !== 'GET') {
        return res.status(405).json({ message: 'Method not allowed' });
    }

    const { id, vendorId = 0 } = req.query;

    let where = {};

    if (id) {
        where = {
            payment: true,
            id: parseInt(id),
        };
    } else {
        where = {
            payment: true,
            vendorId: parseInt(vendorId)
        }
    }

    try {
        const orders = await prisma.shopOrder.findMany({
            where,
            orderBy: {
                createdAt: 'desc'
            },
            include: {
                user: {
                    select: {
                        firstName: true,
                        lastName: true,
                    },
                },
                address: {
                    select: {
                        title: true,
                        city: true,
                        district: true,
                        neighborhood: true,
                        postalCode: true,
                        fullAddress: true,
                    },
                },
                OrderItem: {
                    include: {
                        product: true,
                    },
                },
            },
        });

        const totalCount = await prisma.shopOrder.count({ where });

        const formattedOrders = orders.map(order => ({
            ...order,
            userName: `${order.user.firstName} ${order.user.lastName}`,
        }));
        // Close the Prisma client connection
        await prisma.$disconnect();

        res.status(200).json({ results: formattedOrders, totalCount });
    } catch (error) {
        console.error(error);
        // Close the Prisma client connection
        await prisma.$disconnect();
        res.status(500).json({ message: 'Internal server error' });
    }
}
