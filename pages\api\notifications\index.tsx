import { PrismaClient } from '@prisma/client';
import axios from 'axios';

const prisma = new PrismaClient();

async function sendOneSignalNotification(playerId, title, content) {
  const appId = '8af436eb-c156-478b-8b68-29a49aeb369e'; // OneSignal App ID
  const apiKey = '************************************************'; // OneSignal REST API Key

  try {
    const response = await axios.post(
      'https://onesignal.com/api/v1/notifications',
      {
        app_id: appId,
        include_player_ids: [playerId],
        headings: { en: title },
        contents: { en: content },
      },
      {
        headers: {
          'Content-Type': 'application/json',
          Authorization: `Basic ${apiKey}`,
        },
      }
    );
    console.log('Notification sent successfully:', response.data);
  } catch (error) {
    console.error(
      'Error sending OneSignal notification:',
      error.response?.data || error.message
    );
  }
}

export default async function handler(req, res) {
  if (req.method === 'GET') {
    const { id, page = 1, pageSize = 10, filter = '' } = req.query;

    let where = {};

    if (id) {
      where = {
        id: parseInt(id),
      };
    } else {
      where = {
        AND: [
          {
            OR: [
              { title: { contains: filter } },
              { content: { contains: filter } },
              { type: { contains: filter } },
            ],
          },
        ],
      };
    }

    try {
      // Bildirimleri al
      const notifications = await prisma.notification.findMany({
        where,
        include: {
          user: {
            select: {
              id: true,
              firstName: true,
              lastName: true,
              email: true,
            },
          },
        },
        orderBy: {
          createdAt: 'desc',
        },
        skip: (parseInt(page) - 1) * parseInt(pageSize),
        take: parseInt(pageSize),
      });

      const totalCount = await prisma.notification.count({ where });

      // Kullanıcı listesini al
      const users = await prisma.user.findMany({
        where: { isDeleted: false },
        select: {
          id: true,
          firstName: true,
          lastName: true,
          email: true,
          push_id: true,
        },
      });

      res.status(200).json({ notifications, totalCount, users });
    } catch (error) {
      console.error('Error fetching data:', error);
      res.status(500).json({ message: 'Internal server error' });
    }
  } else if (req.method === 'POST') {
    const { userIds, title, content, type, innerType, target } = req.body;

    if (!title || !content || !type) {
      return res
        .status(400)
        .json({ message: 'Title, content, and type are required' });
    }

    try {
      if (userIds && userIds.length > 0) {
        // Seçilen kullanıcılara bildirim gönder
        const notifications = [];

        for (const userId of userIds) {
          const user = await prisma.user.findUnique({
            where: { id: userId },
            select: { push_id: true },
          });

          if (user?.push_id) {
            await sendOneSignalNotification(user.push_id, title, content);
          }

          notifications.push({
            userId,
            title,
            content,
            type,
            innerType,
            target,
          });
        }

        await prisma.notification.createMany({ data: notifications });
      } else {
        // Tüm kullanıcılara bildirim gönder
        const users = await prisma.user.findMany({
          where: { isDeleted: false },
          select: { id: true, push_id: true },
        });

        const notifications = [];

        for (const user of users) {
          if (user.push_id) {
            await sendOneSignalNotification(user.push_id, title, content);
          }

          notifications.push({
            userId: user.id,
            title,
            content,
            type,
            innerType,
            target,
          });
        }

        await prisma.notification.createMany({ data: notifications });
      }

      res.status(201).json({ message: 'Notification(s) created successfully' });
    } catch (error) {
      console.error('Error creating notifications:', error);
      res.status(500).json({ message: 'Internal server error' });
    }
  } else {
    res.status(405).json({ message: 'Method not allowed' });
  }
}
