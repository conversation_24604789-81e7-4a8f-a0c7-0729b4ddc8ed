import React, { useState, useMemo, useCallback, useEffect } from "react";
import {
    Table,
    TableHeader,
    TableColumn,
    TableBody,
    TableRow,
    TableCell,
    Input,
    Button,
    Pagination,
    Spinner,
    getKeyValue,
    User,
} from "@nextui-org/react";
import { SearchIcon } from "../../src/components/SearchIcon";
import EditIcon from '@mui/icons-material/Edit';
import { withRouter } from 'next/router';
function TicketsTable({ router }) {
    const { id } = router.query;
    const [filterValue, setFilterValue] = useState("");
    const [page, setPage] = useState(1);
    const [total, setTotal] = useState(0);
    const [isLoading, setIsLoading] = useState(true);
    const [items, setItems] = useState([]);

    const rowsPerPage = 10;

    const loadList = async (pageInput: number, filter: string) => {
        let url = `/api/tickets/list?page=${pageInput}&pageSize=${rowsPerPage}&filter=${filter}&id=${id}`;

        try {
            const res = await fetch(url);

            if (res.status === 200) {
                const json = await res.json();

                setTotal(json.totalCount);
                setIsLoading(false);
                setItems(json.results);
            } else {
                setIsLoading(false);
                setItems([]);
            }
        } catch (error) {
            setIsLoading(false);
            setItems([]);
            console.error(error);
            throw error;
        }
    };

    const handleEdit = (rowId) => {
        router.push('/tickets/list?id=' + rowId);
    };

    useEffect(() => {
        loadList(1, filterValue);
    }, []);

    const pages = Math.ceil(total / rowsPerPage);

    const onPaginationChange = useCallback(
        async (pageInput) => {
            setIsLoading(true);
            setPage(pageInput);
            await loadList(pageInput, filterValue);
        },
        []
    );

    const onSearchChange = useCallback(
        (value: string) => {
            if (value) {
                setIsLoading(true);
                setFilterValue(value);
                loadList(page, value);
                setPage(1);
            } else {
                setFilterValue("");
                loadList(page, "");
            }
        },
        []
    );

    const onClear = useCallback(() => {
        setFilterValue("");
        setPage(1);
    }, []);

    const topContent = useMemo(() => {
        return (
            <div className="flex flex-col gap-4">
                <div className="flex justify-between gap-3 items-end">
                    <Input
                        isClearable
                        className="w-full sm:max-w-[44%]"
                        placeholder="Arayın..."
                        startContent={<SearchIcon />}
                        value={filterValue}
                        onClear={() => onClear()}
                        onValueChange={onSearchChange}
                    />
                </div>
            </div>
        );
    }, [
        filterValue,
        onSearchChange,
        onClear,
    ]);

    const renderCell = useCallback((cellValue: any, columnKey: any, rowId: any) => {
        switch (columnKey) {
            case "qrCode":
                return (
                    <a target="_blank" rel="noreferrer" href={`https://api.qrserver.com/v1/create-qr-code/?data=${cellValue}`}>
                        <User name=""
                            avatarProps={{ radius: "none", src: `https://api.qrserver.com/v1/create-qr-code/?data=${cellValue}` }}
                        >
                        </User>
                    </a>
                );
            case "ticket":
                return (
                    cellValue.title
                );
            case "user":
                return (
                    `${cellValue.firstName} ${cellValue.lastName}`
                );
            case "actions":
                return (
                    <Button
                        onClick={() => handleEdit(rowId)}
                        color="primary"
                        startContent={<EditIcon />}
                    >
                        Biletler
                    </Button>
                );
            default:
                return cellValue;
        }
    }, []);

    return (
        <Table
            aria-label="Example table with client async pagination"
            topContent={topContent}
            topContentPlacement="inside"
            bottomContent={
                pages > 0 ? (
                    <div className="flex w-full justify-center">
                        <Pagination
                            isCompact
                            showControls
                            showShadow
                            color="primary"
                            page={page}
                            total={pages}
                            onChange={onPaginationChange}
                        />
                    </div>
                ) : null
            }
            classNames={{
                table: "min-h-[400px]",
            }}
        >
            <TableHeader>
                <TableColumn key="qrCode">QR Kod</TableColumn>
                <TableColumn key="user">Kullanıcı</TableColumn>
                <TableColumn key="ticket">Başlık</TableColumn>
                <TableColumn key="paidPrice">Fiyat</TableColumn>
                <TableColumn key="actions">İşlemler</TableColumn>
            </TableHeader>
            <TableBody
                isLoading={isLoading && !items.length}
                items={items}
                loadingContent={<Spinner />}
                emptyContent={"Kayıt bulunamadı!"}
            >
                {(item) => (
                    <TableRow key={item.id}>
                        {(columnKey) => (
                            <TableCell key={columnKey+item.id}>{renderCell(getKeyValue(item, columnKey), columnKey, item.id)}</TableCell>
                        )}
                    </TableRow>
                )}
            </TableBody>
        </Table>
    );
}

export default withRouter(TicketsTable);
