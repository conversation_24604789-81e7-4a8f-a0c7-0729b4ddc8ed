import React, { useEffect, useState } from 'react';
import Head from 'next/head';
import { authMiddleware } from 'middleware';
import { Container, Grid, Typography, Button  } from '@mui/material';
import { Card, CardBody, CardHeader } from '@nextui-org/react';
import SidebarLayout from '@/layouts/SidebarLayout';
import PageTitleWrapper from '@/components/PageTitleWrapper';
import Footer from '@/components/Footer';

const WalletTransactions = () => {
    const [transactions, setTransactions] = useState([]);

    const fetchTransactions = async () => {
        const url = `/api/wallet-islemler-gecmisi`;

        try {
            const res = await fetch(url);

            if (res.ok) {
                const data = await res.json();
                setTransactions(data);
            } else {
                console.error('Failed to fetch transactions');
            }
        } catch (error) {
            console.error('Error fetching transactions:', error);
        }
    };

    useEffect(() => {
        fetchTransactions();
    }, []);

    // İşlem tarihini gün adı, saat ve dakika olarak formatlayan fonksiyon
    const formatTransactionDate = (dateString) => {
        const date = new Date(dateString);
        const options = {
            
            hour12: false
        };
        return date.toLocaleString('tr-TR', options);
    };

    const handleApproveTransaction = async (transactionId) => {
        try {
            const res = await fetch('/api/wallet-transctions-update', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({ transactionId })
            });

            if (res.ok) {
                fetchTransactions(); // Yeniden işlemleri almak için
            } else {
                console.error('Failed to approve transaction');
            }
        } catch (error) {
            console.error('Error approving transaction:', error);
        }
    };
    
    

    return (
        <>
            <Head>
                <title>Cüzdan İşlemleri</title>
            </Head>
            <Container maxWidth="lg">
                <PageTitleWrapper>
                    <Grid container alignItems="center">
                        <Grid item>
                            <Typography variant="h3" component="h3" gutterBottom>
                               Cüzdan İşlemleri
                            </Typography>
                        </Grid>
                    </Grid>
                </PageTitleWrapper>
                <Grid container spacing={3}>
                    <Grid item xs={12}>
                        <Card>
                            <CardHeader>
                                <Typography variant="h5" component="h2">
                                    Gelen Hesaplar
                                </Typography>
                            </CardHeader>
                            <CardBody>
                                <Grid container spacing={2}>
                                    {transactions.map((transaction) => (
                                        <Grid item xs={12} key={transaction.id}>
                                            <Card>
                                                <CardHeader>
                                                    <Typography variant="body1" component="p">
                                                        {transaction.description} <br />
                                                        Tutar: {transaction.amount} ₺ <br />
                                                        Holly Puan İndirimi: {transaction.calculatedHollyPoints || 0} ₺<br />
                                                        Kullanılan Holly Puanı: {transaction.usedHollyPoints || 0} <br />
                                                        Cüzdan İd : {transaction.walletId} <br />
                                                        Kullanıcı: {transaction.user.firstName} {transaction.user.lastName} <br />
                                                        İşlem Zamanı: {formatTransactionDate(transaction.createdAt)} <br />
                                                        {transaction.kasaonay ? (
                                                            <Typography variant="body1" component="p">
                                                                İşlem Onaylandı
                                                            </Typography>
                                                        ) : (
                                                            <Button variant="contained" onClick={() => handleApproveTransaction(transaction.id)}>
                                                                Onayla
                                                            </Button>
                                                        )}
                                                    </Typography>
                                                </CardHeader>
                                            </Card>
                                        </Grid>
                                    ))}
                                </Grid>
                            </CardBody>
                        </Card>
                    </Grid>
                </Grid>
            </Container>
            <Footer />
        </>
    );
};

WalletTransactions.getLayout = (page: React.ReactNode) => <SidebarLayout>{page}</SidebarLayout>;

export const getServerSideProps = authMiddleware(async (_context: any) => {
    return {
        props: {},
    };
});

export default WalletTransactions;