import { PrismaClient } from '@prisma/client';

const prisma = new PrismaClient();

export default async function handler(req, res) {
    if (req.method === 'DELETE') {
        const { id } = req.body;

        try {
            const updatedConcert = await prisma.concert.update({
                where: {
                    id: parseInt(id),
                },
                data: {
                    isDeleted: true,
                },
            });

            res.status(200).json({ message: 'Ko<PERSON>r başarılı bir şekilde silindi!', updatedItem: updatedConcert });
        } catch (error) {
            console.error(error);
            res.status(500).json({ message: 'Internal server error', error });
        }
    } else {
        res.status(405).json({ message: 'Method not allowed' });
    }
}
