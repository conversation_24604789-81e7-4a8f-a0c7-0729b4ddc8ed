import React, { useState, useEffect } from 'react';
import {
  <PERSON>ton,
  Card,
  Box,
  Grid,
  Typography,
  useTheme,
  CardHeader,
} from '@mui/material';
import Link from 'next/link';
import { Image } from '@nextui-org/react';
import { Spinner } from '@nextui-org/react';
import { getCookie } from 'cookies-next';

function HollyTicketVendor() {
  const theme = useTheme();

  const [total, setTotal] = useState(0);
  const [totalIncome, setTotalIncome] = useState(0);
  const [totalRefund, setTotalRefund] = useState(0);
  const [totalCount, setTotalCount] = useState(0);
  const [totalIncomeCount, setTotalIncomeCount] = useState(0);
  const [totalRefundCount, setTotalRefundCount] = useState(0);
  const [bestSeller, setBestSeller]: any = useState({});
  const [loading, setLoading] = useState(true);

  const date = new Date();
  const turhishMonths = [
    'Oca<PERSON>',
    '<PERSON><PERSON><PERSON>',
    'Mart',
    '<PERSON>san',
    'Mayıs',
    'Ha<PERSON>ran',
    'Temmuz',
    'Ağustos',
    'Eylül',
    'Ekim',
    'Kasım',
    'Aralık',
  ];
  const thisMonthTurkish = turhishMonths[date.getMonth()];

  const loadList = async () => {
    const vendorId = getCookie("vendorId");
    const url = `/api/holly-ticket-vendor?vendorId=${vendorId}`;
    setLoading(true);

    try {
      const res = await fetch(url);

      if (res.status === 200) {
        const json = await res.json();
        setTotal(json.total);
        setTotalIncome(json.totalIncome);
        setTotalRefund(json.totalRefund);
        setTotalCount(json.totalCount);
        setTotalIncomeCount(json.totalIncomeCount);
        setTotalRefundCount(json.totalRefundCount);
        setBestSeller(json.bestSeller);
      } else {
        console.log("API call failed");
      }
    } catch (error) {
      console.error(error);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    loadList();
  }, []);

  return (
    <Card>
      <CardHeader title="Holly Ticket" />
      {
        loading ? (
          <div className="flex">
            <div className="p-4 w-full flex flex-col justify-center items-center">
              <Spinner />
            </div>
          </div>
        ) : (
          <Grid spacing={0} container>
            <Grid item xs={12} md={12}>
              <Box p={4}>
                <Typography
                  sx={{
                    pb: 3
                  }}
                  variant="h4"
                >
                  {`${thisMonthTurkish} Ayı`}
                </Typography>
                <Box>
                  <div className="w-full mb-2 flex flex-row">
                    <div className="mr-2">
                      <Typography
                        sx={{
                          pb: 3
                        }}
                        variant="h4"
                      >
                        {`Toplam Gelir (${totalIncomeCount})`}
                      </Typography>
                      <Typography variant="h1" gutterBottom sx={{
                        color: "green"
                      }}>
                        {totalIncome ?? 0} ₺
                      </Typography>
                    </div>
                    <div className="ml-2 mr-2">
                      <Typography
                        sx={{
                          pb: 3
                        }}
                        variant="h4"
                      >
                        {`Toplam İade (${totalRefundCount})`}
                      </Typography>
                      <Typography variant="h1" gutterBottom sx={{
                        color: "red"
                      }}>
                        {totalRefund ?? 0} ₺
                      </Typography>
                    </div>
                    <div className="ml-2">
                      <Typography
                        sx={{
                          pb: 3
                        }}
                        variant="h4"
                      >
                        {`Genel Toplam (${totalCount})`}
                      </Typography>
                      <Typography variant="h1" gutterBottom>
                        {total ?? 0} ₺
                      </Typography>
                    </div>
                  </div>
                  <Box
                    display="flex"
                    sx={{
                      py: 4
                    }}
                    alignItems="center"
                  >
                    <Image
                      width={theme.spacing(8)}
                      height={theme.spacing(8)}
                      radius="md"
                      loading="eager"
                      src={bestSeller?.image ? `https://api.hollystone.com.tr/resources/images/${bestSeller?.image}` : "https://api.hollystone.com.tr/resources/images/questionMark.webp"}
                    />
                    <Box sx={{
                      ml: 2
                    }}>
                      <Typography variant="h4">En Çok Bilet Satan Konser</Typography>
                      <Typography variant="subtitle2" noWrap>
                        Bu Ay
                      </Typography>
                    </Box>
                  </Box>
                </Box>
                <Grid container spacing={3}>
                  <Grid sm item>
                    <Link href="/concerts/new">
                      <Button fullWidth variant="outlined">
                        Konser Oluştur
                      </Button>
                    </Link>
                  </Grid>
                  <Grid sm item>
                    <Link href="/daily-activities/new">
                      <Button fullWidth variant="contained">
                        Günlük Etkinlik Oluştur
                      </Button>
                    </Link>
                  </Grid>
                </Grid>
              </Box>
            </Grid>
          </Grid>
        )
      }
    </Card>
  );
}

export default HollyTicketVendor;
