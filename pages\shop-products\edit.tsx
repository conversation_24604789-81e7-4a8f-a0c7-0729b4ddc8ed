import React, { useState, useEffect } from 'react';
import Head from 'next/head';
import { useRouter } from 'next/router';
import { authMiddleware } from 'middleware';
import Swal from 'sweetalert2';
import { Container, Grid, Typography, CircularProgress } from '@mui/material';
import { Image, Card, CardBody, CardHeader, Button, Input, Divider, Accordion, AccordionItem, Tabs, Tab } from '@nextui-org/react';
import { Select, SelectItem } from "@nextui-org/select";
import { Listbox, ListboxItem } from "@nextui-org/listbox";
import SidebarLayout from '@/layouts/SidebarLayout';
import PageTitleWrapper from '@/components/PageTitleWrapper';
import Footer from '@/components/Footer';
import { PlusIcon } from '@/components/PlusIcon';
import DeleteForeverIcon from '@mui/icons-material/DeleteForever';
import NextUIModal from 'pages/components/modal';
import ArrowBackIcon from '@mui/icons-material/ArrowBack';
import Link from 'next/link';

const EditProductPage = () => {
    const router = useRouter();
    const [isOpen1, setIsOpen1] = useState(false);
    const [isOpen2, setIsOpen2] = useState(false);
    const [currentTimestamp, setCurrentTimestamp] = useState(new Date().getTime());
    const [item, setItem]: any = useState({});
    const { id } = router.query;
    const [loading, setLoading] = useState(true);
    const [imageLoading, setImageLoading] = useState(false);
    const [newDetailTitle, setNewDetailTitle] = useState("");
    const [newOption, setNewOption] = useState("");
    const [categories, setCategories] = useState([]);

    const loadList = async () => {
        const url = `/api/shop-products?id=${id}`;

        try {
            const res = await fetch(url);

            if (res.status === 200) {
                const json = await res.json();
                json.results[0].images = json.results[0]?.images.split(',');
                json.results[0].options = json.results[0]?.options.split(',');
                json.results[0].details = JSON.parse(json.results[0]?.details);
                setItem(json.results[0]);
            } else {
                Swal.fire({
                    icon: 'error',
                    title: 'Hata',
                    text: 'Bir sorun meydana geldi!',
                });
            }
        } catch (error) {
            console.error(error);
        } finally {
            setLoading(false);
        }
    };

    const loadCategories = async () => {
        const url = `/api/shop-categories`;

        try {
            const res = await fetch(url);

            if (res.status === 200) {
                const json = await res.json();
                setCategories(json.results);
            } else {
                Swal.fire({
                    icon: 'error',
                    title: 'Hata',
                    text: 'Bir sorun meydana geldi!',
                });
            }
        } catch (error) {
            console.error(error);
        }
    };

    useEffect(() => {
        loadList();
        loadCategories();
    }, []);

    const onOpen1 = () => {
        setIsOpen1(true);
    };

    const onClose1 = () => {
        setIsOpen1(false);
    };

    const onOpenChange1 = (newIsOpen) => {
        console.log('isOpen changed to', newIsOpen);
    };

    useEffect(() => {
        onOpenChange1(isOpen1);
    }, [isOpen1]);

    const onOpen2 = () => {
        setIsOpen2(true);
    };

    const onClose2 = () => {
        setIsOpen2(false);
    };

    const onOpenChange2 = (newIsOpen) => {
        console.log('isOpen changed to', newIsOpen);
    };

    useEffect(() => {
        onOpenChange2(isOpen2);
    }, [isOpen2]);

    const handleImageChange = async (event, image) => {
        const file = event.target.files[0];
        const filePath = image.split('.');

        if (file) {
            if (!file.type.startsWith('image/')) {
                Swal.fire({
                    icon: 'error',
                    title: 'Hata',
                    text: 'Seçilen format desteklenmiyor!',
                });
                return;
            }
            setImageLoading(true);
            const formData = new FormData();
            formData.append('image', file);
            formData.append('fileName', filePath[0]);

            try {
                const response = await fetch(`https://api.hollystone.com.tr/api/functions/upload`, {
                    method: 'POST',
                    headers: {
                        'Authorization': `Bearer ${localStorage.getItem('token')}`
                    },
                    body: formData,
                });

                if (response.ok) {
                    const data = await response.json();

                    if (data.type == "success") {
                        Swal.fire({
                            icon: 'success',
                            title: 'Başarılı',
                            text: 'Resim güncellendi!',
                        });
                        setCurrentTimestamp(new Date().getTime());
                    } else {
                        Swal.fire({
                            icon: 'error',
                            title: 'Hata',
                            text: data.error,
                        });
                    }
                } else {
                    Swal.fire({
                        icon: 'error',
                        title: 'Hata',
                        text: 'Bir sorun meydana geldi!',
                    });
                }
            } catch (error) {
                Swal.fire({
                    icon: 'error',
                    title: 'Hata',
                    text: 'Bir sorun meydana geldi!',
                });
            }
            setImageLoading(false);
        }
    };

    const handleDelete = async () => {
        const shouldDelete = await Swal.fire({
            title: 'Dikkat!',
            text: 'Bu kaydı silmek istediğine gerçekten emin misin?',
            icon: 'warning',
            showCancelButton: true,
            confirmButtonText: 'Evet, sil!',
            cancelButtonText: 'Vazgeç',
        });

        if (shouldDelete.isConfirmed) {
            try {
                const response = await fetch(`/api/concerts/delete`, {
                    method: 'DELETE',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({ id: id }),
                });

                if (response.ok) {
                    router.push('/concerts');
                    Swal.fire({
                        icon: 'success',
                        title: 'Başarılı',
                        text: 'Kayıt silindi!',
                    });
                } else {
                    Swal.fire({
                        icon: 'error',
                        title: 'Hata',
                        text: 'Bir sorun meydana geldi!',
                    });
                }
            } catch (error) {
                console.error('An error occurred:', error);
            }
        }
    };

    const updateImage = async () => {
        const updatedData = {
            id: id,
            images: item.images.join(",")
        }
        try {
            const response = await fetch(`/api/shop-products/update`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify(updatedData),
            });

            if (response.ok) {
                Swal.fire({
                    icon: 'success',
                    title: 'Başarılı',
                    text: 'Resim listesi güncellendi!',
                });
            } else {
                Swal.fire({
                    icon: 'error',
                    title: 'Hata',
                    text: 'Bir sorun meydana geldi!',
                });
            }
        } catch (error) {
            console.error('An error occurred:', error);
        }
    }

    const handleNewDetail = async () => {
        let newDetails = item.details.filter(obj => !obj.hasOwnProperty(newDetailTitle));
        newDetails.push({ [newDetailTitle]: "" });
        setItem({ ...item, details: newDetails })
        setNewDetailTitle("");
    }

    const handleDeleteDetail = async (key) => {
        const shouldDelete = await Swal.fire({
            title: 'Dikkat!',
            text: 'Bu kaydı silmek istediğine gerçekten emin misin?',
            icon: 'warning',
            showCancelButton: true,
            confirmButtonText: 'Evet, sil!',
            cancelButtonText: 'Vazgeç',
        });

        if (shouldDelete.isConfirmed) {
            const newDetails = item.details.filter(obj => !obj.hasOwnProperty(key));
            setItem({ ...item, details: newDetails })
        }
    }

    const handleNewImage = async () => {
        const latestImage = item.images[item.images.length - 1];
        const lastImageParts = latestImage.split('-');
        const lastImageNumber = parseInt(lastImageParts[1].replace(".webp", ""));
        let newImages = item.images;
        newImages.push(`products/${id}-${lastImageNumber + 1}.webp`);
        setItem({ ...item, images: newImages })
        await updateImage();
    }

    const handleDeleteImage = async (key) => {
        const shouldDelete = await Swal.fire({
            title: 'Dikkat!',
            text: 'Bu resmi silmek istediğine gerçekten emin misin?',
            icon: 'warning',
            showCancelButton: true,
            confirmButtonText: 'Evet, sil!',
            cancelButtonText: 'Vazgeç',
        });

        if (shouldDelete.isConfirmed) {
            let newImages = item.images;
            const filePath = newImages[key];

            try {
                const response = await fetch(`https://api.hollystone.com.tr/api/functions/delete`, {
                    method: 'POST',
                    headers: {
                        'Authorization': `Bearer ${localStorage.getItem('token')}`,
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({ fileName: filePath })
                });

                if (response.ok) {
                    const data = await response.json();

                    if (data.type == "success") {
                        newImages.splice(key, 1);
                        setItem({ ...item, images: newImages })
                        await updateImage();
                        Swal.fire({
                            icon: 'success',
                            title: 'Başarılı',
                            text: 'Resim güncellendi!',
                        });
                    } else {
                        Swal.fire({
                            icon: 'error',
                            title: 'Hata',
                            text: data.error,
                        });
                    }
                } else {
                    Swal.fire({
                        icon: 'error',
                        title: 'Hata',
                        text: 'Bir sorun meydana geldi!',
                    });
                }
            } catch (error) {
                Swal.fire({
                    icon: 'error',
                    title: 'Hata',
                    text: 'Bir sorun meydana geldi!',
                });
            }
        }
    }

    const handleNewOption = async (key) => {
        if (!key) {
            let newOptions = item.options;
            newOptions.push(newOption);
            setItem({ ...item, options: newOptions })
            setNewOption("");
        } else {
            if (key == 'addNew') {
                onOpen2();
            } else {
                const shouldDelete = await Swal.fire({
                    title: 'Dikkat!',
                    text: 'Bu kaydı silmek istediğine gerçekten emin misin?',
                    icon: 'warning',
                    showCancelButton: true,
                    confirmButtonText: 'Evet, sil!',
                    cancelButtonText: 'Vazgeç',
                });

                if (shouldDelete.isConfirmed) {
                    let newOptions = item.options;
                    newOptions.splice(key, 1);
                    setItem({ ...item, options: newOptions })
                }
            }
        }
    }

    const handleUpdate = async (event) => {
        event.preventDefault();
        const updatedData = {
            id: id,
            details: JSON.stringify(item.details),
            name: item.name,
            description: item.description,
            rank: item.rank,
            price: item.price,
            discountedPrice: item.discountedPrice,
            options: item.options.join(","),
            hollyPoints: item.hollyPoints,
            status: item.status
        }
        try {
            const response = await fetch(`/api/shop-products/update`, {
                method: 'PUT',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify(updatedData),
            });

            if (response.ok) {
                await loadList();
                Swal.fire({
                    icon: 'success',
                    title: 'Başarılı',
                    text: 'Kayıt güncellendi!',
                });
            } else {
                Swal.fire({
                    icon: 'error',
                    title: 'Hata',
                    text: 'Bir sorun meydana geldi!',
                });
            }
        } catch (error) {
            console.error('An error occurred:', error);
        }
    }

    if (loading) {
        return "Loading";
    }

    return (
        <>
            <Head>
                <title>Ürün - Düzenle</title>
            </Head>
            <PageTitleWrapper>
                <Grid container alignItems="center">
                    <Grid item>
                        <Typography variant="h3" component="h3" gutterBottom>
                            Ürün Düzenle
                        </Typography>
                    </Grid>
                </Grid>
            </PageTitleWrapper>
            <Container maxWidth="lg">
                <Grid
                    container
                    direction="row"
                    justifyContent="center"
                    alignItems="stretch"
                    spacing={4}
                >
                    <Grid item xs={12}>
                        <Card
                            isBlurred
                            className="border-none bg-white dark:bg-default-100/50 "
                            shadow="sm"
                        >
                            <form onSubmit={handleUpdate}>
                                <CardHeader className="flex justify-between gap-3">
                                    <div>
                                        <Link href="/shop-products">
                                            <Button type="button" color="default" startContent={<ArrowBackIcon />}>
                                                Ürünler
                                            </Button>
                                        </Link>
                                    </div>
                                    <div>Ürün Bilgileri</div>
                                    <div className="flex-end">
                                        <Button type="button" onClick={handleDelete} color="danger" className="mr-2">
                                            Sil
                                        </Button>
                                        <Button type="submit" color="primary">
                                            Güncelle
                                        </Button>
                                    </div>
                                </CardHeader>

                                <Divider />

                                <CardBody>
                                    <div className="flex">
                                        <div className="relative w-1/2" style={{
                                            minWidth: 400
                                        }}>
                                            <Button color="primary" onClick={handleNewImage} endContent={<PlusIcon width={undefined} height={undefined} />}>
                                                Foroğraf Ekle
                                            </Button>
                                            <Accordion defaultExpandedKeys={["0"]}>
                                                {
                                                    item.images.map((image: any, index: number) => {
                                                        return (
                                                            <AccordionItem
                                                                key={index}
                                                                aria-label={`Resim ${index + 1}`}
                                                                title={`Resim ${index + 1}`}
                                                                startContent={
                                                                    (
                                                                        <div className="w-full mb-2 flex flex-row">
                                                                            <Button color="danger" onClick={() => { handleDeleteImage(index) }} className="min-w-unit-10 mr-2">
                                                                                <DeleteForeverIcon />
                                                                            </Button>
                                                                            <Image
                                                                                radius="none"
                                                                                loading="eager"
                                                                                width={100}
                                                                            height={100}
                                                                                className="h-unit-10"
                                                                                src={`https://api.hollystone.com.tr/resources/images/${image}?t=${currentTimestamp}`}
                                                                            />
                                                                        </div>
                                                                    )
                                                                }>
                                                                <label className="block cursor-pointer">
                                                                <div style={{ width: '100px', height: '100px' }}>
                                                                <Image
                                                                            width={100}
                                                                            height={100}
                                                                            radius="none"
                                                                            loading="eager"
                                                                            style={{
                                                                                height: "100%"
                                                                            }}
                                                                            src={`https://api.hollystone.com.tr/resources/images/${image}?t=${currentTimestamp}`}
                                                                            fallbackSrc="https://via.placeholder.com/400x400.png?text="
                                                                        />
                                                                        {imageLoading ? (
                                                                            <CircularProgress
                                                                                size={48}
                                                                                style={{
                                                                                    position: 'absolute',
                                                                                    top: '40%',
                                                                                    left: '40%',
                                                                                    transform: 'translate(-40%, -40%)',
                                                                                    zIndex: 999,
                                                                                }}
                                                                            />
                                                                        ) : null}
                                                                    </div>
                                                                    <Input
                                                                        type="file"
                                                                        label="Resim"
                                                                        name="image"
                                                                        className="hidden"
                                                                        accept="image/*"
                                                                        onChange={(e: any) => { handleImageChange(e, image) }}
                                                                    />
                                                                </label>
                                                            </AccordionItem>
                                                        )
                                                    })
                                                }
                                            </Accordion>
                                        </div>

                                        <div className="p-4 w-full">
                                            <div className="w-full mb-2 flex flex-row">
                                                <Input
                                                    type="text"
                                                    label="Başlık"
                                                    name="name"
                                                    value={item.name}
                                                    className="mr-2"
                                                    onChange={(e: any) => setItem({ ...item, name: e.target.value })}
                                                />
                                                {categories.length > 0 ? (
                                                    <Select
                                                        placeholder="Bir seçim yapın..."
                                                        label="Kategori"
                                                        defaultSelectedKeys={[item.categoryId?.toString()]}
                                                        onChange={(e: any) => {
                                                            setItem({ ...item, categoryId: parseInt(e.currentKey) });
                                                        }}
                                                        style={{
                                                            minWidth: "150px"
                                                        }}
                                                    >
                                                        {[
                                                            <SelectItem key="0" value="0"></SelectItem>,
                                                            ...categories.map((dItem, dIndex) => (
                                                                <SelectItem key={dItem.id} value={dIndex}>
                                                                    {dItem.name}
                                                                </SelectItem>
                                                            ))
                                                        ]}
                                                    </Select>
                                                ) : (<CircularProgress />)}
                                            </div>
                                            <Input
                                                type="text"
                                                label="Açıklama"
                                                placeholder="Açıklama"
                                                name="description"
                                                value={item.description}
                                                className="mb-2"
                                                onChange={(e: any) => setItem({ ...item, description: e.target.value })}
                                            />
                                            <Divider className="mb-2" />
                                            <Button color="primary" size="sm" className="p-0 mr-2" onPress={onOpen1}>&nbsp;<PlusIcon width={undefined} height={undefined} />&nbsp;</Button>
                                            <Tabs variant="underlined" className="mb-2">
                                                {
                                                    item.details.map((detail, key) => (
                                                        Object.keys(detail).map((index) => (
                                                            <Tab key={index} title={index}>
                                                                <div className="w-full mb-2 flex flex-row">
                                                                    <Input
                                                                        type="text"
                                                                        label="Değer"
                                                                        placeholder="Değer"
                                                                        name="value"
                                                                        value={detail[index]}
                                                                        className="mr-2"
                                                                        onChange={(e: any) => {
                                                                            const newValue = e.target.value;
                                                                            const updatedDetails = item.details.map((d, idx) => {
                                                                                if (idx === key) {
                                                                                    return { [index]: newValue };
                                                                                }
                                                                                return d;
                                                                            });
                                                                            setItem({ ...item, details: updatedDetails });
                                                                        }}
                                                                    />
                                                                    <Button color="danger" onClick={() => { handleDeleteDetail(index) }} className="min-w-unit-10 h-14">
                                                                        <DeleteForeverIcon />
                                                                    </Button>
                                                                </div>
                                                            </Tab>
                                                        ))
                                                    ))
                                                }
                                            </Tabs>
                                            <Divider className="mb-2" />
                                            <div className="py-3 px-1 outline-none">
                                                <Card className="border-none mb-2 bg-default-100 dark:bg-default-100/50 max-w-[610px]" shadow="none">
                                                    <CardHeader>
                                                        <label className="block font-medium text-foreground-600 text-tiny will-change-auto origin-top-left transition-all !duration-200 !ease-[cubic-bezier(0,0,0.2,1)] motion-reduce:transition-none">Varyantlar</label>
                                                    </CardHeader>
                                                    <CardBody style={{ padding: 0 }}>
                                                        <Listbox aria-label="Actions" className="flex flex-row" onAction={(key) => handleNewOption(key)}>
                                                            <ListboxItem key="addNew" className="text-primary" color="primary" endContent={<PlusIcon width={undefined} height={undefined} />}>Ekle</ListboxItem>
                                                            {item.options.map((option, index) => {
                                                                return (
                                                                    <ListboxItem key={index}>{option}</ListboxItem>
                                                                );
                                                            })}
                                                        </Listbox>
                                                    </CardBody>
                                                </Card>
                                            </div>
                                            <Divider className="mb-2" />
                                            <div className="w-full mb-2 flex flex-row">
                                                <Input
                                                    type="number"
                                                    label="Fiyat"
                                                    placeholder="Fiyat"
                                                    name="price"
                                                    min={0}
                                                    value={item.price}
                                                    className="mr-2"
                                                    onChange={(e: any) => setItem({ ...item, price: e.target.value })}
                                                />
                                                <Input
                                                    type="number"
                                                    label="İndirimli Fiyat"
                                                    placeholder="İndirimli Fiyat"
                                                    name="discountedPrice"
                                                    min={0}
                                                    value={item.discountedPrice}
                                                    onChange={(e: any) => setItem({ ...item, discountedPrice: e.target.value })}
                                                />
                                            </div>
                                            <div className="w-full mb-2 flex flex-row">
                                                <Input
                                                    type="number"
                                                    label="Holly Puan"
                                                    placeholder="Holly Puan"
                                                    name="hollyPoints"
                                                    min={0}
                                                    value={item.hollyPoints}
                                                    className="mr-2"
                                                    onChange={(e: any) => setItem({ ...item, hollyPoints: parseInt(e.target.value) })}
                                                />
                                                <Input
                                                    type="number"
                                                    label="Sıralama"
                                                    placeholder="Sıralama"
                                                    name="rank"
                                                    min={0}
                                                    value={item.rank}
                                                    className="mr-2"
                                                    onChange={(e: any) => setItem({ ...item, rank: parseInt(e.target.value) })}
                                                />
                                                <Select
                                                    label="Durum"
                                                    placeholder="Bir seçim yapın..."
                                                    defaultSelectedKeys={[item.status?.toString()]}
                                                    onChange={(e: any) => setItem({ ...item, status: (/true/i).test(e.target.value) })}
                                                >
                                                    <SelectItem key="true" value="true">
                                                        Aktif
                                                    </SelectItem>
                                                    <SelectItem key="false" value="false">
                                                        Pasif
                                                    </SelectItem>
                                                </Select>
                                            </div>
                                        </div>
                                    </div>
                                </CardBody>
                            </form>
                        </Card>
                    </Grid>
                </Grid>
            </Container >
            <NextUIModal
                newTitle={newDetailTitle}
                setNewTitle={setNewDetailTitle}
                handleNew={handleNewDetail}
                isOpen={isOpen1}
                onClose={onClose1}
                onOpenChange={onOpenChange1}
            ></NextUIModal>
            <NextUIModal
                newTitle={newOption}
                setNewTitle={setNewOption}
                handleNew={handleNewOption}
                isOpen={isOpen2}
                onClose={onClose2}
                onOpenChange={onOpenChange2}
            ></NextUIModal>
            <Footer />
        </>
    );
};

EditProductPage.getLayout = (page: React.ReactNode) => <SidebarLayout>{page}</SidebarLayout>;

export const getServerSideProps = authMiddleware(async (_context: any) => {
    return {
        props: {},
    };
});

export default EditProductPage;
