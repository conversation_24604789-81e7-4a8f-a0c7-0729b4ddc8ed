# THIS IS AN AUTOGENERATED FILE. DO NOT EDIT THIS FILE DIRECTLY.
# yarn lockfile v1


"@aashutoshrathi/word-wrap@^1.2.3":
  version "1.2.6"
  resolved "https://registry.npmjs.org/@aashutoshrathi/word-wrap/-/word-wrap-1.2.6.tgz"
  integrity sha512-1Yjs2SvM8TflER/OD3cOjhWWOZb58A2t7wpE2S9XfBYTiIl+XFhQG2bjy4Pu1I+EAlCNUzRDYDdFwFYUKvXcIA==

"@alloc/quick-lru@^5.2.0":
  version "5.2.0"
  resolved "https://registry.npmjs.org/@alloc/quick-lru/-/quick-lru-5.2.0.tgz"
  integrity sha512-UrcABB+4bUrFABwbluTIBErXwvbsU/V7TZWfmbgJfbkwiBuziS9gxdODUyuiecfdGQ85jglMW6juS3+z5TsKLw==

"@babel/code-frame@^7.0.0":
  version "7.22.13"
  resolved "https://registry.npmjs.org/@babel/code-frame/-/code-frame-7.22.13.tgz"
  integrity sha512-XktuhWlJ5g+3TJXc5upd9Ks1HutSArik6jf2eAjYFyIOf4ej3RN+184cZbzDvbPnuTJIUhPKKJE3cIsYTiAT3w==
  dependencies:
    "@babel/highlight" "^7.22.13"
    chalk "^2.4.2"

"@babel/helper-module-imports@^7.16.7":
  version "7.22.15"
  resolved "https://registry.npmjs.org/@babel/helper-module-imports/-/helper-module-imports-7.22.15.tgz"
  integrity sha512-0pYVBnDKZO2fnSPCrgM/6WMc7eS20Fbok+0r88fp+YtWVLZrp4CkafFGIp+W0VKw4a22sgebPT99y+FDNMdP4w==
  dependencies:
    "@babel/types" "^7.22.15"

"@babel/helper-string-parser@^7.22.5":
  version "7.22.5"
  resolved "https://registry.npmjs.org/@babel/helper-string-parser/-/helper-string-parser-7.22.5.tgz"
  integrity sha512-mM4COjgZox8U+JcXQwPijIZLElkgEpO5rsERVDJTc2qfCDfERyob6k5WegS14SX18IIjv+XD+GrqNumY5JRCDw==

"@babel/helper-validator-identifier@^7.22.20":
  version "7.22.20"
  resolved "https://registry.npmjs.org/@babel/helper-validator-identifier/-/helper-validator-identifier-7.22.20.tgz"
  integrity sha512-Y4OZ+ytlatR8AI+8KZfKuL5urKp7qey08ha31L8b3BwewJAoJamTzyvxPR/5D+KkdJCGPq/+8TukHBlY10FX9A==

"@babel/highlight@^7.22.13":
  version "7.22.20"
  resolved "https://registry.npmjs.org/@babel/highlight/-/highlight-7.22.20.tgz"
  integrity sha512-dkdMCN3py0+ksCgYmGG8jKeGA/8Tk+gJwSYYlFGxG5lmhfKNoAy004YpLxpS1W2J8m/EK2Ew+yOs9pVRwO89mg==
  dependencies:
    "@babel/helper-validator-identifier" "^7.22.20"
    chalk "^2.4.2"
    js-tokens "^4.0.0"

"@babel/runtime@^7.12.5", "@babel/runtime@^7.13.10", "@babel/runtime@^7.14.0", "@babel/runtime@^7.16.0", "@babel/runtime@^7.17.2", "@babel/runtime@^7.18.3", "@babel/runtime@^7.20.13", "@babel/runtime@^7.20.7", "@babel/runtime@^7.23.2", "@babel/runtime@^7.3.1", "@babel/runtime@^7.5.5", "@babel/runtime@^7.8.3", "@babel/runtime@^7.8.7":
  version "7.23.2"
  resolved "https://registry.npmjs.org/@babel/runtime/-/runtime-7.23.2.tgz"
  integrity sha512-mM8eg4yl5D6i3lu2QKPuPH4FArvJ8KhTofbE7jwMUv9KX5mBvwPAqnV3MlyBNqdp9RyRKP6Yck8TrfYrPvX3bg==
  dependencies:
    regenerator-runtime "^0.14.0"

"@babel/types@^7.22.15":
  version "7.23.0"
  resolved "https://registry.npmjs.org/@babel/types/-/types-7.23.0.tgz"
  integrity sha512-0oIyUfKoI3mSqMvsxBdclDwxXKXAUA8v/apZbc+iSyARYou1o8ZGDxbUYyLFoW2arqS2jDGqJuZvv1d/io1axg==
  dependencies:
    "@babel/helper-string-parser" "^7.22.5"
    "@babel/helper-validator-identifier" "^7.22.20"
    to-fast-properties "^2.0.0"

"@ckeditor/ckeditor5-adapter-ckfinder@39.0.2":
  version "39.0.2"
  resolved "https://registry.npmjs.org/@ckeditor/ckeditor5-adapter-ckfinder/-/ckeditor5-adapter-ckfinder-39.0.2.tgz"
  integrity sha512-7HIFArQXAI6ciXc9hvaJngmS15EV34UZGHKXM6pbZJkJCcdMQ/qL5rMZ2NiLDfvm8hrBwx4O8Vt0tmWa4ylyJg==
  dependencies:
    ckeditor5 "39.0.2"

"@ckeditor/ckeditor5-autoformat@39.0.2":
  version "39.0.2"
  resolved "https://registry.npmjs.org/@ckeditor/ckeditor5-autoformat/-/ckeditor5-autoformat-39.0.2.tgz"
  integrity sha512-iltoRC/XAgApwM7+TErrGMjkX+G13YbB/YxCTXBBQeHiPRnEEpPMeqHGyLMipSshq6mCZaPNwzmfaQ1pQ+PWlg==
  dependencies:
    ckeditor5 "39.0.2"

"@ckeditor/ckeditor5-basic-styles@39.0.2":
  version "39.0.2"
  resolved "https://registry.npmjs.org/@ckeditor/ckeditor5-basic-styles/-/ckeditor5-basic-styles-39.0.2.tgz"
  integrity sha512-sp5sfE4d8Tng/zIssBK+SgaEkPfGcbmoGkjbHhPlWYAoQB9ONwvWkRZy9s3C+H0gfsEj3EqqxUKY70Us0yT8rA==
  dependencies:
    ckeditor5 "39.0.2"

"@ckeditor/ckeditor5-block-quote@39.0.2":
  version "39.0.2"
  resolved "https://registry.npmjs.org/@ckeditor/ckeditor5-block-quote/-/ckeditor5-block-quote-39.0.2.tgz"
  integrity sha512-ykxkLWJ1nuu5vVBnvHKKgKleudUNTjt/2zDsvUAiBbl4iN0rnOqhLbSL54fY4130p/DvE1CVoozqNiv7L83Vjw==
  dependencies:
    ckeditor5 "39.0.2"

"@ckeditor/ckeditor5-build-classic@^39.0.1":
  version "39.0.2"
  resolved "https://registry.npmjs.org/@ckeditor/ckeditor5-build-classic/-/ckeditor5-build-classic-39.0.2.tgz"
  integrity sha512-uozP7NOEkMMKivVQql08SzBp+Fl1hgYzPDK6wb7JQuuMHvhooJhyY6AzRI6pPQNylp35R3BC2s9G4rh6beX/CQ==
  dependencies:
    "@ckeditor/ckeditor5-adapter-ckfinder" "39.0.2"
    "@ckeditor/ckeditor5-autoformat" "39.0.2"
    "@ckeditor/ckeditor5-basic-styles" "39.0.2"
    "@ckeditor/ckeditor5-block-quote" "39.0.2"
    "@ckeditor/ckeditor5-ckbox" "39.0.2"
    "@ckeditor/ckeditor5-ckfinder" "39.0.2"
    "@ckeditor/ckeditor5-cloud-services" "39.0.2"
    "@ckeditor/ckeditor5-easy-image" "39.0.2"
    "@ckeditor/ckeditor5-editor-classic" "39.0.2"
    "@ckeditor/ckeditor5-essentials" "39.0.2"
    "@ckeditor/ckeditor5-heading" "39.0.2"
    "@ckeditor/ckeditor5-image" "39.0.2"
    "@ckeditor/ckeditor5-indent" "39.0.2"
    "@ckeditor/ckeditor5-link" "39.0.2"
    "@ckeditor/ckeditor5-list" "39.0.2"
    "@ckeditor/ckeditor5-media-embed" "39.0.2"
    "@ckeditor/ckeditor5-paragraph" "39.0.2"
    "@ckeditor/ckeditor5-paste-from-office" "39.0.2"
    "@ckeditor/ckeditor5-table" "39.0.2"
    "@ckeditor/ckeditor5-typing" "39.0.2"

"@ckeditor/ckeditor5-ckbox@39.0.2":
  version "39.0.2"
  resolved "https://registry.npmjs.org/@ckeditor/ckeditor5-ckbox/-/ckeditor5-ckbox-39.0.2.tgz"
  integrity sha512-z+2tAmwmX+frW3VXyPyBC03GbDty8No++0zBXEszHHPt3j9FkjoqqFPbS33SqIXC+wAoDqY4vEgUrmzBbaB21g==
  dependencies:
    ckeditor5 "39.0.2"

"@ckeditor/ckeditor5-ckfinder@39.0.2":
  version "39.0.2"
  resolved "https://registry.npmjs.org/@ckeditor/ckeditor5-ckfinder/-/ckeditor5-ckfinder-39.0.2.tgz"
  integrity sha512-Ony6dTV0TVB1WkcsupA5Ydz7E0BU8eoU0ivp6zubfyfHHt1vUqQaU+FH6NStqbamVbtpb7JnXBPvfCGtL8tpdg==
  dependencies:
    ckeditor5 "39.0.2"

"@ckeditor/ckeditor5-clipboard@39.0.2":
  version "39.0.2"
  resolved "https://registry.npmjs.org/@ckeditor/ckeditor5-clipboard/-/ckeditor5-clipboard-39.0.2.tgz"
  integrity sha512-SSuanBiQ4lWznXSGyeV5vqQweVWZ1+iZG1/atmrZSb/BOWa3XD6EGPu9tuzdm9MCjk2OxBuyMT07Cw+WzXFosQ==
  dependencies:
    "@ckeditor/ckeditor5-core" "39.0.2"
    "@ckeditor/ckeditor5-engine" "39.0.2"
    "@ckeditor/ckeditor5-ui" "39.0.2"
    "@ckeditor/ckeditor5-utils" "39.0.2"
    "@ckeditor/ckeditor5-widget" "39.0.2"
    lodash-es "4.17.21"

"@ckeditor/ckeditor5-cloud-services@39.0.2":
  version "39.0.2"
  resolved "https://registry.npmjs.org/@ckeditor/ckeditor5-cloud-services/-/ckeditor5-cloud-services-39.0.2.tgz"
  integrity sha512-MPs9yiSdgzugF6jqrpYut0qaYfDkRPuQ2ipHlC/zb6G4pivvJTctNIBcJL/6hAFnE5KUbayynjVfhQ4RNDemtA==
  dependencies:
    ckeditor5 "39.0.2"

"@ckeditor/ckeditor5-core@>=37.0.0", "@ckeditor/ckeditor5-core@39.0.2":
  version "39.0.2"
  resolved "https://registry.npmjs.org/@ckeditor/ckeditor5-core/-/ckeditor5-core-39.0.2.tgz"
  integrity sha512-/xtor5vIXgwBVsAj+yO/wyzezQUmXabdkb/T8aSXtO2665zeOVbDbtSsJ1Ov7Tz5A4Ia1pA9d7iDCt7E8Kva7A==
  dependencies:
    "@ckeditor/ckeditor5-engine" "39.0.2"
    "@ckeditor/ckeditor5-utils" "39.0.2"
    lodash-es "4.17.21"

"@ckeditor/ckeditor5-easy-image@39.0.2":
  version "39.0.2"
  resolved "https://registry.npmjs.org/@ckeditor/ckeditor5-easy-image/-/ckeditor5-easy-image-39.0.2.tgz"
  integrity sha512-tOpmHH97Mv0bz844fciqxXe3owUyqqh4MqCMZ7c5cjywuvZOQ8Kp/o7HgWEKKRuKJUoLGaBH0bDRKMpIvXkOVg==
  dependencies:
    ckeditor5 "39.0.2"

"@ckeditor/ckeditor5-editor-classic@39.0.2":
  version "39.0.2"
  resolved "https://registry.npmjs.org/@ckeditor/ckeditor5-editor-classic/-/ckeditor5-editor-classic-39.0.2.tgz"
  integrity sha512-zDDFe70junUJT5VltMsVdBqSZUew4fCe8fmi9JMI2m2MAwNwARAOkOy9mQMMTJlrD+P3CfD/Y9QpromEjLOnYA==
  dependencies:
    ckeditor5 "39.0.2"
    lodash-es "4.17.21"

"@ckeditor/ckeditor5-engine@>=37.0.0", "@ckeditor/ckeditor5-engine@39.0.2":
  version "39.0.2"
  resolved "https://registry.npmjs.org/@ckeditor/ckeditor5-engine/-/ckeditor5-engine-39.0.2.tgz"
  integrity sha512-ERcEpIrmTML0/uhukkC+ZJSOx4mRaPbNG5vPEBXIentfDpzu1NrmUhGZRGXaw5lltL+NJbuTI0wjEINap0Hl3w==
  dependencies:
    "@ckeditor/ckeditor5-utils" "39.0.2"
    lodash-es "4.17.21"

"@ckeditor/ckeditor5-enter@39.0.2":
  version "39.0.2"
  resolved "https://registry.npmjs.org/@ckeditor/ckeditor5-enter/-/ckeditor5-enter-39.0.2.tgz"
  integrity sha512-7yeZI/mzJH4/yryXFQemrueZA/UNGkfnauN8Mu6XFMykoRNo6l+cASz0x4iGb3pWumV3g+tUeX6AJmuwXkRb5A==
  dependencies:
    "@ckeditor/ckeditor5-core" "39.0.2"
    "@ckeditor/ckeditor5-engine" "39.0.2"
    "@ckeditor/ckeditor5-utils" "39.0.2"

"@ckeditor/ckeditor5-essentials@39.0.2":
  version "39.0.2"
  resolved "https://registry.npmjs.org/@ckeditor/ckeditor5-essentials/-/ckeditor5-essentials-39.0.2.tgz"
  integrity sha512-5Nf3XeuB0thXTJS2M2JYrlW2uuSujNOhwW7L05e7Z5JP4nUSkopBHdvymX9feZKMzGPOw6SVhk8gQIDYV7S28A==
  dependencies:
    ckeditor5 "39.0.2"

"@ckeditor/ckeditor5-heading@39.0.2":
  version "39.0.2"
  resolved "https://registry.npmjs.org/@ckeditor/ckeditor5-heading/-/ckeditor5-heading-39.0.2.tgz"
  integrity sha512-P1ogDMAsTZB/TfuNwifNv1H9KHaMSPRaPfd+clhhLV/GAdt2rGMeH0HKuSke54cZYB6qMQP7Za8Xsfi2ufY0GA==
  dependencies:
    ckeditor5 "39.0.2"

"@ckeditor/ckeditor5-image@39.0.2":
  version "39.0.2"
  resolved "https://registry.npmjs.org/@ckeditor/ckeditor5-image/-/ckeditor5-image-39.0.2.tgz"
  integrity sha512-sp7ce+j/UqRW4kmhMX8DfZKPbNs5HgiEQY8ChQUPOKghstEjBrPpGFYCJ++NMPmWguDTzLxVAVuo2k+VOt+swA==
  dependencies:
    "@ckeditor/ckeditor5-ui" "39.0.2"
    ckeditor5 "39.0.2"
    lodash-es "4.17.21"

"@ckeditor/ckeditor5-indent@39.0.2":
  version "39.0.2"
  resolved "https://registry.npmjs.org/@ckeditor/ckeditor5-indent/-/ckeditor5-indent-39.0.2.tgz"
  integrity sha512-wU/1jBqgkK068fxkZDxnZM5iACunABXBVtpgfXqe2S6cMPVQyhiLzo955Yt7BhqN7Mkor59xESO2+Npj/vwB7w==
  dependencies:
    ckeditor5 "39.0.2"

"@ckeditor/ckeditor5-link@39.0.2":
  version "39.0.2"
  resolved "https://registry.npmjs.org/@ckeditor/ckeditor5-link/-/ckeditor5-link-39.0.2.tgz"
  integrity sha512-UKSamaD+yGaLPo/7pLS6bi+h2pRpX7qFSe4isVvmC8PfhN4YAm6ZYpIXpnktzfv2+/Jx6P9HzfjOnJPPT5PRdQ==
  dependencies:
    "@ckeditor/ckeditor5-ui" "39.0.2"
    ckeditor5 "39.0.2"
    lodash-es "4.17.21"

"@ckeditor/ckeditor5-list@39.0.2":
  version "39.0.2"
  resolved "https://registry.npmjs.org/@ckeditor/ckeditor5-list/-/ckeditor5-list-39.0.2.tgz"
  integrity sha512-XRcp67PuDK/mP0sL8g+TFLIr/MigVHKATwO+7OAJZlGAGxr73V/eRw3G6ZAe6Lh0lVQPRA54r+h5DHG5P+KRRg==
  dependencies:
    "@ckeditor/ckeditor5-ui" "39.0.2"
    ckeditor5 "39.0.2"

"@ckeditor/ckeditor5-media-embed@39.0.2":
  version "39.0.2"
  resolved "https://registry.npmjs.org/@ckeditor/ckeditor5-media-embed/-/ckeditor5-media-embed-39.0.2.tgz"
  integrity sha512-1QL+nYSTKlnTz9rMWeuW0/kvq8eCR0Yl0dJl+37uPJOrR6fQNxVcGq375D9f+WO45IJjOZ+7dtgxm19WK3R1cQ==
  dependencies:
    "@ckeditor/ckeditor5-ui" "39.0.2"
    ckeditor5 "39.0.2"

"@ckeditor/ckeditor5-paragraph@39.0.2":
  version "39.0.2"
  resolved "https://registry.npmjs.org/@ckeditor/ckeditor5-paragraph/-/ckeditor5-paragraph-39.0.2.tgz"
  integrity sha512-is0+G3ghz+6EwoNT5ciySiPt05FI0I/lxPksSDl4QG9BcGUwkN94fJalCxgXuMlGulojm/YZ3dCmJPnLMrAnUA==
  dependencies:
    "@ckeditor/ckeditor5-core" "39.0.2"
    "@ckeditor/ckeditor5-ui" "39.0.2"
    "@ckeditor/ckeditor5-utils" "39.0.2"

"@ckeditor/ckeditor5-paste-from-office@39.0.2":
  version "39.0.2"
  resolved "https://registry.npmjs.org/@ckeditor/ckeditor5-paste-from-office/-/ckeditor5-paste-from-office-39.0.2.tgz"
  integrity sha512-Y9O1C+9BpFCtvYI80AJzm/BkZWMedhreCaZcSCsEUUIo6vrIwehatTvgqWZJLwKZwL+4iy5/Kv9OfUhvO10I8Q==
  dependencies:
    ckeditor5 "39.0.2"

"@ckeditor/ckeditor5-react@^6.1.0":
  version "6.1.0"
  resolved "https://registry.npmjs.org/@ckeditor/ckeditor5-react/-/ckeditor5-react-6.1.0.tgz"
  integrity sha512-uwJ89pOJnjlMgfluGJhxatt6zV5ZavuV7AaRvJpxzAU7SjoC3sNumohDRmI0KGDGoyCVPMJO9fgE3ijIb3gHlQ==
  dependencies:
    prop-types "^15.7.2"

"@ckeditor/ckeditor5-select-all@39.0.2":
  version "39.0.2"
  resolved "https://registry.npmjs.org/@ckeditor/ckeditor5-select-all/-/ckeditor5-select-all-39.0.2.tgz"
  integrity sha512-/zTPZ14sfsL6E7LHcdOSW5JR7A5tsaSO2KNlQ/jm77XWSeQ7HZ4I7hoLXWksvcseubQ+xExWrzMQIjIOGqBLcQ==
  dependencies:
    "@ckeditor/ckeditor5-core" "39.0.2"
    "@ckeditor/ckeditor5-ui" "39.0.2"
    "@ckeditor/ckeditor5-utils" "39.0.2"

"@ckeditor/ckeditor5-table@39.0.2":
  version "39.0.2"
  resolved "https://registry.npmjs.org/@ckeditor/ckeditor5-table/-/ckeditor5-table-39.0.2.tgz"
  integrity sha512-IpI53KPEOj1F1vbMYUHLkOeoQoQV/q3pfiJxXXHZja1QaIEnqZT7YcSvJQbCyiuQw4UCZqU5x84c8GcyIZlBhg==
  dependencies:
    ckeditor5 "39.0.2"
    lodash-es "4.17.21"

"@ckeditor/ckeditor5-typing@39.0.2":
  version "39.0.2"
  resolved "https://registry.npmjs.org/@ckeditor/ckeditor5-typing/-/ckeditor5-typing-39.0.2.tgz"
  integrity sha512-ylkBumukIRe/8jQTanVYNcExvoraXX9TIX3POwiug2F+c/POKjoHgeq8+Wf/JEl7/ydtXZKT5tROqOe9haSteA==
  dependencies:
    "@ckeditor/ckeditor5-core" "39.0.2"
    "@ckeditor/ckeditor5-engine" "39.0.2"
    "@ckeditor/ckeditor5-utils" "39.0.2"
    lodash-es "4.17.21"

"@ckeditor/ckeditor5-ui@39.0.2":
  version "39.0.2"
  resolved "https://registry.npmjs.org/@ckeditor/ckeditor5-ui/-/ckeditor5-ui-39.0.2.tgz"
  integrity sha512-OHYasdPXG1Vy1tR5hcGeffeqg6ujWzCEbRczuU+0sC3ttYkxrnFk6qYe7gjA+BoqD61otk1au+rhCVux8dy0hQ==
  dependencies:
    "@ckeditor/ckeditor5-core" "39.0.2"
    "@ckeditor/ckeditor5-utils" "39.0.2"
    color-convert "2.0.1"
    color-parse "1.4.2"
    lodash-es "4.17.21"
    vanilla-colorful "0.7.2"

"@ckeditor/ckeditor5-undo@39.0.2":
  version "39.0.2"
  resolved "https://registry.npmjs.org/@ckeditor/ckeditor5-undo/-/ckeditor5-undo-39.0.2.tgz"
  integrity sha512-DSy7rRtnpVPpzZYOoQJNZ/6Zx4LEk3WVf8VG8wJDgpuxbRrE59DsNwVEqGEvDyVGlgzbuWZrfkkcL4hjcoYHCw==
  dependencies:
    "@ckeditor/ckeditor5-core" "39.0.2"
    "@ckeditor/ckeditor5-engine" "39.0.2"
    "@ckeditor/ckeditor5-ui" "39.0.2"

"@ckeditor/ckeditor5-upload@39.0.2":
  version "39.0.2"
  resolved "https://registry.npmjs.org/@ckeditor/ckeditor5-upload/-/ckeditor5-upload-39.0.2.tgz"
  integrity sha512-6LLU9lJDlnE4RT1GBzK1V6gsIv/+4NiWVojblzET9AlyMI4BRTn3/PPxZRWyPPKH96AeuIWoyj32D/a24yQujg==
  dependencies:
    "@ckeditor/ckeditor5-core" "39.0.2"
    "@ckeditor/ckeditor5-ui" "39.0.2"
    "@ckeditor/ckeditor5-utils" "39.0.2"

"@ckeditor/ckeditor5-utils@>=37.0.0", "@ckeditor/ckeditor5-utils@39.0.2":
  version "39.0.2"
  resolved "https://registry.npmjs.org/@ckeditor/ckeditor5-utils/-/ckeditor5-utils-39.0.2.tgz"
  integrity sha512-aqiGhPJxEihSLW21lGWcAvjVTTwJYxEbfMk1eLf/BEY3euy6iltRC6EqbXkyJDcKGU7cQtk6JXAIkH+D2FF87g==
  dependencies:
    lodash-es "4.17.21"

"@ckeditor/ckeditor5-watchdog@>=37.0.0", "@ckeditor/ckeditor5-watchdog@39.0.2":
  version "39.0.2"
  resolved "https://registry.npmjs.org/@ckeditor/ckeditor5-watchdog/-/ckeditor5-watchdog-39.0.2.tgz"
  integrity sha512-9E7BNp9c+nj/01JWF4lBVcDwJI2lN/Vgerw+ex4l/nTRl7crLzckSy3DWhYlpQPjJzsrkFjGQ+zCG4C/Vevl3Q==
  dependencies:
    lodash-es "4.17.21"

"@ckeditor/ckeditor5-widget@39.0.2":
  version "39.0.2"
  resolved "https://registry.npmjs.org/@ckeditor/ckeditor5-widget/-/ckeditor5-widget-39.0.2.tgz"
  integrity sha512-bGs8iKD3c+r3/JCqhXoh/MPJut0JcdUdxDSgS+xYu1UJ03b/K39/0QbdVjT1I6oNRSDb4IUwBO2Xc3zTMl/qTw==
  dependencies:
    "@ckeditor/ckeditor5-core" "39.0.2"
    "@ckeditor/ckeditor5-engine" "39.0.2"
    "@ckeditor/ckeditor5-enter" "39.0.2"
    "@ckeditor/ckeditor5-typing" "39.0.2"
    "@ckeditor/ckeditor5-ui" "39.0.2"
    "@ckeditor/ckeditor5-utils" "39.0.2"
    lodash-es "4.17.21"

"@date-io/core@^2.17.0":
  version "2.17.0"
  resolved "https://registry.npmjs.org/@date-io/core/-/core-2.17.0.tgz"
  integrity sha512-+EQE8xZhRM/hsY0CDTVyayMDDY5ihc4MqXCrPxooKw19yAzUIC6uUqsZeaOFNL9YKTNxYKrJP5DFgE8o5xRCOw==

"@date-io/date-fns@^2.11.0":
  version "2.17.0"
  resolved "https://registry.npmjs.org/@date-io/date-fns/-/date-fns-2.17.0.tgz"
  integrity sha512-L0hWZ/mTpy3Gx/xXJ5tq5CzHo0L7ry6KEO9/w/JWiFWFLZgiNVo3ex92gOl3zmzjHqY/3Ev+5sehAr8UnGLEng==
  dependencies:
    "@date-io/core" "^2.17.0"

"@date-io/dayjs@^2.11.0":
  version "2.17.0"
  resolved "https://registry.npmjs.org/@date-io/dayjs/-/dayjs-2.17.0.tgz"
  integrity sha512-Iq1wjY5XzBh0lheFA0it6Dsyv94e8mTiNR8vuTai+KopxDkreL3YjwTmZHxkgB7/vd0RMIACStzVgWvPATnDCA==
  dependencies:
    "@date-io/core" "^2.17.0"

"@date-io/luxon@^2.11.1":
  version "2.17.0"
  resolved "https://registry.npmjs.org/@date-io/luxon/-/luxon-2.17.0.tgz"
  integrity sha512-l712Vdm/uTddD2XWt9TlQloZUiTiRQtY5TCOG45MQ/8u0tu8M17BD6QYHar/3OrnkGybALAMPzCy1r5D7+0HBg==
  dependencies:
    "@date-io/core" "^2.17.0"

"@date-io/moment@^2.11.0":
  version "2.17.0"
  resolved "https://registry.npmjs.org/@date-io/moment/-/moment-2.17.0.tgz"
  integrity sha512-e4nb4CDZU4k0WRVhz1Wvl7d+hFsedObSauDHKtZwU9kt7gdYEAzKgnrSCTHsEaXrDumdrkCYTeZ0Tmyk7uV4tw==
  dependencies:
    "@date-io/core" "^2.17.0"

"@emotion/babel-plugin@^11.7.1":
  version "11.11.0"
  resolved "https://registry.npmjs.org/@emotion/babel-plugin/-/babel-plugin-11.11.0.tgz"
  integrity sha512-m4HEDZleaaCH+XgDDsPF15Ht6wTLsgDTeR3WYj9Q/k76JtWhrJjcP4+/XlG8LGT/Rol9qUfOIztXeA84ATpqPQ==
  dependencies:
    "@babel/helper-module-imports" "^7.16.7"
    "@babel/runtime" "^7.18.3"
    "@emotion/hash" "^0.9.1"
    "@emotion/memoize" "^0.8.1"
    "@emotion/serialize" "^1.1.2"
    babel-plugin-macros "^3.1.0"
    convert-source-map "^1.5.0"
    escape-string-regexp "^4.0.0"
    find-root "^1.1.0"
    source-map "^0.5.7"
    stylis "4.2.0"

"@emotion/cache@^11.11.0":
  version "11.11.0"
  resolved "https://registry.npmjs.org/@emotion/cache/-/cache-11.11.0.tgz"
  integrity sha512-P34z9ssTCBi3e9EI1ZsWpNHcfY1r09ZO0rZbRO2ob3ZQMnFI35jB536qoXbkdesr5EUhYi22anuEJuyxifaqAQ==
  dependencies:
    "@emotion/memoize" "^0.8.1"
    "@emotion/sheet" "^1.2.2"
    "@emotion/utils" "^1.2.1"
    "@emotion/weak-memoize" "^0.3.1"
    stylis "4.2.0"

"@emotion/cache@^11.7.1":
  version "11.11.0"
  resolved "https://registry.npmjs.org/@emotion/cache/-/cache-11.11.0.tgz"
  integrity sha512-P34z9ssTCBi3e9EI1ZsWpNHcfY1r09ZO0rZbRO2ob3ZQMnFI35jB536qoXbkdesr5EUhYi22anuEJuyxifaqAQ==
  dependencies:
    "@emotion/memoize" "^0.8.1"
    "@emotion/sheet" "^1.2.2"
    "@emotion/utils" "^1.2.1"
    "@emotion/weak-memoize" "^0.3.1"
    stylis "4.2.0"

"@emotion/cache@11.7.1":
  version "11.7.1"
  resolved "https://registry.npmjs.org/@emotion/cache/-/cache-11.7.1.tgz"
  integrity sha512-r65Zy4Iljb8oyjtLeCuBH8Qjiy107dOYC6SJq7g7GV5UCQWMObY4SJDPGFjiiVpPrOJ2hmJOoBiYTC7hwx9E2A==
  dependencies:
    "@emotion/memoize" "^0.7.4"
    "@emotion/sheet" "^1.1.0"
    "@emotion/utils" "^1.0.0"
    "@emotion/weak-memoize" "^0.2.5"
    stylis "4.0.13"

"@emotion/hash@^0.8.0":
  version "0.8.0"
  resolved "https://registry.npmjs.org/@emotion/hash/-/hash-0.8.0.tgz"
  integrity sha512-kBJtf7PH6aWwZ6fka3zQ0p6SBYzx4fl1LoZXE2RrnYST9Xljm7WfKJrU4g/Xr3Beg72MLrp1AWNUmuYJTL7Cow==

"@emotion/hash@^0.9.1":
  version "0.9.1"
  resolved "https://registry.npmjs.org/@emotion/hash/-/hash-0.9.1.tgz"
  integrity sha512-gJB6HLm5rYwSLI6PQa+X1t5CFGrv1J1TWG+sOyMCeKz2ojaj6Fnl/rZEspogG+cvqbt4AE/2eIyD2QfLKTBNlQ==

"@emotion/is-prop-valid@^0.8.2":
  version "0.8.8"
  resolved "https://registry.npmjs.org/@emotion/is-prop-valid/-/is-prop-valid-0.8.8.tgz"
  integrity sha512-u5WtneEAr5IDG2Wv65yhunPSMLIpuKsbuOktRojfrEiEvRyC85LgPMZI63cr7NUqT8ZIGdSVg8ZKGxIug4lXcA==
  dependencies:
    "@emotion/memoize" "0.7.4"

"@emotion/is-prop-valid@^1.1.0", "@emotion/is-prop-valid@^1.1.2":
  version "1.2.1"
  resolved "https://registry.npmjs.org/@emotion/is-prop-valid/-/is-prop-valid-1.2.1.tgz"
  integrity sha512-61Mf7Ufx4aDxx1xlDeOm8aFFigGHE4z+0sKCa+IHCeZKiyP9RLD0Mmx7m8b9/Cf37f7NAvQOOJAbQQGVr5uERw==
  dependencies:
    "@emotion/memoize" "^0.8.1"

"@emotion/memoize@^0.7.4":
  version "0.7.5"
  resolved "https://registry.npmjs.org/@emotion/memoize/-/memoize-0.7.5.tgz"
  integrity sha512-igX9a37DR2ZPGYtV6suZ6whr8pTFtyHL3K/oLUotxpSVO2ASaprmAe2Dkq7tBo7CRY7MMDrAa9nuQP9/YG8FxQ==

"@emotion/memoize@^0.8.1":
  version "0.8.1"
  resolved "https://registry.npmjs.org/@emotion/memoize/-/memoize-0.8.1.tgz"
  integrity sha512-W2P2c/VRW1/1tLox0mVUalvnWXxavmv/Oum2aPsRcoDJuob75FC3Y8FbpfLwUegRcxINtGUMPq0tFCvYNTBXNA==

"@emotion/memoize@0.7.4":
  version "0.7.4"
  resolved "https://registry.npmjs.org/@emotion/memoize/-/memoize-0.7.4.tgz"
  integrity sha512-Ja/Vfqe3HpuzRsG1oBtWTHk2PGZ7GR+2Vz5iYGelAw8dx32K0y7PjVuxK6z1nMpZOqAFsRUPCkK1YjJ56qJlgw==

"@emotion/react@^11.0.0-rc.0", "@emotion/react@^11.4.1", "@emotion/react@^11.5.0", "@emotion/react@11.9.0":
  version "11.9.0"
  resolved "https://registry.npmjs.org/@emotion/react/-/react-11.9.0.tgz"
  integrity sha512-lBVSF5d0ceKtfKCDQJveNAtkC7ayxpVlgOohLgXqRwqWr9bOf4TZAFFyIcNngnV6xK6X4x2ZeXq7vliHkoVkxQ==
  dependencies:
    "@babel/runtime" "^7.13.10"
    "@emotion/babel-plugin" "^11.7.1"
    "@emotion/cache" "^11.7.1"
    "@emotion/serialize" "^1.0.3"
    "@emotion/utils" "^1.1.0"
    "@emotion/weak-memoize" "^0.2.5"
    hoist-non-react-statics "^3.3.1"

"@emotion/serialize@^1.0.2", "@emotion/serialize@^1.0.3", "@emotion/serialize@^1.1.2":
  version "1.1.2"
  resolved "https://registry.npmjs.org/@emotion/serialize/-/serialize-1.1.2.tgz"
  integrity sha512-zR6a/fkFP4EAcCMQtLOhIgpprZOwNmCldtpaISpvz348+DP4Mz8ZoKaGGCQpbzepNIUWbq4w6hNZkwDyKoS+HA==
  dependencies:
    "@emotion/hash" "^0.9.1"
    "@emotion/memoize" "^0.8.1"
    "@emotion/unitless" "^0.8.1"
    "@emotion/utils" "^1.2.1"
    csstype "^3.0.2"

"@emotion/server@11.4.0":
  version "11.4.0"
  resolved "https://registry.npmjs.org/@emotion/server/-/server-11.4.0.tgz"
  integrity sha512-IHovdWA3V0DokzxLtUNDx4+hQI82zUXqQFcVz/om2t44O0YSc+NHB+qifnyAOoQwt3SXcBTgaSntobwUI9gnfA==
  dependencies:
    "@emotion/utils" "^1.0.0"
    html-tokenize "^2.0.0"
    multipipe "^1.0.2"
    through "^2.3.8"

"@emotion/sheet@^1.1.0", "@emotion/sheet@^1.2.2":
  version "1.2.2"
  resolved "https://registry.npmjs.org/@emotion/sheet/-/sheet-1.2.2.tgz"
  integrity sha512-0QBtGvaqtWi+nx6doRwDdBIzhNdZrXUppvTM4dtZZWEGTXL/XE/yJxLMGlDT1Gt+UHH5IX1n+jkXyytE/av7OA==

"@emotion/styled@^11.3.0", "@emotion/styled@11.8.1":
  version "11.8.1"
  resolved "https://registry.npmjs.org/@emotion/styled/-/styled-11.8.1.tgz"
  integrity sha512-OghEVAYBZMpEquHZwuelXcRjRJQOVayvbmNR0zr174NHdmMgrNkLC6TljKC5h9lZLkN5WGrdUcrKlOJ4phhoTQ==
  dependencies:
    "@babel/runtime" "^7.13.10"
    "@emotion/babel-plugin" "^11.7.1"
    "@emotion/is-prop-valid" "^1.1.2"
    "@emotion/serialize" "^1.0.2"
    "@emotion/utils" "^1.1.0"

"@emotion/unitless@^0.8.1":
  version "0.8.1"
  resolved "https://registry.npmjs.org/@emotion/unitless/-/unitless-0.8.1.tgz"
  integrity sha512-KOEGMu6dmJZtpadb476IsZBclKvILjopjUii3V+7MnXIQCYh8W3NgNcgwo21n9LXZX6EDIKvqfjYxXebDwxKmQ==

"@emotion/utils@^1.0.0", "@emotion/utils@^1.1.0", "@emotion/utils@^1.2.1":
  version "1.2.1"
  resolved "https://registry.npmjs.org/@emotion/utils/-/utils-1.2.1.tgz"
  integrity sha512-Y2tGf3I+XVnajdItskUCn6LX+VUDmP6lTL4fcqsXAv43dnlbZiuW4MWQW38rW/BVWSE7Q/7+XQocmpnRYILUmg==

"@emotion/weak-memoize@^0.2.5":
  version "0.2.5"
  resolved "https://registry.npmjs.org/@emotion/weak-memoize/-/weak-memoize-0.2.5.tgz"
  integrity sha512-6U71C2Wp7r5XtFtQzYrW5iKFT67OixrSxjI4MptCHzdSVlgabczzqLe0ZSgnub/5Kp4hSbpDB1tMytZY9pwxxA==

"@emotion/weak-memoize@^0.3.1":
  version "0.3.1"
  resolved "https://registry.npmjs.org/@emotion/weak-memoize/-/weak-memoize-0.3.1.tgz"
  integrity sha512-EsBwpc7hBUJWAsNPBmJy4hxWx12v6bshQsldrVmjxJoc3isbxhOrF2IcCpaXxfvq03NwkI7sbsOLXbYuqF/8Ww==

"@eslint/eslintrc@^1.3.0":
  version "1.4.1"
  resolved "https://registry.npmjs.org/@eslint/eslintrc/-/eslintrc-1.4.1.tgz"
  integrity sha512-XXrH9Uarn0stsyldqDYq8r++mROmWRI1xKMXa640Bb//SY1+ECYX6VzT6Lcx5frD0V30XieqJ0oX9I2Xj5aoMA==
  dependencies:
    ajv "^6.12.4"
    debug "^4.3.2"
    espree "^9.4.0"
    globals "^13.19.0"
    ignore "^5.2.0"
    import-fresh "^3.2.1"
    js-yaml "^4.1.0"
    minimatch "^3.1.2"
    strip-json-comments "^3.1.1"

"@formatjs/ecma402-abstract@2.2.4":
  version "2.2.4"
  resolved "https://registry.npmjs.org/@formatjs/ecma402-abstract/-/ecma402-abstract-2.2.4.tgz"
  integrity sha512-lFyiQDVvSbQOpU+WFd//ILolGj4UgA/qXrKeZxdV14uKiAUiPAtX6XAn7WBCRi7Mx6I7EybM9E5yYn4BIpZWYg==
  dependencies:
    "@formatjs/fast-memoize" "2.2.3"
    "@formatjs/intl-localematcher" "0.5.8"
    tslib "2"

"@formatjs/fast-memoize@2.2.3":
  version "2.2.3"
  resolved "https://registry.npmjs.org/@formatjs/fast-memoize/-/fast-memoize-2.2.3.tgz"
  integrity sha512-3jeJ+HyOfu8osl3GNSL4vVHUuWFXR03Iz9jjgI7RwjG6ysu/Ymdr0JRCPHfF5yGbTE6JCrd63EpvX1/WybYRbA==
  dependencies:
    tslib "2"

"@formatjs/icu-messageformat-parser@2.9.4":
  version "2.9.4"
  resolved "https://registry.npmjs.org/@formatjs/icu-messageformat-parser/-/icu-messageformat-parser-2.9.4.tgz"
  integrity sha512-Tbvp5a9IWuxUcpWNIW6GlMQYEc4rwNHR259uUFoKWNN1jM9obf9Ul0e+7r7MvFOBNcN+13K7NuKCKqQiAn1QEg==
  dependencies:
    "@formatjs/ecma402-abstract" "2.2.4"
    "@formatjs/icu-skeleton-parser" "1.8.8"
    tslib "2"

"@formatjs/icu-skeleton-parser@1.8.8":
  version "1.8.8"
  resolved "https://registry.npmjs.org/@formatjs/icu-skeleton-parser/-/icu-skeleton-parser-1.8.8.tgz"
  integrity sha512-vHwK3piXwamFcx5YQdCdJxUQ1WdTl6ANclt5xba5zLGDv5Bsur7qz8AD7BevaKxITwpgDeU0u8My3AIibW9ywA==
  dependencies:
    "@formatjs/ecma402-abstract" "2.2.4"
    tslib "2"

"@formatjs/intl-localematcher@0.5.8":
  version "0.5.8"
  resolved "https://registry.npmjs.org/@formatjs/intl-localematcher/-/intl-localematcher-0.5.8.tgz"
  integrity sha512-I+WDNWWJFZie+jkfkiK5Mp4hEDyRSEvmyfYadflOno/mmKJKcB17fEpEH0oJu/OWhhCJ8kJBDz2YMd/6cDl7Mg==
  dependencies:
    tslib "2"

"@humanwhocodes/config-array@^0.9.2":
  version "0.9.5"
  resolved "https://registry.npmjs.org/@humanwhocodes/config-array/-/config-array-0.9.5.tgz"
  integrity sha512-ObyMyWxZiCu/yTisA7uzx81s40xR2fD5Cg/2Kq7G02ajkNubJf6BopgDTmDyc3U7sXpNKM8cYOw7s7Tyr+DnCw==
  dependencies:
    "@humanwhocodes/object-schema" "^1.2.1"
    debug "^4.1.1"
    minimatch "^3.0.4"

"@humanwhocodes/object-schema@^1.2.1":
  version "1.2.1"
  resolved "https://registry.npmjs.org/@humanwhocodes/object-schema/-/object-schema-1.2.1.tgz"
  integrity sha512-ZnQMnLV4e7hDlUvw8H+U8ASL02SS2Gn6+9Ac3wGGLIe7+je2AeAOxPY+izIPJDfFDb7eDjev0Us8MO1iFRN8hA==

"@internationalized/date@^3.5.4", "@internationalized/date@^3.6.0":
  version "3.6.0"
  resolved "https://registry.npmjs.org/@internationalized/date/-/date-3.6.0.tgz"
  integrity sha512-+z6ti+CcJnRlLHok/emGEsWQhe7kfSmEW+/6qCzvKY67YPh7YOBfvc7+/+NXq+zJlbArg30tYpqLjNgcAYv2YQ==
  dependencies:
    "@swc/helpers" "^0.5.0"

"@internationalized/message@^3.1.4", "@internationalized/message@^3.1.6":
  version "3.1.6"
  resolved "https://registry.npmjs.org/@internationalized/message/-/message-3.1.6.tgz"
  integrity sha512-JxbK3iAcTIeNr1p0WIFg/wQJjIzJt9l/2KNY/48vXV7GRGZSv3zMxJsce008fZclk2cDC8y0Ig3odceHO7EfNQ==
  dependencies:
    "@swc/helpers" "^0.5.0"
    intl-messageformat "^10.1.0"

"@internationalized/number@^3.5.3", "@internationalized/number@^3.6.0":
  version "3.6.0"
  resolved "https://registry.npmjs.org/@internationalized/number/-/number-3.6.0.tgz"
  integrity sha512-PtrRcJVy7nw++wn4W2OuePQQfTqDzfusSuY1QTtui4wa7r+rGVtR75pO8CyKvHvzyQYi3Q1uO5sY0AsB4e65Bw==
  dependencies:
    "@swc/helpers" "^0.5.0"

"@internationalized/string@^3.2.3", "@internationalized/string@^3.2.5":
  version "3.2.5"
  resolved "https://registry.npmjs.org/@internationalized/string/-/string-3.2.5.tgz"
  integrity sha512-rKs71Zvl2OKOHM+mzAFMIyqR5hI1d1O6BBkMK2/lkfg3fkmVh9Eeg0awcA8W2WqYqDOv6a86DIOlFpggwLtbuw==
  dependencies:
    "@swc/helpers" "^0.5.0"

"@isaacs/cliui@^8.0.2":
  version "8.0.2"
  resolved "https://registry.npmjs.org/@isaacs/cliui/-/cliui-8.0.2.tgz"
  integrity sha512-O8jcjabXaleOG9DQ0+ARXWZBTfnP4WNAqzuiJK7ll44AmxGKv/J2M4TPjxjY3znBCfvBXFzucm1twdyFybFqEA==
  dependencies:
    string-width "^5.1.2"
    string-width-cjs "npm:string-width@^4.2.0"
    strip-ansi "^7.0.1"
    strip-ansi-cjs "npm:strip-ansi@^6.0.1"
    wrap-ansi "^8.1.0"
    wrap-ansi-cjs "npm:wrap-ansi@^7.0.0"

"@jridgewell/gen-mapping@^0.3.2", "@jridgewell/gen-mapping@^0.3.5":
  version "0.3.5"
  resolved "https://registry.npmjs.org/@jridgewell/gen-mapping/-/gen-mapping-0.3.5.tgz"
  integrity sha512-IzL8ZoEDIBRWEzlCcRhOaCupYyN5gdIK+Q6fbFdPDg6HqX6jpkItn7DFIpW9LQzXG6Df9sA7+OKnq0qlz/GaQg==
  dependencies:
    "@jridgewell/set-array" "^1.2.1"
    "@jridgewell/sourcemap-codec" "^1.4.10"
    "@jridgewell/trace-mapping" "^0.3.24"

"@jridgewell/resolve-uri@^3.1.0":
  version "3.1.2"
  resolved "https://registry.npmjs.org/@jridgewell/resolve-uri/-/resolve-uri-3.1.2.tgz"
  integrity sha512-bRISgCIjP20/tbWSPWMEi54QVPRZExkuD9lJL+UIxUKtwVJA8wW1Trb1jMs1RFXo1CBTNZ/5hpC9QvmKWdopKw==

"@jridgewell/set-array@^1.2.1":
  version "1.2.1"
  resolved "https://registry.npmjs.org/@jridgewell/set-array/-/set-array-1.2.1.tgz"
  integrity sha512-R8gLRTZeyp03ymzP/6Lil/28tGeGEzhx1q2k703KGWRAI1VdvPIXdG70VJc2pAMw3NA6JKL5hhFu1sJX0Mnn/A==

"@jridgewell/source-map@^0.3.3":
  version "0.3.6"
  resolved "https://registry.npmjs.org/@jridgewell/source-map/-/source-map-0.3.6.tgz"
  integrity sha512-1ZJTZebgqllO79ue2bm3rIGud/bOe0pP5BjSRCRxxYkEZS8STV7zN84UBbiYu7jy+eCKSnVIUgoWWE/tt+shMQ==
  dependencies:
    "@jridgewell/gen-mapping" "^0.3.5"
    "@jridgewell/trace-mapping" "^0.3.25"

"@jridgewell/sourcemap-codec@^1.4.10", "@jridgewell/sourcemap-codec@^1.4.14":
  version "1.5.0"
  resolved "https://registry.npmjs.org/@jridgewell/sourcemap-codec/-/sourcemap-codec-1.5.0.tgz"
  integrity sha512-gv3ZRaISU3fjPAgNsriBRqGWQL6quFx04YMPW/zD8XMLsU32mhCCbfbO6KZFLjvYpCZ8zyDEgqsgf+PwPaM7GQ==

"@jridgewell/trace-mapping@^0.3.20", "@jridgewell/trace-mapping@^0.3.24", "@jridgewell/trace-mapping@^0.3.25":
  version "0.3.25"
  resolved "https://registry.npmjs.org/@jridgewell/trace-mapping/-/trace-mapping-0.3.25.tgz"
  integrity sha512-vNk6aEwybGtawWmy/PzwnGDOjCkLWSD2wqvjGGAgOAwCGWySYXfYoxt00IJkTF+8Lb57DwOb3Aa0o9CApepiYQ==
  dependencies:
    "@jridgewell/resolve-uri" "^3.1.0"
    "@jridgewell/sourcemap-codec" "^1.4.14"

"@juggle/resize-observer@^3.4.0":
  version "3.4.0"
  resolved "https://registry.npmjs.org/@juggle/resize-observer/-/resize-observer-3.4.0.tgz"
  integrity sha512-dfLbk+PwWvFzSxwk3n5ySL0hfBog779o8h68wK/7/APo/7cgyWp5jcXockbxdk5kFRkbeXWm4Fbi9FrdN381sA==

"@mapbox/node-pre-gyp@^1.0.11":
  version "1.0.11"
  resolved "https://registry.npmjs.org/@mapbox/node-pre-gyp/-/node-pre-gyp-1.0.11.tgz"
  integrity sha512-Yhlar6v9WQgUp/He7BdgzOz8lqMQ8sU+jkCq7Wx8Myc5YFJLbEe7lgui/V7G1qB1DJykHSGwreceSaD60Y0PUQ==
  dependencies:
    detect-libc "^2.0.0"
    https-proxy-agent "^5.0.0"
    make-dir "^3.1.0"
    node-fetch "^2.6.7"
    nopt "^5.0.0"
    npmlog "^5.0.1"
    rimraf "^3.0.2"
    semver "^7.3.5"
    tar "^6.1.11"

"@mui/base@5.0.0-alpha.83":
  version "5.0.0-alpha.83"
  resolved "https://registry.npmjs.org/@mui/base/-/base-5.0.0-alpha.83.tgz"
  integrity sha512-/bFcjiI36R2Epf2Y3BkZOIdxrz5uMLqOU4cRai4igJ8DHTRMZDeKbOff0SdvwJNwg8r6oPUyoeOpsWkaOOX9/g==
  dependencies:
    "@babel/runtime" "^7.17.2"
    "@emotion/is-prop-valid" "^1.1.2"
    "@mui/types" "^7.1.3"
    "@mui/utils" "^5.8.0"
    "@popperjs/core" "^2.11.5"
    clsx "^1.1.1"
    prop-types "^15.8.1"
    react-is "^17.0.2"

"@mui/core@5.0.0-alpha.54":
  version "5.0.0-alpha.54"
  resolved "https://registry.npmjs.org/@mui/core/-/core-5.0.0-alpha.54.tgz"
  integrity sha512-8TxdHqDdSb6wjhsnpE5n7qtkFKDG3PUSlVY0gR3VcdsHXscUY13l3VbMQW1brI4D/R9zx5VYmxIHWaHFgw4RtA==
  dependencies:
    "@babel/runtime" "^7.16.0"
    "@emotion/is-prop-valid" "^1.1.0"
    "@mui/utils" "^5.1.0"
    "@popperjs/core" "^2.4.4"
    clsx "^1.1.1"
    prop-types "^15.7.2"
    react-is "^17.0.2"

"@mui/icons-material@5.8.2":
  version "5.8.2"
  resolved "https://registry.npmjs.org/@mui/icons-material/-/icons-material-5.8.2.tgz"
  integrity sha512-fP6KUCCZZjc2rdbMSmkNmBHDskLkmP0uCox57cbVXvomU6BOPrCxnr5YXsSsQrZB8fchx7hfH0bkAgvMZ5KM0Q==
  dependencies:
    "@babel/runtime" "^7.17.2"

"@mui/lab@5.0.0-alpha.84":
  version "5.0.0-alpha.84"
  resolved "https://registry.npmjs.org/@mui/lab/-/lab-5.0.0-alpha.84.tgz"
  integrity sha512-HLYD6E3PAlzKMGZkkpiPI7trHP3WYDvrjQstEsFwdaGy9AMWPmyTxhwUyfB4VVHOx3zcj4p/a36kECDtEOAJ+g==
  dependencies:
    "@babel/runtime" "^7.17.2"
    "@mui/base" "5.0.0-alpha.83"
    "@mui/system" "^5.8.2"
    "@mui/utils" "^5.8.0"
    "@mui/x-date-pickers" "5.0.0-alpha.1"
    clsx "^1.1.1"
    prop-types "^15.8.1"
    react-is "^17.0.2"
    react-transition-group "^4.4.2"
    rifm "^0.12.1"

"@mui/material@^5.0.0", "@mui/material@^5.2.3", "@mui/material@5.8.2":
  version "5.8.2"
  resolved "https://registry.npmjs.org/@mui/material/-/material-5.8.2.tgz"
  integrity sha512-w/A1KG9Czf42uTyJOiRU5U1VullOz1R3xcsBvv3BtKCCWdVP+D6v/Yb8v0tJpIixMEbjeWzWGjotQBU0nd+yNA==
  dependencies:
    "@babel/runtime" "^7.17.2"
    "@mui/base" "5.0.0-alpha.83"
    "@mui/system" "^5.8.2"
    "@mui/types" "^7.1.3"
    "@mui/utils" "^5.8.0"
    "@types/react-transition-group" "^4.4.4"
    clsx "^1.1.1"
    csstype "^3.1.0"
    hoist-non-react-statics "^3.3.2"
    prop-types "^15.8.1"
    react-is "^17.0.2"
    react-transition-group "^4.4.2"

"@mui/private-theming@^5.14.15", "@mui/private-theming@^5.8.0":
  version "5.14.15"
  resolved "https://registry.npmjs.org/@mui/private-theming/-/private-theming-5.14.15.tgz"
  integrity sha512-V2Xh+Tu6A07NoSpup0P9m29GwvNMYl5DegsGWqlOTJyAV7cuuVjmVPqxgvL8xBng4R85xqIQJRMjtYYktoPNuQ==
  dependencies:
    "@babel/runtime" "^7.23.2"
    "@mui/utils" "^5.14.15"
    prop-types "^15.8.1"

"@mui/styled-engine@^5.14.15":
  version "5.14.15"
  resolved "https://registry.npmjs.org/@mui/styled-engine/-/styled-engine-5.14.15.tgz"
  integrity sha512-mbOjRf867BysNpexe5Z/P8s3bWzDPNowmKhi7gtNDP/LPEeqAfiDSuC4WPTXmtvse1dCl30Nl755OLUYuoi7Mw==
  dependencies:
    "@babel/runtime" "^7.23.2"
    "@emotion/cache" "^11.11.0"
    csstype "^3.1.2"
    prop-types "^15.8.1"

"@mui/styles@5.8.0":
  version "5.8.0"
  resolved "https://registry.npmjs.org/@mui/styles/-/styles-5.8.0.tgz"
  integrity sha512-kssqBWhCFo7AMiu93QziR8qxq3z8bJm2qCYdmNadE5NvEla7Xwu3twNJUWnCEoeDeoFMk/2H0ACMOdrasI9ANw==
  dependencies:
    "@babel/runtime" "^7.17.2"
    "@emotion/hash" "^0.8.0"
    "@mui/private-theming" "^5.8.0"
    "@mui/types" "^7.1.3"
    "@mui/utils" "^5.8.0"
    clsx "^1.1.1"
    csstype "^3.0.11"
    hoist-non-react-statics "^3.3.2"
    jss "^10.8.2"
    jss-plugin-camel-case "^10.8.2"
    jss-plugin-default-unit "^10.8.2"
    jss-plugin-global "^10.8.2"
    jss-plugin-nested "^10.8.2"
    jss-plugin-props-sort "^10.8.2"
    jss-plugin-rule-value-function "^10.8.2"
    jss-plugin-vendor-prefixer "^10.8.2"
    prop-types "^15.8.1"

"@mui/system@^5.2.3", "@mui/system@^5.8.2":
  version "5.14.15"
  resolved "https://registry.npmjs.org/@mui/system/-/system-5.14.15.tgz"
  integrity sha512-zr0Gdk1RgKiEk+tCMB900LaOpEC8NaGvxtkmMdL/CXgkqQZSVZOt2PQsxJWaw7kE4YVkIe4VukFVc43qcq9u3w==
  dependencies:
    "@babel/runtime" "^7.23.2"
    "@mui/private-theming" "^5.14.15"
    "@mui/styled-engine" "^5.14.15"
    "@mui/types" "^7.2.7"
    "@mui/utils" "^5.14.15"
    clsx "^2.0.0"
    csstype "^3.1.2"
    prop-types "^15.8.1"

"@mui/types@^7.1.3", "@mui/types@^7.2.7":
  version "7.2.7"
  resolved "https://registry.npmjs.org/@mui/types/-/types-7.2.7.tgz"
  integrity sha512-sofpWmcBqOlTzRbr1cLQuUDKaUYVZTw8ENQrtL39TECRNENEzwgnNPh6WMfqMZlMvf1Aj9DLg74XPjnLr0izUQ==

"@mui/utils@^5.1.0", "@mui/utils@^5.14.15", "@mui/utils@^5.6.0", "@mui/utils@^5.8.0":
  version "5.14.15"
  resolved "https://registry.npmjs.org/@mui/utils/-/utils-5.14.15.tgz"
  integrity sha512-QBfHovAvTa0J1jXuYDaXGk+Yyp7+Fm8GSqx6nK2JbezGqzCFfirNdop/+bL9Flh/OQ/64PeXcW4HGDdOge+n3A==
  dependencies:
    "@babel/runtime" "^7.23.2"
    "@types/prop-types" "^15.7.8"
    prop-types "^15.8.1"
    react-is "^18.2.0"

"@mui/x-date-pickers@5.0.0-alpha.1":
  version "5.0.0-alpha.1"
  resolved "https://registry.npmjs.org/@mui/x-date-pickers/-/x-date-pickers-5.0.0-alpha.1.tgz"
  integrity sha512-dLPkRiIn2Gr0momblxiOnIwrxn4SijVix+8e08mwAGWhiWcmWep1O9XTRDpZsjB0kjHYCf+kZjlRX4dxnj2acg==
  dependencies:
    "@date-io/date-fns" "^2.11.0"
    "@date-io/dayjs" "^2.11.0"
    "@date-io/luxon" "^2.11.1"
    "@date-io/moment" "^2.11.0"
    "@mui/utils" "^5.6.0"
    clsx "^1.1.1"
    prop-types "^15.7.2"
    react-transition-group "^4.4.2"
    rifm "^0.12.1"

"@next/env@12.1.6":
  version "12.1.6"
  resolved "https://registry.npmjs.org/@next/env/-/env-12.1.6.tgz"
  integrity sha512-Te/OBDXFSodPU6jlXYPAXpmZr/AkG6DCATAxttQxqOWaq6eDFX25Db3dK0120GZrSZmv4QCe9KsZmJKDbWs4OA==

"@next/eslint-plugin-next@12.1.6":
  version "12.1.6"
  resolved "https://registry.npmjs.org/@next/eslint-plugin-next/-/eslint-plugin-next-12.1.6.tgz"
  integrity sha512-yNUtJ90NEiYFT6TJnNyofKMPYqirKDwpahcbxBgSIuABwYOdkGwzos1ZkYD51Qf0diYwpQZBeVqElTk7Q2WNqw==
  dependencies:
    glob "7.1.7"

"@next/swc-win32-x64-msvc@12.1.6":
  version "12.1.6"
  resolved "https://registry.npmjs.org/@next/swc-win32-x64-msvc/-/swc-win32-x64-msvc-12.1.6.tgz"
  integrity sha512-4ZEwiRuZEicXhXqmhw3+de8Z4EpOLQj/gp+D9fFWo6ii6W1kBkNNvvEx4A90ugppu+74pT1lIJnOuz3A9oQeJA==

"@nextui-org/accordion@2.0.40":
  version "2.0.40"
  resolved "https://registry.npmjs.org/@nextui-org/accordion/-/accordion-2.0.40.tgz"
  integrity sha512-aJmhflLOXOFTjbBWlWto30hYzimw+sw1EZwSRG9CdxbjRact2dRfCLsZQmHkJW2ifVx51g/qLNE2NSFAi2L8dA==
  dependencies:
    "@nextui-org/aria-utils" "2.0.26"
    "@nextui-org/divider" "2.0.32"
    "@nextui-org/framer-utils" "2.0.25"
    "@nextui-org/react-utils" "2.0.17"
    "@nextui-org/shared-icons" "2.0.9"
    "@nextui-org/shared-utils" "2.0.8"
    "@nextui-org/use-aria-accordion" "2.0.7"
    "@react-aria/button" "3.9.5"
    "@react-aria/focus" "3.17.1"
    "@react-aria/interactions" "3.21.3"
    "@react-aria/utils" "3.24.1"
    "@react-stately/tree" "3.8.1"
    "@react-types/accordion" "3.0.0-alpha.21"
    "@react-types/shared" "3.23.1"

"@nextui-org/aria-utils@2.0.26":
  version "2.0.26"
  resolved "https://registry.npmjs.org/@nextui-org/aria-utils/-/aria-utils-2.0.26.tgz"
  integrity sha512-e81HxkNI3/HCPPJT9OVK0g0ivTkuqeeQ043WlAxvgf+upFTEvNN5vmsSKBfWGgfZpsVHgNyHIzwbHjy9zKePLQ==
  dependencies:
    "@nextui-org/react-rsc-utils" "2.0.14"
    "@nextui-org/shared-utils" "2.0.8"
    "@nextui-org/system" "2.2.6"
    "@react-aria/utils" "3.24.1"
    "@react-stately/collections" "3.10.7"
    "@react-stately/overlays" "3.6.7"
    "@react-types/overlays" "3.8.7"
    "@react-types/shared" "3.23.1"

"@nextui-org/autocomplete@2.1.7":
  version "2.1.7"
  resolved "https://registry.npmjs.org/@nextui-org/autocomplete/-/autocomplete-2.1.7.tgz"
  integrity sha512-T3dF5akCXvJ21OxwPxAQmTjHoiB/GMUa2ppcJ9PStfCCPiI2vjwb4CO4q/duj/nXJIpQf/UfPhpSonnJ444o6g==
  dependencies:
    "@nextui-org/aria-utils" "2.0.26"
    "@nextui-org/button" "2.0.38"
    "@nextui-org/input" "2.2.5"
    "@nextui-org/listbox" "2.1.27"
    "@nextui-org/popover" "2.1.29"
    "@nextui-org/react-utils" "2.0.17"
    "@nextui-org/scroll-shadow" "2.1.20"
    "@nextui-org/shared-icons" "2.0.9"
    "@nextui-org/shared-utils" "2.0.8"
    "@nextui-org/spinner" "2.0.34"
    "@nextui-org/use-aria-button" "2.0.10"
    "@nextui-org/use-safe-layout-effect" "2.0.6"
    "@react-aria/combobox" "3.9.1"
    "@react-aria/focus" "3.17.1"
    "@react-aria/i18n" "3.11.1"
    "@react-aria/interactions" "3.21.3"
    "@react-aria/utils" "3.24.1"
    "@react-aria/visually-hidden" "3.8.12"
    "@react-stately/combobox" "3.8.4"
    "@react-types/combobox" "3.11.1"
    "@react-types/shared" "3.23.1"

"@nextui-org/avatar@2.0.33":
  version "2.0.33"
  resolved "https://registry.npmjs.org/@nextui-org/avatar/-/avatar-2.0.33.tgz"
  integrity sha512-SPnIKM+34T/a+KCRCBiG8VwMBzu2/bap7IPHhmICtQ6KmG8Dzmazj3tGZsVt7HjhMRVY7e1vzev4IMaHqkIdRg==
  dependencies:
    "@nextui-org/react-utils" "2.0.17"
    "@nextui-org/shared-utils" "2.0.8"
    "@nextui-org/use-image" "2.0.6"
    "@react-aria/focus" "3.17.1"
    "@react-aria/interactions" "3.21.3"
    "@react-aria/utils" "3.24.1"

"@nextui-org/badge@2.0.32":
  version "2.0.32"
  resolved "https://registry.npmjs.org/@nextui-org/badge/-/badge-2.0.32.tgz"
  integrity sha512-vlV/SY0e7/AmpVP7hB57XoSOo95Fr3kRWcLfMx8yL8VDR2UWMFaMlrT7JTghdgTGFSO7L1Ov1BFwDRRKVe3eyg==
  dependencies:
    "@nextui-org/react-utils" "2.0.17"
    "@nextui-org/shared-utils" "2.0.8"

"@nextui-org/breadcrumbs@2.0.13":
  version "2.0.13"
  resolved "https://registry.npmjs.org/@nextui-org/breadcrumbs/-/breadcrumbs-2.0.13.tgz"
  integrity sha512-tdet47IBOwUaJL0PmxTuGH+ZI2nucyNwG3mX1OokfIXmq5HuMCGKaVFXaNP8mWb4Pii2bvtRqaqTfxmUb3kjGw==
  dependencies:
    "@nextui-org/react-utils" "2.0.17"
    "@nextui-org/shared-icons" "2.0.9"
    "@nextui-org/shared-utils" "2.0.8"
    "@react-aria/breadcrumbs" "3.5.13"
    "@react-aria/focus" "3.17.1"
    "@react-aria/utils" "3.24.1"
    "@react-types/breadcrumbs" "3.7.5"
    "@react-types/shared" "3.23.1"

"@nextui-org/button@2.0.38":
  version "2.0.38"
  resolved "https://registry.npmjs.org/@nextui-org/button/-/button-2.0.38.tgz"
  integrity sha512-XbgyqBv+X7QirXeriGwkqkMOENpAxXRo+jzfMyBMvfsM3kwrFj92OSF1F7/dWDvcW7imVZB9o2Ci7LIppq9ZZQ==
  dependencies:
    "@nextui-org/react-utils" "2.0.17"
    "@nextui-org/ripple" "2.0.33"
    "@nextui-org/shared-utils" "2.0.8"
    "@nextui-org/spinner" "2.0.34"
    "@nextui-org/use-aria-button" "2.0.10"
    "@react-aria/button" "3.9.5"
    "@react-aria/focus" "3.17.1"
    "@react-aria/interactions" "3.21.3"
    "@react-aria/utils" "3.24.1"
    "@react-types/button" "3.9.4"
    "@react-types/shared" "3.23.1"

"@nextui-org/calendar@2.0.12":
  version "2.0.12"
  resolved "https://registry.npmjs.org/@nextui-org/calendar/-/calendar-2.0.12.tgz"
  integrity sha512-FnEnOQnsuyN+F+hy4LEJBvZZcfXMpDGgLkTdnDdoZObXQWwd0PWPjU8GzY+ukhhR5eiU7QIj2AADVRCvuAkiLA==
  dependencies:
    "@internationalized/date" "^3.5.4"
    "@nextui-org/button" "2.0.38"
    "@nextui-org/framer-utils" "2.0.25"
    "@nextui-org/react-utils" "2.0.17"
    "@nextui-org/shared-icons" "2.0.9"
    "@nextui-org/shared-utils" "2.0.8"
    "@nextui-org/use-aria-button" "2.0.10"
    "@react-aria/calendar" "3.5.8"
    "@react-aria/focus" "3.17.1"
    "@react-aria/i18n" "3.11.1"
    "@react-aria/interactions" "3.21.3"
    "@react-aria/utils" "3.24.1"
    "@react-aria/visually-hidden" "3.8.12"
    "@react-stately/calendar" "3.5.1"
    "@react-stately/utils" "3.10.1"
    "@react-types/button" "3.9.4"
    "@react-types/calendar" "3.4.6"
    "@react-types/shared" "3.23.1"
    "@types/lodash.debounce" "^4.0.7"
    lodash.debounce "^4.0.8"
    scroll-into-view-if-needed "3.0.10"

"@nextui-org/card@2.0.34":
  version "2.0.34"
  resolved "https://registry.npmjs.org/@nextui-org/card/-/card-2.0.34.tgz"
  integrity sha512-2RYNPsQkM0FOifGCKmRBR3AuYgYCNmPV7dyA5M3D9Lf0APsHHtsXRA/GeIJ/AuPnglZrYBX8wpM5kLt3dnlQjQ==
  dependencies:
    "@nextui-org/react-utils" "2.0.17"
    "@nextui-org/ripple" "2.0.33"
    "@nextui-org/shared-utils" "2.0.8"
    "@nextui-org/use-aria-button" "2.0.10"
    "@react-aria/button" "3.9.5"
    "@react-aria/focus" "3.17.1"
    "@react-aria/interactions" "3.21.3"
    "@react-aria/utils" "3.24.1"
    "@react-types/shared" "3.23.1"

"@nextui-org/checkbox@2.1.5":
  version "2.1.5"
  resolved "https://registry.npmjs.org/@nextui-org/checkbox/-/checkbox-2.1.5.tgz"
  integrity sha512-PSCWmxEzFPfeIJfoGAtbQS5T7JvBRblUMz5NdCMArA8MLvWW8EKL41cMPsqWjaUanjD0fAI8Q9HuDfBZnkcPbw==
  dependencies:
    "@nextui-org/react-utils" "2.0.17"
    "@nextui-org/shared-utils" "2.0.8"
    "@nextui-org/use-callback-ref" "2.0.6"
    "@nextui-org/use-safe-layout-effect" "2.0.6"
    "@react-aria/checkbox" "3.14.3"
    "@react-aria/focus" "3.17.1"
    "@react-aria/interactions" "3.21.3"
    "@react-aria/utils" "3.24.1"
    "@react-aria/visually-hidden" "3.8.12"
    "@react-stately/checkbox" "3.6.5"
    "@react-stately/toggle" "3.7.4"
    "@react-types/checkbox" "3.8.1"
    "@react-types/shared" "3.23.1"

"@nextui-org/chip@2.0.33":
  version "2.0.33"
  resolved "https://registry.npmjs.org/@nextui-org/chip/-/chip-2.0.33.tgz"
  integrity sha512-6cQkMTV/34iPprjnfK6xlwkv5lnZjMsbYBN3ZqHHrQfV2zQg19ewFcuIw9XlRYA3pGYPpoycdOmSdQ6qXc66lQ==
  dependencies:
    "@nextui-org/react-utils" "2.0.17"
    "@nextui-org/shared-icons" "2.0.9"
    "@nextui-org/shared-utils" "2.0.8"
    "@react-aria/focus" "3.17.1"
    "@react-aria/interactions" "3.21.3"
    "@react-aria/utils" "3.24.1"
    "@react-types/checkbox" "3.8.1"

"@nextui-org/code@2.0.33":
  version "2.0.33"
  resolved "https://registry.npmjs.org/@nextui-org/code/-/code-2.0.33.tgz"
  integrity sha512-G2254ov2rsPxFEoJ0UHVHe+rSmNYwoHZc7STAtiTsJ2HfebZPQbNnfuCifMIpa+kgvHrMBGb85eGk0gy1R+ArA==
  dependencies:
    "@nextui-org/react-utils" "2.0.17"
    "@nextui-org/shared-utils" "2.0.8"
    "@nextui-org/system-rsc" "2.1.6"

"@nextui-org/date-input@2.1.4":
  version "2.1.4"
  resolved "https://registry.npmjs.org/@nextui-org/date-input/-/date-input-2.1.4.tgz"
  integrity sha512-U8Pbe7EhMp9VTfFxB/32+A9N9cJJWswebIz1qpaPy0Hmr92AHS3c1qVTcspkop6wbIM8AnHWEST0QkR95IXPDA==
  dependencies:
    "@internationalized/date" "^3.5.4"
    "@nextui-org/react-utils" "2.0.17"
    "@nextui-org/shared-utils" "2.0.8"
    "@react-aria/datepicker" "3.10.1"
    "@react-aria/i18n" "3.11.1"
    "@react-aria/utils" "3.24.1"
    "@react-stately/datepicker" "3.9.4"
    "@react-types/datepicker" "3.7.4"
    "@react-types/shared" "3.23.1"

"@nextui-org/date-picker@2.1.8":
  version "2.1.8"
  resolved "https://registry.npmjs.org/@nextui-org/date-picker/-/date-picker-2.1.8.tgz"
  integrity sha512-pokAFcrf6AdM53QHf1EzvqVhj8imQRZHWitK9eZPtIdGzJzx28dW0ir7ID0lQFMiNNIQTesSpBLzedTawbcJrg==
  dependencies:
    "@internationalized/date" "^3.5.4"
    "@nextui-org/aria-utils" "2.0.26"
    "@nextui-org/button" "2.0.38"
    "@nextui-org/calendar" "2.0.12"
    "@nextui-org/date-input" "2.1.4"
    "@nextui-org/popover" "2.1.29"
    "@nextui-org/react-utils" "2.0.17"
    "@nextui-org/shared-icons" "2.0.9"
    "@nextui-org/shared-utils" "2.0.8"
    "@react-aria/datepicker" "3.10.1"
    "@react-aria/i18n" "3.11.1"
    "@react-aria/utils" "3.24.1"
    "@react-stately/datepicker" "3.9.4"
    "@react-stately/overlays" "3.6.7"
    "@react-stately/utils" "3.10.1"
    "@react-types/datepicker" "3.7.4"
    "@react-types/shared" "3.23.1"

"@nextui-org/divider@2.0.32":
  version "2.0.32"
  resolved "https://registry.npmjs.org/@nextui-org/divider/-/divider-2.0.32.tgz"
  integrity sha512-2B2j3VmvVDFnMc9Uw7UWMkByA+osgnRmVwMZNZjl9g3oCycz3UDXotNJXjgsLocT8tGO8UwMcrdgo7QBZl52uw==
  dependencies:
    "@nextui-org/react-rsc-utils" "2.0.14"
    "@nextui-org/shared-utils" "2.0.8"
    "@nextui-org/system-rsc" "2.1.6"
    "@react-types/shared" "3.23.1"

"@nextui-org/dropdown@2.1.31":
  version "2.1.31"
  resolved "https://registry.npmjs.org/@nextui-org/dropdown/-/dropdown-2.1.31.tgz"
  integrity sha512-tP6c5MAhWK4hJ6U02oX6APUpjjrn97Zn7t+56Xx4YyQOSj0CJx18VF0JsU+MrjFZxPX3UBKU3B2zGBHOEGE4Kw==
  dependencies:
    "@nextui-org/aria-utils" "2.0.26"
    "@nextui-org/menu" "2.0.30"
    "@nextui-org/popover" "2.1.29"
    "@nextui-org/react-utils" "2.0.17"
    "@nextui-org/shared-utils" "2.0.8"
    "@react-aria/focus" "3.17.1"
    "@react-aria/menu" "3.14.1"
    "@react-aria/utils" "3.24.1"
    "@react-stately/menu" "3.7.1"
    "@react-types/menu" "3.9.9"

"@nextui-org/framer-utils@2.0.25":
  version "2.0.25"
  resolved "https://registry.npmjs.org/@nextui-org/framer-utils/-/framer-utils-2.0.25.tgz"
  integrity sha512-bhQKDg4c5Da4II4UYLKyvYagusTd62eVwPqpfUP+GHZKKZcmRaS6MQZTh4xJYbpyh298S4jRSH/AUAiN/OK3TQ==
  dependencies:
    "@nextui-org/shared-utils" "2.0.8"
    "@nextui-org/system" "2.2.6"
    "@nextui-org/use-measure" "2.0.2"

"@nextui-org/image@2.0.32":
  version "2.0.32"
  resolved "https://registry.npmjs.org/@nextui-org/image/-/image-2.0.32.tgz"
  integrity sha512-JpE0O8qAeJpQA61ZnXNLH76to+dbx93PR5tTOxSvmTxtnuqVg4wl5ar/SBY3czibJPr0sj33k8Mv2EfULjoH7Q==
  dependencies:
    "@nextui-org/react-utils" "2.0.17"
    "@nextui-org/shared-utils" "2.0.8"
    "@nextui-org/use-image" "2.0.6"

"@nextui-org/input@2.2.5":
  version "2.2.5"
  resolved "https://registry.npmjs.org/@nextui-org/input/-/input-2.2.5.tgz"
  integrity sha512-xLgyKcnb+RatRZ62AbCFfTpS3exd2bPSSR75UFKylHHhgX+nvVOkX0dQgmr9e0V8IEECeRvbltw2s/laNFPTtg==
  dependencies:
    "@nextui-org/react-utils" "2.0.17"
    "@nextui-org/shared-icons" "2.0.9"
    "@nextui-org/shared-utils" "2.0.8"
    "@nextui-org/use-safe-layout-effect" "2.0.6"
    "@react-aria/focus" "3.17.1"
    "@react-aria/interactions" "3.21.3"
    "@react-aria/textfield" "3.14.5"
    "@react-aria/utils" "3.24.1"
    "@react-stately/utils" "3.10.1"
    "@react-types/shared" "3.23.1"
    "@react-types/textfield" "3.9.3"
    react-textarea-autosize "^8.5.3"

"@nextui-org/kbd@2.0.34":
  version "2.0.34"
  resolved "https://registry.npmjs.org/@nextui-org/kbd/-/kbd-2.0.34.tgz"
  integrity sha512-sO6RJPgEFccFV8gmfYMTVeQ4f9PBYh09OieRpsZhN4HqdfWwEaeT6LrmdBko3XnJ0T6Me3tBrYULgKWcDcNogw==
  dependencies:
    "@nextui-org/react-utils" "2.0.17"
    "@nextui-org/shared-utils" "2.0.8"
    "@nextui-org/system-rsc" "2.1.6"
    "@react-aria/utils" "3.24.1"

"@nextui-org/link@2.0.35":
  version "2.0.35"
  resolved "https://registry.npmjs.org/@nextui-org/link/-/link-2.0.35.tgz"
  integrity sha512-0XVUsSsysu+WMssokTlLHiMnjr1N6D2Uh3bIBcdFwSqmTLyq+Llgexlm6Fuv1wADRwsR8/DGFp3Pr826cv2Svg==
  dependencies:
    "@nextui-org/react-utils" "2.0.17"
    "@nextui-org/shared-icons" "2.0.9"
    "@nextui-org/shared-utils" "2.0.8"
    "@nextui-org/use-aria-link" "2.0.19"
    "@react-aria/focus" "3.17.1"
    "@react-aria/link" "3.7.1"
    "@react-aria/utils" "3.24.1"
    "@react-types/link" "3.5.5"

"@nextui-org/listbox@^2.1.7", "@nextui-org/listbox@2.1.27":
  version "2.1.27"
  resolved "https://registry.npmjs.org/@nextui-org/listbox/-/listbox-2.1.27.tgz"
  integrity sha512-B9HW/k0awfXsYaNyjaqv/GvEioVzrsCsOdSxVQZgQ3wQ6jNXmGRe1/X6IKg0fIa+P0v379kSgAqrZcwfRpKnWw==
  dependencies:
    "@nextui-org/aria-utils" "2.0.26"
    "@nextui-org/divider" "2.0.32"
    "@nextui-org/react-utils" "2.0.17"
    "@nextui-org/shared-utils" "2.0.8"
    "@nextui-org/use-is-mobile" "2.0.9"
    "@react-aria/focus" "3.17.1"
    "@react-aria/interactions" "3.21.3"
    "@react-aria/listbox" "3.12.1"
    "@react-aria/utils" "3.24.1"
    "@react-stately/list" "3.10.5"
    "@react-types/menu" "3.9.9"
    "@react-types/shared" "3.23.1"

"@nextui-org/menu@2.0.30":
  version "2.0.30"
  resolved "https://registry.npmjs.org/@nextui-org/menu/-/menu-2.0.30.tgz"
  integrity sha512-hZRr/EQ5JxB6yQFmUhDSv9pyLTJmaB4SFC/t5A17UljRhMexlvTU6QpalYIkbY0R/bUXvOkTJNzsRgI5OOQ/aA==
  dependencies:
    "@nextui-org/aria-utils" "2.0.26"
    "@nextui-org/divider" "2.0.32"
    "@nextui-org/react-utils" "2.0.17"
    "@nextui-org/shared-utils" "2.0.8"
    "@nextui-org/use-aria-menu" "2.0.7"
    "@nextui-org/use-is-mobile" "2.0.9"
    "@react-aria/focus" "3.17.1"
    "@react-aria/interactions" "3.21.3"
    "@react-aria/menu" "3.14.1"
    "@react-aria/utils" "3.24.1"
    "@react-stately/menu" "3.7.1"
    "@react-stately/tree" "3.8.1"
    "@react-types/menu" "3.9.9"
    "@react-types/shared" "3.23.1"

"@nextui-org/modal@2.0.41":
  version "2.0.41"
  resolved "https://registry.npmjs.org/@nextui-org/modal/-/modal-2.0.41.tgz"
  integrity sha512-Sirn319xAf7E4cZqvQ0o0Vd3Xqy0FRSuhOTwp8dALMGTMY61c2nIyurgVCNP6hh8dMvMT7zQEPP9/LE0boFCEQ==
  dependencies:
    "@nextui-org/framer-utils" "2.0.25"
    "@nextui-org/react-utils" "2.0.17"
    "@nextui-org/shared-icons" "2.0.9"
    "@nextui-org/shared-utils" "2.0.8"
    "@nextui-org/use-aria-button" "2.0.10"
    "@nextui-org/use-aria-modal-overlay" "2.0.13"
    "@nextui-org/use-disclosure" "2.0.10"
    "@react-aria/dialog" "3.5.14"
    "@react-aria/focus" "3.17.1"
    "@react-aria/interactions" "3.21.3"
    "@react-aria/overlays" "3.22.1"
    "@react-aria/utils" "3.24.1"
    "@react-stately/overlays" "3.6.7"
    "@react-types/overlays" "3.8.7"

"@nextui-org/navbar@2.0.37":
  version "2.0.37"
  resolved "https://registry.npmjs.org/@nextui-org/navbar/-/navbar-2.0.37.tgz"
  integrity sha512-HuHXMU+V367LlvSGjqRPBNKmOERLvc4XWceva+KmiT99BLqHmMECkQVTR6ogO36eJUU96aR8JSfAiHLjvw5msw==
  dependencies:
    "@nextui-org/framer-utils" "2.0.25"
    "@nextui-org/react-utils" "2.0.17"
    "@nextui-org/shared-utils" "2.0.8"
    "@nextui-org/use-aria-toggle-button" "2.0.10"
    "@nextui-org/use-scroll-position" "2.0.9"
    "@react-aria/focus" "3.17.1"
    "@react-aria/interactions" "3.21.3"
    "@react-aria/overlays" "3.22.1"
    "@react-aria/utils" "3.24.1"
    "@react-stately/toggle" "3.7.4"
    "@react-stately/utils" "3.10.1"
    react-remove-scroll "^2.5.6"

"@nextui-org/pagination@2.0.36":
  version "2.0.36"
  resolved "https://registry.npmjs.org/@nextui-org/pagination/-/pagination-2.0.36.tgz"
  integrity sha512-VKs2vMj8dybNzb/WkAMmvFBsxdgBvpVihIA4eXSo2ve7fpcLjIF1iPLHuDgpSyv3h3dy009sQTVo3lVTVT1a6w==
  dependencies:
    "@nextui-org/react-utils" "2.0.17"
    "@nextui-org/shared-icons" "2.0.9"
    "@nextui-org/shared-utils" "2.0.8"
    "@nextui-org/use-pagination" "2.0.10"
    "@react-aria/focus" "3.17.1"
    "@react-aria/i18n" "3.11.1"
    "@react-aria/interactions" "3.21.3"
    "@react-aria/utils" "3.24.1"
    scroll-into-view-if-needed "3.0.10"

"@nextui-org/popover@2.1.29":
  version "2.1.29"
  resolved "https://registry.npmjs.org/@nextui-org/popover/-/popover-2.1.29.tgz"
  integrity sha512-qGjMnAQVHQNfG571h9Tah2MXPs5mhxcTIj4TuBgwPzQTWXjjeffaHV3FlHdg5PxjTpNZOdDfrg0eRhDqIjKocQ==
  dependencies:
    "@nextui-org/aria-utils" "2.0.26"
    "@nextui-org/button" "2.0.38"
    "@nextui-org/framer-utils" "2.0.25"
    "@nextui-org/react-utils" "2.0.17"
    "@nextui-org/shared-utils" "2.0.8"
    "@nextui-org/use-aria-button" "2.0.10"
    "@nextui-org/use-safe-layout-effect" "2.0.6"
    "@react-aria/dialog" "3.5.14"
    "@react-aria/focus" "3.17.1"
    "@react-aria/interactions" "3.21.3"
    "@react-aria/overlays" "3.22.1"
    "@react-aria/utils" "3.24.1"
    "@react-stately/overlays" "3.6.7"
    "@react-types/button" "3.9.4"
    "@react-types/overlays" "3.8.7"
    react-remove-scroll "^2.5.6"

"@nextui-org/progress@2.0.34":
  version "2.0.34"
  resolved "https://registry.npmjs.org/@nextui-org/progress/-/progress-2.0.34.tgz"
  integrity sha512-rJmZCrLdufJKLsonJ37oPOEHEpZykD4c+0G749zcKOkRXHOD9DiQian2YoZEE/Yyf3pLdFQG3W9vSLbsgED3PQ==
  dependencies:
    "@nextui-org/react-utils" "2.0.17"
    "@nextui-org/shared-utils" "2.0.8"
    "@nextui-org/use-is-mounted" "2.0.6"
    "@react-aria/i18n" "3.11.1"
    "@react-aria/progress" "3.4.13"
    "@react-aria/utils" "3.24.1"
    "@react-types/progress" "3.5.4"

"@nextui-org/radio@2.1.5":
  version "2.1.5"
  resolved "https://registry.npmjs.org/@nextui-org/radio/-/radio-2.1.5.tgz"
  integrity sha512-0tF/VkMQv+KeYmFQpkrpz9S7j7U8gqCet+F97Cz7fFjdb+Q3w9waBzg84QayD7EZdjsYW4FNSkjPeiBhLdVUsw==
  dependencies:
    "@nextui-org/react-utils" "2.0.17"
    "@nextui-org/shared-utils" "2.0.8"
    "@react-aria/focus" "3.17.1"
    "@react-aria/interactions" "3.21.3"
    "@react-aria/radio" "3.10.4"
    "@react-aria/utils" "3.24.1"
    "@react-aria/visually-hidden" "3.8.12"
    "@react-stately/radio" "3.10.4"
    "@react-types/radio" "3.8.1"
    "@react-types/shared" "3.23.1"

"@nextui-org/react-rsc-utils@2.0.14":
  version "2.0.14"
  resolved "https://registry.npmjs.org/@nextui-org/react-rsc-utils/-/react-rsc-utils-2.0.14.tgz"
  integrity sha512-s0GVgDhScyx+d9FtXd8BXf049REyaPvWsO4RRr7JDHrk91NlQ11Mqxka9o+8g5NX0rphI0rbe3/b1Dz+iQRx3w==

"@nextui-org/react-utils@2.0.17":
  version "2.0.17"
  resolved "https://registry.npmjs.org/@nextui-org/react-utils/-/react-utils-2.0.17.tgz"
  integrity sha512-U/b49hToVfhOM4dg4n57ZyUjLpts4JogQ139lfQBYPTb8z/ATNsJ3vLIqW5ZvDK6L0Er+JT11UVQ+03m7QMvaQ==
  dependencies:
    "@nextui-org/react-rsc-utils" "2.0.14"
    "@nextui-org/shared-utils" "2.0.8"

"@nextui-org/react@^2.2.9":
  version "2.4.8"
  resolved "https://registry.npmjs.org/@nextui-org/react/-/react-2.4.8.tgz"
  integrity sha512-ZwXg6As3A+Gs+Jyc42t4MHNupHEsh9YmEaypE20ikqIPTCLQnrGQ/RWOGwzZ2a9kZWbZ89a/3rJwZMRKdcemxg==
  dependencies:
    "@nextui-org/accordion" "2.0.40"
    "@nextui-org/autocomplete" "2.1.7"
    "@nextui-org/avatar" "2.0.33"
    "@nextui-org/badge" "2.0.32"
    "@nextui-org/breadcrumbs" "2.0.13"
    "@nextui-org/button" "2.0.38"
    "@nextui-org/calendar" "2.0.12"
    "@nextui-org/card" "2.0.34"
    "@nextui-org/checkbox" "2.1.5"
    "@nextui-org/chip" "2.0.33"
    "@nextui-org/code" "2.0.33"
    "@nextui-org/date-input" "2.1.4"
    "@nextui-org/date-picker" "2.1.8"
    "@nextui-org/divider" "2.0.32"
    "@nextui-org/dropdown" "2.1.31"
    "@nextui-org/framer-utils" "2.0.25"
    "@nextui-org/image" "2.0.32"
    "@nextui-org/input" "2.2.5"
    "@nextui-org/kbd" "2.0.34"
    "@nextui-org/link" "2.0.35"
    "@nextui-org/listbox" "2.1.27"
    "@nextui-org/menu" "2.0.30"
    "@nextui-org/modal" "2.0.41"
    "@nextui-org/navbar" "2.0.37"
    "@nextui-org/pagination" "2.0.36"
    "@nextui-org/popover" "2.1.29"
    "@nextui-org/progress" "2.0.34"
    "@nextui-org/radio" "2.1.5"
    "@nextui-org/ripple" "2.0.33"
    "@nextui-org/scroll-shadow" "2.1.20"
    "@nextui-org/select" "2.2.7"
    "@nextui-org/skeleton" "2.0.32"
    "@nextui-org/slider" "2.2.17"
    "@nextui-org/snippet" "2.0.43"
    "@nextui-org/spacer" "2.0.33"
    "@nextui-org/spinner" "2.0.34"
    "@nextui-org/switch" "2.0.34"
    "@nextui-org/system" "2.2.6"
    "@nextui-org/table" "2.0.40"
    "@nextui-org/tabs" "2.0.37"
    "@nextui-org/theme" "2.2.11"
    "@nextui-org/tooltip" "2.0.41"
    "@nextui-org/user" "2.0.34"
    "@react-aria/visually-hidden" "3.8.12"

"@nextui-org/ripple@2.0.33":
  version "2.0.33"
  resolved "https://registry.npmjs.org/@nextui-org/ripple/-/ripple-2.0.33.tgz"
  integrity sha512-Zsa60CXtGCF7weTCFbSfT0OlxlGHdd5b/sSJTYrmMZRHOIUpHW8kT0bxVYF/6X8nCCJYxzBKXUqdE3Y31fhNeQ==
  dependencies:
    "@nextui-org/react-utils" "2.0.17"
    "@nextui-org/shared-utils" "2.0.8"

"@nextui-org/scroll-shadow@2.1.20":
  version "2.1.20"
  resolved "https://registry.npmjs.org/@nextui-org/scroll-shadow/-/scroll-shadow-2.1.20.tgz"
  integrity sha512-8ULiUmbZ/Jzr1okI8Yzjzl5M4Ow3pJEm34hT5id0EaMIgklNa3Nnp/Dyp54JwwUbI8Kt3jOAMqkPitGIZyo5Ag==
  dependencies:
    "@nextui-org/react-utils" "2.0.17"
    "@nextui-org/shared-utils" "2.0.8"
    "@nextui-org/use-data-scroll-overflow" "2.1.7"

"@nextui-org/select@^2.1.8", "@nextui-org/select@2.2.7":
  version "2.2.7"
  resolved "https://registry.npmjs.org/@nextui-org/select/-/select-2.2.7.tgz"
  integrity sha512-lA2EOjquhiHmLSInHFEarq64ZOQV37+ry1d8kvsqJ7R9dsqw1QEuMzH2Kk8/NqwrYMccHh5iAZ7PaLp90NSSxg==
  dependencies:
    "@nextui-org/aria-utils" "2.0.26"
    "@nextui-org/listbox" "2.1.27"
    "@nextui-org/popover" "2.1.29"
    "@nextui-org/react-utils" "2.0.17"
    "@nextui-org/scroll-shadow" "2.1.20"
    "@nextui-org/shared-icons" "2.0.9"
    "@nextui-org/shared-utils" "2.0.8"
    "@nextui-org/spinner" "2.0.34"
    "@nextui-org/use-aria-button" "2.0.10"
    "@nextui-org/use-aria-multiselect" "2.2.5"
    "@nextui-org/use-safe-layout-effect" "2.0.6"
    "@react-aria/focus" "3.17.1"
    "@react-aria/form" "3.0.5"
    "@react-aria/interactions" "3.21.3"
    "@react-aria/utils" "3.24.1"
    "@react-aria/visually-hidden" "3.8.12"
    "@react-types/shared" "3.23.1"

"@nextui-org/shared-icons@2.0.9":
  version "2.0.9"
  resolved "https://registry.npmjs.org/@nextui-org/shared-icons/-/shared-icons-2.0.9.tgz"
  integrity sha512-WG3yinVY7Tk9VqJgcdF4V8Ok9+fcm5ey7S1els7kujrfqLYxtqoKywgiY/7QHwZlfQkzpykAfy+NAlHkTP5hMg==

"@nextui-org/shared-utils@2.0.8":
  version "2.0.8"
  resolved "https://registry.npmjs.org/@nextui-org/shared-utils/-/shared-utils-2.0.8.tgz"
  integrity sha512-ZEtoMPXS+IjT8GvpJTS9IWDnT1JNCKV+NDqqgysAf1niJmOFLyJgl6dh/9n4ufcGf1GbSEQN+VhJasEw7ajYGQ==

"@nextui-org/skeleton@2.0.32":
  version "2.0.32"
  resolved "https://registry.npmjs.org/@nextui-org/skeleton/-/skeleton-2.0.32.tgz"
  integrity sha512-dS0vuRrc4oWktW3wa/KFhcBNnV0oiDqKXP4BqRj7wgS01fOAqj3cJiqwUDLKO8GbEnxLkbqLBFcUoLgktpRszQ==
  dependencies:
    "@nextui-org/react-utils" "2.0.17"
    "@nextui-org/shared-utils" "2.0.8"

"@nextui-org/slider@^2.2.5", "@nextui-org/slider@2.2.17":
  version "2.2.17"
  resolved "https://registry.npmjs.org/@nextui-org/slider/-/slider-2.2.17.tgz"
  integrity sha512-MgeJv3X+bT7Bw+LK1zba4vToOUzv8lCvDuGe0U5suJy1AKGN6uGDgSAxpIZhCYNWsuNRsopwdvsGtyeIjOEStA==
  dependencies:
    "@nextui-org/react-utils" "2.0.17"
    "@nextui-org/shared-utils" "2.0.8"
    "@nextui-org/tooltip" "2.0.41"
    "@react-aria/focus" "3.17.1"
    "@react-aria/i18n" "3.11.1"
    "@react-aria/interactions" "3.21.3"
    "@react-aria/slider" "3.7.8"
    "@react-aria/utils" "3.24.1"
    "@react-aria/visually-hidden" "3.8.12"
    "@react-stately/slider" "3.5.4"

"@nextui-org/snippet@2.0.43":
  version "2.0.43"
  resolved "https://registry.npmjs.org/@nextui-org/snippet/-/snippet-2.0.43.tgz"
  integrity sha512-PLxc9ph9CLj52L26XSv4vBmQcSytCNc3ZBxkOTBEqmLSHCWwGQExrqKPnVZTE1etr6dcULiy5vNIpD8R7taO8A==
  dependencies:
    "@nextui-org/button" "2.0.38"
    "@nextui-org/react-utils" "2.0.17"
    "@nextui-org/shared-icons" "2.0.9"
    "@nextui-org/shared-utils" "2.0.8"
    "@nextui-org/tooltip" "2.0.41"
    "@nextui-org/use-clipboard" "2.0.7"
    "@react-aria/focus" "3.17.1"
    "@react-aria/utils" "3.24.1"

"@nextui-org/spacer@2.0.33":
  version "2.0.33"
  resolved "https://registry.npmjs.org/@nextui-org/spacer/-/spacer-2.0.33.tgz"
  integrity sha512-0YDtovMWuAVgBvVXUmplzohObGxMPFhisHXn6v+0nflAE9LiVeiXf121WVOEMrd08S7xvmrAANcMwo4TsYi49g==
  dependencies:
    "@nextui-org/react-utils" "2.0.17"
    "@nextui-org/shared-utils" "2.0.8"
    "@nextui-org/system-rsc" "2.1.6"

"@nextui-org/spinner@2.0.34":
  version "2.0.34"
  resolved "https://registry.npmjs.org/@nextui-org/spinner/-/spinner-2.0.34.tgz"
  integrity sha512-YKw/6xSLhsXU1k22OvYKyWhtJCHzW2bRAiieVSVG5xak3gYwknTds5H9s5uur+oAZVK9AkyAObD19QuZND32Jg==
  dependencies:
    "@nextui-org/react-utils" "2.0.17"
    "@nextui-org/shared-utils" "2.0.8"
    "@nextui-org/system-rsc" "2.1.6"

"@nextui-org/switch@2.0.34":
  version "2.0.34"
  resolved "https://registry.npmjs.org/@nextui-org/switch/-/switch-2.0.34.tgz"
  integrity sha512-SczQiHswo8eR94ecDgcULIsSIPfYVncqfKllcHEGqAs9BDpZun44KK0/R0xhWuPpx5oqB60VeSABN7JtEAxF+Q==
  dependencies:
    "@nextui-org/react-utils" "2.0.17"
    "@nextui-org/shared-utils" "2.0.8"
    "@nextui-org/use-safe-layout-effect" "2.0.6"
    "@react-aria/focus" "3.17.1"
    "@react-aria/interactions" "3.21.3"
    "@react-aria/switch" "3.6.4"
    "@react-aria/utils" "3.24.1"
    "@react-aria/visually-hidden" "3.8.12"
    "@react-stately/toggle" "3.7.4"
    "@react-types/shared" "3.23.1"

"@nextui-org/system-rsc@2.1.6":
  version "2.1.6"
  resolved "https://registry.npmjs.org/@nextui-org/system-rsc/-/system-rsc-2.1.6.tgz"
  integrity sha512-Wl2QwEFjYwuvw26R1RH3ZY81PD8YmfgtIjFvJZRP2VEIT6rPvlQ4ojgqdrkVkQZQ0L/K+5ZLbTKgLEFkj5ysdQ==
  dependencies:
    "@react-types/shared" "3.23.1"
    clsx "^1.2.1"

"@nextui-org/system@>=2.0.0", "@nextui-org/system@>=2.1.0", "@nextui-org/system@2.2.6":
  version "2.2.6"
  resolved "https://registry.npmjs.org/@nextui-org/system/-/system-2.2.6.tgz"
  integrity sha512-tjIkOI0w32g68CGWleuSyIbEz8XBbeoNogR2lu7MWk3QovHCqgr4VVrP1cwMRYnwDPFQP3OpmH+NR9yzt+pIfg==
  dependencies:
    "@internationalized/date" "^3.5.4"
    "@nextui-org/react-utils" "2.0.17"
    "@nextui-org/system-rsc" "2.1.6"
    "@react-aria/i18n" "3.11.1"
    "@react-aria/overlays" "3.22.1"
    "@react-aria/utils" "3.24.1"
    "@react-stately/utils" "3.10.1"

"@nextui-org/table@2.0.40":
  version "2.0.40"
  resolved "https://registry.npmjs.org/@nextui-org/table/-/table-2.0.40.tgz"
  integrity sha512-qDbSsu6mpWnr1Mt3DYTBzTFtN8Z5Gv7GDqECGcDVradkDVuJFZvkB9Ke392LcVZoXSk99Rpamq4WSWkEewBhWg==
  dependencies:
    "@nextui-org/checkbox" "2.1.5"
    "@nextui-org/react-utils" "2.0.17"
    "@nextui-org/shared-icons" "2.0.9"
    "@nextui-org/shared-utils" "2.0.8"
    "@nextui-org/spacer" "2.0.33"
    "@react-aria/focus" "3.17.1"
    "@react-aria/interactions" "3.21.3"
    "@react-aria/table" "3.14.1"
    "@react-aria/utils" "3.24.1"
    "@react-aria/visually-hidden" "3.8.12"
    "@react-stately/table" "3.11.8"
    "@react-stately/virtualizer" "3.7.1"
    "@react-types/grid" "3.2.6"
    "@react-types/table" "3.9.5"

"@nextui-org/tabs@2.0.37":
  version "2.0.37"
  resolved "https://registry.npmjs.org/@nextui-org/tabs/-/tabs-2.0.37.tgz"
  integrity sha512-IQicuDggxTL+JeW3fRoZR4Rr24EwinxAdfU1jqcvT6gZywumndV27+I00kARz8P03kobYoY9t73NY92qo8T5gg==
  dependencies:
    "@nextui-org/aria-utils" "2.0.26"
    "@nextui-org/framer-utils" "2.0.25"
    "@nextui-org/react-utils" "2.0.17"
    "@nextui-org/shared-utils" "2.0.8"
    "@nextui-org/use-is-mounted" "2.0.6"
    "@nextui-org/use-update-effect" "2.0.6"
    "@react-aria/focus" "3.17.1"
    "@react-aria/interactions" "3.21.3"
    "@react-aria/tabs" "3.9.1"
    "@react-aria/utils" "3.24.1"
    "@react-stately/tabs" "3.6.6"
    "@react-types/shared" "3.23.1"
    "@react-types/tabs" "3.3.7"
    scroll-into-view-if-needed "3.0.10"

"@nextui-org/theme@>=2.1.0", "@nextui-org/theme@>=2.2.0", "@nextui-org/theme@2.2.11":
  version "2.2.11"
  resolved "https://registry.npmjs.org/@nextui-org/theme/-/theme-2.2.11.tgz"
  integrity sha512-bg9+KNnFxcP3w/ugivEJtvQibODbTxfl6UdVvx7TCY8Rd269U7F2+nhnw1Qd1xJT5yZQnX6m//9wOoGtJV+6Kg==
  dependencies:
    clsx "^1.2.1"
    color "^4.2.3"
    color2k "^2.0.2"
    deepmerge "4.3.1"
    flat "^5.0.2"
    lodash.foreach "^4.5.0"
    lodash.get "^4.4.2"
    lodash.kebabcase "^4.1.1"
    lodash.mapkeys "^4.6.0"
    lodash.omit "^4.5.0"
    tailwind-merge "^1.14.0"
    tailwind-variants "^0.1.20"

"@nextui-org/tooltip@2.0.41":
  version "2.0.41"
  resolved "https://registry.npmjs.org/@nextui-org/tooltip/-/tooltip-2.0.41.tgz"
  integrity sha512-1c+vkCCszKcKl15HywlZ7UOL7c1UFgLudqBB/dEdWZiclT01BRiracMbcQ7McKHQCRl77Aa7LFv5x4wHOicWHQ==
  dependencies:
    "@nextui-org/aria-utils" "2.0.26"
    "@nextui-org/framer-utils" "2.0.25"
    "@nextui-org/react-utils" "2.0.17"
    "@nextui-org/shared-utils" "2.0.8"
    "@nextui-org/use-safe-layout-effect" "2.0.6"
    "@react-aria/interactions" "3.21.3"
    "@react-aria/overlays" "3.22.1"
    "@react-aria/tooltip" "3.7.4"
    "@react-aria/utils" "3.24.1"
    "@react-stately/tooltip" "3.4.9"
    "@react-types/overlays" "3.8.7"
    "@react-types/tooltip" "3.4.9"

"@nextui-org/use-aria-accordion@2.0.7":
  version "2.0.7"
  resolved "https://registry.npmjs.org/@nextui-org/use-aria-accordion/-/use-aria-accordion-2.0.7.tgz"
  integrity sha512-VzGlxmsu2tWG2Pht1e0PBz40jz95v0OEKYVXq91WpDMwj8Bl1CYvxrw2Qz41/5Xi0X843Mmo4sPwrc/hk0+RHA==
  dependencies:
    "@react-aria/button" "3.9.5"
    "@react-aria/focus" "3.17.1"
    "@react-aria/selection" "3.18.1"
    "@react-aria/utils" "3.24.1"
    "@react-stately/tree" "3.8.1"
    "@react-types/accordion" "3.0.0-alpha.21"
    "@react-types/shared" "3.23.1"

"@nextui-org/use-aria-button@2.0.10":
  version "2.0.10"
  resolved "https://registry.npmjs.org/@nextui-org/use-aria-button/-/use-aria-button-2.0.10.tgz"
  integrity sha512-tUpp4QMr1zugKPevyToeRHIufTuc/g+67/r/oQLRTG0mMo3yGVmggykQuYn22fqqZPpW6nHcB9VYc+XtZZ27TQ==
  dependencies:
    "@react-aria/focus" "3.17.1"
    "@react-aria/interactions" "3.21.3"
    "@react-aria/utils" "3.24.1"
    "@react-types/button" "3.9.4"
    "@react-types/shared" "3.23.1"

"@nextui-org/use-aria-link@2.0.19":
  version "2.0.19"
  resolved "https://registry.npmjs.org/@nextui-org/use-aria-link/-/use-aria-link-2.0.19.tgz"
  integrity sha512-ef61cJLlwcR4zBWiaeHZy4K18juFjUup2SslfLIAiZz3kVosBCGKmkJkw1SASYY8+D/oUc2B6BFIk25YEsRKRw==
  dependencies:
    "@react-aria/focus" "3.17.1"
    "@react-aria/interactions" "3.21.3"
    "@react-aria/utils" "3.24.1"
    "@react-types/link" "3.5.5"
    "@react-types/shared" "3.23.1"

"@nextui-org/use-aria-menu@2.0.7":
  version "2.0.7"
  resolved "https://registry.npmjs.org/@nextui-org/use-aria-menu/-/use-aria-menu-2.0.7.tgz"
  integrity sha512-5U91zFiWTLXsOhE0W3CThsD5TmL3ANeTEtoimtPgSLWV9keZBD9Ja62WsnPZPPAWhmv7jtL0/qk4d/YOra7PVA==
  dependencies:
    "@react-aria/i18n" "3.11.1"
    "@react-aria/interactions" "3.21.3"
    "@react-aria/menu" "3.14.1"
    "@react-aria/selection" "3.18.1"
    "@react-aria/utils" "3.24.1"
    "@react-stately/collections" "3.10.7"
    "@react-stately/tree" "3.8.1"
    "@react-types/menu" "3.9.9"
    "@react-types/shared" "3.23.1"

"@nextui-org/use-aria-modal-overlay@2.0.13":
  version "2.0.13"
  resolved "https://registry.npmjs.org/@nextui-org/use-aria-modal-overlay/-/use-aria-modal-overlay-2.0.13.tgz"
  integrity sha512-ifQxJwTX72lhVUofEVQqMbpe9vEUiCIqiimzlUjeVuE0cYOXaoJLEgPozHpYQrdjTNiwD5On0LLMRgz19XyAqw==
  dependencies:
    "@react-aria/overlays" "3.22.1"
    "@react-aria/utils" "3.24.1"
    "@react-stately/overlays" "3.6.7"
    "@react-types/shared" "3.23.1"

"@nextui-org/use-aria-multiselect@2.2.5":
  version "2.2.5"
  resolved "https://registry.npmjs.org/@nextui-org/use-aria-multiselect/-/use-aria-multiselect-2.2.5.tgz"
  integrity sha512-Gxo2M0LdnFL4/WCi192ziFB8JmSZm6yZYT8RB021Z3iAPBu/Pp9GnWEPZu5g15mKnn3jW5Ecnfw03jTEAQBR+Q==
  dependencies:
    "@react-aria/i18n" "3.11.1"
    "@react-aria/interactions" "3.21.3"
    "@react-aria/label" "3.7.8"
    "@react-aria/listbox" "3.12.1"
    "@react-aria/menu" "3.14.1"
    "@react-aria/selection" "3.18.1"
    "@react-aria/utils" "3.24.1"
    "@react-stately/form" "3.0.3"
    "@react-stately/list" "3.10.5"
    "@react-stately/menu" "3.7.1"
    "@react-types/button" "3.9.4"
    "@react-types/overlays" "3.8.7"
    "@react-types/select" "3.9.4"
    "@react-types/shared" "3.23.1"

"@nextui-org/use-aria-toggle-button@2.0.10":
  version "2.0.10"
  resolved "https://registry.npmjs.org/@nextui-org/use-aria-toggle-button/-/use-aria-toggle-button-2.0.10.tgz"
  integrity sha512-U5jOmEO+nMIgYvBF0+gJtdq8C6dynGMjzAboPG4FhuHOzDoNiC12G5FIbGnRe8K1hMsKVuaI72p9986NhfqNgw==
  dependencies:
    "@nextui-org/use-aria-button" "2.0.10"
    "@react-aria/utils" "3.24.1"
    "@react-stately/toggle" "3.7.4"
    "@react-types/button" "3.9.4"
    "@react-types/shared" "3.23.1"

"@nextui-org/use-callback-ref@2.0.6":
  version "2.0.6"
  resolved "https://registry.npmjs.org/@nextui-org/use-callback-ref/-/use-callback-ref-2.0.6.tgz"
  integrity sha512-2WcwWuK1L/wIpTbibnLrysmmkzWomvkVIcgWayB6n/w+bpPrPCG7Zyg2WHzmMmDhe6imV//KKBgNKRi8Xhu/VA==
  dependencies:
    "@nextui-org/use-safe-layout-effect" "2.0.6"

"@nextui-org/use-clipboard@2.0.7":
  version "2.0.7"
  resolved "https://registry.npmjs.org/@nextui-org/use-clipboard/-/use-clipboard-2.0.7.tgz"
  integrity sha512-Bn1fF/goMwOA5DQyw3A4ebfgozwR8U5k5TAZMPiy1RBWgTFw7+lB0GNbH+DOnUGY5Vyztyaw6gtUyc3tVzJxeg==

"@nextui-org/use-data-scroll-overflow@2.1.7":
  version "2.1.7"
  resolved "https://registry.npmjs.org/@nextui-org/use-data-scroll-overflow/-/use-data-scroll-overflow-2.1.7.tgz"
  integrity sha512-MP4YLjBWyIt0KyWPndXyhnkKgOLqTZ2aPY82Czjqn+eZk/l8BNo0nfA+dZFfbfEuPJgqdt/JDkMOrS+uq0+vkQ==
  dependencies:
    "@nextui-org/shared-utils" "2.0.8"

"@nextui-org/use-disclosure@2.0.10":
  version "2.0.10"
  resolved "https://registry.npmjs.org/@nextui-org/use-disclosure/-/use-disclosure-2.0.10.tgz"
  integrity sha512-s2I58d7x2f1JRriZnNm9ZoxrGmxF+DnC9BXM1sD99Wq1VNMd0dhitmx0mUWfUB7l5HLyZgKOeiSLG+ugy1F1Yw==
  dependencies:
    "@nextui-org/use-callback-ref" "2.0.6"
    "@react-aria/utils" "3.24.1"
    "@react-stately/utils" "3.10.1"

"@nextui-org/use-image@2.0.6":
  version "2.0.6"
  resolved "https://registry.npmjs.org/@nextui-org/use-image/-/use-image-2.0.6.tgz"
  integrity sha512-VelN9y3vzwIpPfubFMh00YRQ0f4+I5FElcAvAqoo0Kfb0K7sGrTo1lZNApHm6yBN2gJMMeccG9u7bZB+wcDGZQ==
  dependencies:
    "@nextui-org/use-safe-layout-effect" "2.0.6"

"@nextui-org/use-is-mobile@2.0.9":
  version "2.0.9"
  resolved "https://registry.npmjs.org/@nextui-org/use-is-mobile/-/use-is-mobile-2.0.9.tgz"
  integrity sha512-u5pRmPV0wacdpOcAkQnWwE30yNBl2uk1WvbWkrSELxIVRN22+fTIYn8ynnHK0JbJFTA6/5zh7uIfETQu3L6KjA==
  dependencies:
    "@react-aria/ssr" "3.9.4"

"@nextui-org/use-is-mounted@2.0.6":
  version "2.0.6"
  resolved "https://registry.npmjs.org/@nextui-org/use-is-mounted/-/use-is-mounted-2.0.6.tgz"
  integrity sha512-/lcMdYnwBZ1EuKMLRIhHeAZG8stXWNTz7wBweAlLId23VC4VHgCp/s9K9Vbj1A5/r8FiFQeoTmXQuMAMUoPRtg==

"@nextui-org/use-measure@2.0.2":
  version "2.0.2"
  resolved "https://registry.npmjs.org/@nextui-org/use-measure/-/use-measure-2.0.2.tgz"
  integrity sha512-H/RSPPA9B5sZ10wiXR3jLlYFEuiVnc0O/sgLLQfrb5M0hvHoaqMThnsZpm//5iyS7tD7kxPeYNLa1EhzlQKxDA==

"@nextui-org/use-pagination@2.0.10":
  version "2.0.10"
  resolved "https://registry.npmjs.org/@nextui-org/use-pagination/-/use-pagination-2.0.10.tgz"
  integrity sha512-PD6M8QKngUnTJfyoGiZrnrfUtA1A9ZVUjmbONO/1kxPuUegv0ZOQeFECPP2h7SFPxsyOceL1T97rg/2YPS247g==
  dependencies:
    "@nextui-org/shared-utils" "2.0.8"
    "@react-aria/i18n" "3.11.1"

"@nextui-org/use-safe-layout-effect@2.0.6":
  version "2.0.6"
  resolved "https://registry.npmjs.org/@nextui-org/use-safe-layout-effect/-/use-safe-layout-effect-2.0.6.tgz"
  integrity sha512-xzEJXf/g9GaSqjLpQ4+Z2/pw1GPq2Fc5cWRGqEXbGauEMXuH8UboRls1BmIV1RuOpqI6FgxkEmxL1EuVIRVmvQ==

"@nextui-org/use-scroll-position@2.0.9":
  version "2.0.9"
  resolved "https://registry.npmjs.org/@nextui-org/use-scroll-position/-/use-scroll-position-2.0.9.tgz"
  integrity sha512-tXbpb2bkKIjOp2I1uZ1T4T9Lxp0+Ta/TKu+5qvqsXkHRPbcoukdsquagYUDWK/fcumg72UPR8QP+na8KMn2gCg==

"@nextui-org/use-update-effect@2.0.6":
  version "2.0.6"
  resolved "https://registry.npmjs.org/@nextui-org/use-update-effect/-/use-update-effect-2.0.6.tgz"
  integrity sha512-n5Qiv3ferKn+cSxU3Vv+96LdG8I/00mzc7Veoan+P9GL0aCTrsPB6RslTsiblaiAXQcqTiFXd8xwsK309DXOXA==

"@nextui-org/user@2.0.34":
  version "2.0.34"
  resolved "https://registry.npmjs.org/@nextui-org/user/-/user-2.0.34.tgz"
  integrity sha512-7MN/xBaMhDJ0b+hB2YpGIm2DsC9CTpN1ab+EKwhUuWn26SgXw2FNu8CSHViyDEkvOP7sYKdHLp9UtSo/f3JnsQ==
  dependencies:
    "@nextui-org/avatar" "2.0.33"
    "@nextui-org/react-utils" "2.0.17"
    "@nextui-org/shared-utils" "2.0.8"
    "@react-aria/focus" "3.17.1"
    "@react-aria/utils" "3.24.1"

"@nodelib/fs.scandir@2.1.5":
  version "2.1.5"
  resolved "https://registry.npmjs.org/@nodelib/fs.scandir/-/fs.scandir-2.1.5.tgz"
  integrity sha512-vq24Bq3ym5HEQm2NKCr3yXDwjc7vTsEThRDnkp2DK9p1uqLR+DHurm/NOTo0KG7HYHU7eppKZj3MyqYuMBf62g==
  dependencies:
    "@nodelib/fs.stat" "2.0.5"
    run-parallel "^1.1.9"

"@nodelib/fs.stat@^2.0.2", "@nodelib/fs.stat@2.0.5":
  version "2.0.5"
  resolved "https://registry.npmjs.org/@nodelib/fs.stat/-/fs.stat-2.0.5.tgz"
  integrity sha512-RkhPPp2zrqDAQA/2jNhnztcPAlv64XdhIp7a7454A5ovI7Bukxgt7MX7udwAu3zg1DcpPU0rz3VV1SeaqvY4+A==

"@nodelib/fs.walk@^1.2.3":
  version "1.2.8"
  resolved "https://registry.npmjs.org/@nodelib/fs.walk/-/fs.walk-1.2.8.tgz"
  integrity sha512-oGB+UxlgWcgQkgwo8GcEGwemoTFt3FIO9ababBmaGwXIoBKZ+GTy0pP185beGg7Llih/NSHSV2XAs1lnznocSg==
  dependencies:
    "@nodelib/fs.scandir" "2.1.5"
    fastq "^1.6.0"

"@pkgjs/parseargs@^0.11.0":
  version "0.11.0"
  resolved "https://registry.npmjs.org/@pkgjs/parseargs/-/parseargs-0.11.0.tgz"
  integrity sha512-+1VkjdD0QBLPodGrJUeqarH8VAIvQODIbwh9XpP5Syisf7YoQgsJKPNFoqqLQlu+VQ/tVSshMR6loPMn8U+dPg==

"@popperjs/core@^2.11.5", "@popperjs/core@^2.4.4":
  version "2.11.8"
  resolved "https://registry.npmjs.org/@popperjs/core/-/core-2.11.8.tgz"
  integrity sha512-P1st0aksCrn9sGZhp8GMYwBnQsbvAWsZAX44oXNNvLHGqAOcoVxmjZiohstwQ7SqKnbR47akdNi+uleWD8+g6A==

"@prisma/client@^5.6.0":
  version "5.6.0"
  resolved "https://registry.npmjs.org/@prisma/client/-/client-5.6.0.tgz"
  integrity sha512-mUDefQFa1wWqk4+JhKPYq8BdVoFk9NFMBXUI8jAkBfQTtgx8WPx02U2HB/XbAz3GSUJpeJOKJQtNvaAIDs6sug==
  dependencies:
    "@prisma/engines-version" "5.6.0-32.e95e739751f42d8ca026f6b910f5a2dc5adeaeee"

"@prisma/engines-version@5.6.0-32.e95e739751f42d8ca026f6b910f5a2dc5adeaeee":
  version "5.6.0-32.e95e739751f42d8ca026f6b910f5a2dc5adeaeee"
  resolved "https://registry.npmjs.org/@prisma/engines-version/-/engines-version-5.6.0-32.e95e739751f42d8ca026f6b910f5a2dc5adeaeee.tgz"
  integrity sha512-UoFgbV1awGL/3wXuUK3GDaX2SolqczeeJ5b4FVec9tzeGbSWJboPSbT0psSrmgYAKiKnkOPFSLlH6+b+IyOwAw==

"@prisma/engines@5.5.2":
  version "5.5.2"
  resolved "https://registry.npmjs.org/@prisma/engines/-/engines-5.5.2.tgz"
  integrity sha512-Be5hoNF8k+lkB3uEMiCHbhbfF6aj1GnrTBnn5iYFT7GEr3TsOEp1soviEcBR0tYCgHbxjcIxJMhdbvxALJhAqg==

"@react-aria/breadcrumbs@3.5.13":
  version "3.5.13"
  resolved "https://registry.npmjs.org/@react-aria/breadcrumbs/-/breadcrumbs-3.5.13.tgz"
  integrity sha512-G1Gqf/P6kVdfs94ovwP18fTWuIxadIQgHsXS08JEVcFVYMjb9YjqnEBaohUxD1tq2WldMbYw53ahQblT4NTG+g==
  dependencies:
    "@react-aria/i18n" "^3.11.1"
    "@react-aria/link" "^3.7.1"
    "@react-aria/utils" "^3.24.1"
    "@react-types/breadcrumbs" "^3.7.5"
    "@react-types/shared" "^3.23.1"
    "@swc/helpers" "^0.5.0"

"@react-aria/button@3.9.5":
  version "3.9.5"
  resolved "https://registry.npmjs.org/@react-aria/button/-/button-3.9.5.tgz"
  integrity sha512-dgcYR6j8WDOMLKuVrtxzx4jIC05cVKDzc+HnPO8lNkBAOfjcuN5tkGRtIjLtqjMvpZHhQT5aDbgFpIaZzxgFIg==
  dependencies:
    "@react-aria/focus" "^3.17.1"
    "@react-aria/interactions" "^3.21.3"
    "@react-aria/utils" "^3.24.1"
    "@react-stately/toggle" "^3.7.4"
    "@react-types/button" "^3.9.4"
    "@react-types/shared" "^3.23.1"
    "@swc/helpers" "^0.5.0"

"@react-aria/calendar@3.5.8":
  version "3.5.8"
  resolved "https://registry.npmjs.org/@react-aria/calendar/-/calendar-3.5.8.tgz"
  integrity sha512-Whlp4CeAA5/ZkzrAHUv73kgIRYjw088eYGSc+cvSOCxfrc/2XkBm9rNrnSBv0DvhJ8AG0Fjz3vYakTmF3BgZBw==
  dependencies:
    "@internationalized/date" "^3.5.4"
    "@react-aria/i18n" "^3.11.1"
    "@react-aria/interactions" "^3.21.3"
    "@react-aria/live-announcer" "^3.3.4"
    "@react-aria/utils" "^3.24.1"
    "@react-stately/calendar" "^3.5.1"
    "@react-types/button" "^3.9.4"
    "@react-types/calendar" "^3.4.6"
    "@react-types/shared" "^3.23.1"
    "@swc/helpers" "^0.5.0"

"@react-aria/checkbox@3.14.3":
  version "3.14.3"
  resolved "https://registry.npmjs.org/@react-aria/checkbox/-/checkbox-3.14.3.tgz"
  integrity sha512-EtBJL6iu0gvrw3A4R7UeVLR6diaVk/mh4kFBc7c8hQjpEJweRr4hmJT3hrNg3MBcTWLxFiMEXPGgWEwXDBygtA==
  dependencies:
    "@react-aria/form" "^3.0.5"
    "@react-aria/interactions" "^3.21.3"
    "@react-aria/label" "^3.7.8"
    "@react-aria/toggle" "^3.10.4"
    "@react-aria/utils" "^3.24.1"
    "@react-stately/checkbox" "^3.6.5"
    "@react-stately/form" "^3.0.3"
    "@react-stately/toggle" "^3.7.4"
    "@react-types/checkbox" "^3.8.1"
    "@react-types/shared" "^3.23.1"
    "@swc/helpers" "^0.5.0"

"@react-aria/combobox@3.9.1":
  version "3.9.1"
  resolved "https://registry.npmjs.org/@react-aria/combobox/-/combobox-3.9.1.tgz"
  integrity sha512-SpK92dCmT8qn8aEcUAihRQrBb5LZUhwIbDExFII8PvUvEFy/PoQHXIo3j1V29WkutDBDpMvBv/6XRCHGXPqrhQ==
  dependencies:
    "@react-aria/i18n" "^3.11.1"
    "@react-aria/listbox" "^3.12.1"
    "@react-aria/live-announcer" "^3.3.4"
    "@react-aria/menu" "^3.14.1"
    "@react-aria/overlays" "^3.22.1"
    "@react-aria/selection" "^3.18.1"
    "@react-aria/textfield" "^3.14.5"
    "@react-aria/utils" "^3.24.1"
    "@react-stately/collections" "^3.10.7"
    "@react-stately/combobox" "^3.8.4"
    "@react-stately/form" "^3.0.3"
    "@react-types/button" "^3.9.4"
    "@react-types/combobox" "^3.11.1"
    "@react-types/shared" "^3.23.1"
    "@swc/helpers" "^0.5.0"

"@react-aria/datepicker@3.10.1":
  version "3.10.1"
  resolved "https://registry.npmjs.org/@react-aria/datepicker/-/datepicker-3.10.1.tgz"
  integrity sha512-4HZL593nrNMa1GjBmWEN/OTvNS6d3/16G1YJWlqiUlv11ADulSbqBIjMmkgwrJVFcjrgqtXFy+yyrTA/oq94Zw==
  dependencies:
    "@internationalized/date" "^3.5.4"
    "@internationalized/number" "^3.5.3"
    "@internationalized/string" "^3.2.3"
    "@react-aria/focus" "^3.17.1"
    "@react-aria/form" "^3.0.5"
    "@react-aria/i18n" "^3.11.1"
    "@react-aria/interactions" "^3.21.3"
    "@react-aria/label" "^3.7.8"
    "@react-aria/spinbutton" "^3.6.5"
    "@react-aria/utils" "^3.24.1"
    "@react-stately/datepicker" "^3.9.4"
    "@react-stately/form" "^3.0.3"
    "@react-types/button" "^3.9.4"
    "@react-types/calendar" "^3.4.6"
    "@react-types/datepicker" "^3.7.4"
    "@react-types/dialog" "^3.5.10"
    "@react-types/shared" "^3.23.1"
    "@swc/helpers" "^0.5.0"

"@react-aria/dialog@3.5.14":
  version "3.5.14"
  resolved "https://registry.npmjs.org/@react-aria/dialog/-/dialog-3.5.14.tgz"
  integrity sha512-oqDCjQ8hxe3GStf48XWBf2CliEnxlR9GgSYPHJPUc69WBj68D9rVcCW3kogJnLAnwIyf3FnzbX4wSjvUa88sAQ==
  dependencies:
    "@react-aria/focus" "^3.17.1"
    "@react-aria/overlays" "^3.22.1"
    "@react-aria/utils" "^3.24.1"
    "@react-types/dialog" "^3.5.10"
    "@react-types/shared" "^3.23.1"
    "@swc/helpers" "^0.5.0"

"@react-aria/focus@^3.17.1", "@react-aria/focus@3.17.1":
  version "3.17.1"
  resolved "https://registry.npmjs.org/@react-aria/focus/-/focus-3.17.1.tgz"
  integrity sha512-FLTySoSNqX++u0nWZJPPN5etXY0WBxaIe/YuL/GTEeuqUIuC/2bJSaw5hlsM6T2yjy6Y/VAxBcKSdAFUlU6njQ==
  dependencies:
    "@react-aria/interactions" "^3.21.3"
    "@react-aria/utils" "^3.24.1"
    "@react-types/shared" "^3.23.1"
    "@swc/helpers" "^0.5.0"
    clsx "^2.0.0"

"@react-aria/focus@^3.19.0":
  version "3.19.0"
  resolved "https://registry.npmjs.org/@react-aria/focus/-/focus-3.19.0.tgz"
  integrity sha512-hPF9EXoUQeQl1Y21/rbV2H4FdUR2v+4/I0/vB+8U3bT1CJ+1AFj1hc/rqx2DqEwDlEwOHN+E4+mRahQmlybq0A==
  dependencies:
    "@react-aria/interactions" "^3.22.5"
    "@react-aria/utils" "^3.26.0"
    "@react-types/shared" "^3.26.0"
    "@swc/helpers" "^0.5.0"
    clsx "^2.0.0"

"@react-aria/form@^3.0.5", "@react-aria/form@3.0.5":
  version "3.0.5"
  resolved "https://registry.npmjs.org/@react-aria/form/-/form-3.0.5.tgz"
  integrity sha512-n290jRwrrRXO3fS82MyWR+OKN7yznVesy5Q10IclSTVYHHI3VI53xtAPr/WzNjJR1um8aLhOcDNFKwnNIUUCsQ==
  dependencies:
    "@react-aria/interactions" "^3.21.3"
    "@react-aria/utils" "^3.24.1"
    "@react-stately/form" "^3.0.3"
    "@react-types/shared" "^3.23.1"
    "@swc/helpers" "^0.5.0"

"@react-aria/grid@^3.9.1":
  version "3.11.0"
  resolved "https://registry.npmjs.org/@react-aria/grid/-/grid-3.11.0.tgz"
  integrity sha512-lN5FpQgu2Rq0CzTPWmzRpq6QHcMmzsXYeClsgO3108uVp1/genBNAObYVTxGOKe/jb9q99trz8EtIn05O6KN1g==
  dependencies:
    "@react-aria/focus" "^3.19.0"
    "@react-aria/i18n" "^3.12.4"
    "@react-aria/interactions" "^3.22.5"
    "@react-aria/live-announcer" "^3.4.1"
    "@react-aria/selection" "^3.21.0"
    "@react-aria/utils" "^3.26.0"
    "@react-stately/collections" "^3.12.0"
    "@react-stately/grid" "^3.10.0"
    "@react-stately/selection" "^3.18.0"
    "@react-types/checkbox" "^3.9.0"
    "@react-types/grid" "^3.2.10"
    "@react-types/shared" "^3.26.0"
    "@swc/helpers" "^0.5.0"

"@react-aria/i18n@^3.11.1", "@react-aria/i18n@3.11.1":
  version "3.11.1"
  resolved "https://registry.npmjs.org/@react-aria/i18n/-/i18n-3.11.1.tgz"
  integrity sha512-vuiBHw1kZruNMYeKkTGGnmPyMnM5T+gT8bz97H1FqIq1hQ6OPzmtBZ6W6l6OIMjeHI5oJo4utTwfZl495GALFQ==
  dependencies:
    "@internationalized/date" "^3.5.4"
    "@internationalized/message" "^3.1.4"
    "@internationalized/number" "^3.5.3"
    "@internationalized/string" "^3.2.3"
    "@react-aria/ssr" "^3.9.4"
    "@react-aria/utils" "^3.24.1"
    "@react-types/shared" "^3.23.1"
    "@swc/helpers" "^0.5.0"

"@react-aria/i18n@^3.12.4":
  version "3.12.4"
  resolved "https://registry.npmjs.org/@react-aria/i18n/-/i18n-3.12.4.tgz"
  integrity sha512-j9+UL3q0Ls8MhXV9gtnKlyozq4aM95YywXqnmJtzT1rYeBx7w28hooqrWkCYLfqr4OIryv1KUnPiCSLwC2OC7w==
  dependencies:
    "@internationalized/date" "^3.6.0"
    "@internationalized/message" "^3.1.6"
    "@internationalized/number" "^3.6.0"
    "@internationalized/string" "^3.2.5"
    "@react-aria/ssr" "^3.9.7"
    "@react-aria/utils" "^3.26.0"
    "@react-types/shared" "^3.26.0"
    "@swc/helpers" "^0.5.0"

"@react-aria/interactions@^3.21.3", "@react-aria/interactions@3.21.3":
  version "3.21.3"
  resolved "https://registry.npmjs.org/@react-aria/interactions/-/interactions-3.21.3.tgz"
  integrity sha512-BWIuf4qCs5FreDJ9AguawLVS0lV9UU+sK4CCnbCNNmYqOWY+1+gRXCsnOM32K+oMESBxilAjdHW5n1hsMqYMpA==
  dependencies:
    "@react-aria/ssr" "^3.9.4"
    "@react-aria/utils" "^3.24.1"
    "@react-types/shared" "^3.23.1"
    "@swc/helpers" "^0.5.0"

"@react-aria/interactions@^3.22.5":
  version "3.22.5"
  resolved "https://registry.npmjs.org/@react-aria/interactions/-/interactions-3.22.5.tgz"
  integrity sha512-kMwiAD9E0TQp+XNnOs13yVJghiy8ET8L0cbkeuTgNI96sOAp/63EJ1FSrDf17iD8sdjt41LafwX/dKXW9nCcLQ==
  dependencies:
    "@react-aria/ssr" "^3.9.7"
    "@react-aria/utils" "^3.26.0"
    "@react-types/shared" "^3.26.0"
    "@swc/helpers" "^0.5.0"

"@react-aria/label@^3.7.8", "@react-aria/label@3.7.8":
  version "3.7.8"
  resolved "https://registry.npmjs.org/@react-aria/label/-/label-3.7.8.tgz"
  integrity sha512-MzgTm5+suPA3KX7Ug6ZBK2NX9cin/RFLsv1BdafJ6CZpmUSpWnGE/yQfYUB7csN7j31OsZrD3/P56eShYWAQfg==
  dependencies:
    "@react-aria/utils" "^3.24.1"
    "@react-types/shared" "^3.23.1"
    "@swc/helpers" "^0.5.0"

"@react-aria/link@^3.7.1", "@react-aria/link@3.7.1":
  version "3.7.1"
  resolved "https://registry.npmjs.org/@react-aria/link/-/link-3.7.1.tgz"
  integrity sha512-a4IaV50P3fXc7DQvEIPYkJJv26JknFbRzFT5MJOMgtzuhyJoQdILEUK6XHYjcSSNCA7uLgzpojArVk5Hz3lCpw==
  dependencies:
    "@react-aria/focus" "^3.17.1"
    "@react-aria/interactions" "^3.21.3"
    "@react-aria/utils" "^3.24.1"
    "@react-types/link" "^3.5.5"
    "@react-types/shared" "^3.23.1"
    "@swc/helpers" "^0.5.0"

"@react-aria/listbox@^3.12.1", "@react-aria/listbox@3.12.1":
  version "3.12.1"
  resolved "https://registry.npmjs.org/@react-aria/listbox/-/listbox-3.12.1.tgz"
  integrity sha512-7JiUp0NGykbv/HgSpmTY1wqhuf/RmjFxs1HZcNaTv8A+DlzgJYc7yQqFjP3ZA/z5RvJFuuIxggIYmgIFjaRYdA==
  dependencies:
    "@react-aria/interactions" "^3.21.3"
    "@react-aria/label" "^3.7.8"
    "@react-aria/selection" "^3.18.1"
    "@react-aria/utils" "^3.24.1"
    "@react-stately/collections" "^3.10.7"
    "@react-stately/list" "^3.10.5"
    "@react-types/listbox" "^3.4.9"
    "@react-types/shared" "^3.23.1"
    "@swc/helpers" "^0.5.0"

"@react-aria/live-announcer@^3.3.4", "@react-aria/live-announcer@^3.4.1":
  version "3.4.1"
  resolved "https://registry.npmjs.org/@react-aria/live-announcer/-/live-announcer-3.4.1.tgz"
  integrity sha512-4X2mcxgqLvvkqxv2l1n00jTzUxxe0kkLiapBGH1LHX/CxA1oQcHDqv8etJ2ZOwmS/MSBBiWnv3DwYHDOF6ubig==
  dependencies:
    "@swc/helpers" "^0.5.0"

"@react-aria/menu@^3.14.1", "@react-aria/menu@3.14.1":
  version "3.14.1"
  resolved "https://registry.npmjs.org/@react-aria/menu/-/menu-3.14.1.tgz"
  integrity sha512-BYliRb38uAzq05UOFcD5XkjA5foQoXRbcH3ZufBsc4kvh79BcP1PMW6KsXKGJ7dC/PJWUwCui6QL1kUg8PqMHA==
  dependencies:
    "@react-aria/focus" "^3.17.1"
    "@react-aria/i18n" "^3.11.1"
    "@react-aria/interactions" "^3.21.3"
    "@react-aria/overlays" "^3.22.1"
    "@react-aria/selection" "^3.18.1"
    "@react-aria/utils" "^3.24.1"
    "@react-stately/collections" "^3.10.7"
    "@react-stately/menu" "^3.7.1"
    "@react-stately/tree" "^3.8.1"
    "@react-types/button" "^3.9.4"
    "@react-types/menu" "^3.9.9"
    "@react-types/shared" "^3.23.1"
    "@swc/helpers" "^0.5.0"

"@react-aria/overlays@^3.22.1", "@react-aria/overlays@3.22.1":
  version "3.22.1"
  resolved "https://registry.npmjs.org/@react-aria/overlays/-/overlays-3.22.1.tgz"
  integrity sha512-GHiFMWO4EQ6+j6b5QCnNoOYiyx1Gk8ZiwLzzglCI4q1NY5AG2EAmfU4Z1+Gtrf2S5Y0zHbumC7rs9GnPoGLUYg==
  dependencies:
    "@react-aria/focus" "^3.17.1"
    "@react-aria/i18n" "^3.11.1"
    "@react-aria/interactions" "^3.21.3"
    "@react-aria/ssr" "^3.9.4"
    "@react-aria/utils" "^3.24.1"
    "@react-aria/visually-hidden" "^3.8.12"
    "@react-stately/overlays" "^3.6.7"
    "@react-types/button" "^3.9.4"
    "@react-types/overlays" "^3.8.7"
    "@react-types/shared" "^3.23.1"
    "@swc/helpers" "^0.5.0"

"@react-aria/progress@3.4.13":
  version "3.4.13"
  resolved "https://registry.npmjs.org/@react-aria/progress/-/progress-3.4.13.tgz"
  integrity sha512-YBV9bOO5JzKvG8QCI0IAA00o6FczMgIDiK8Q9p5gKorFMatFUdRayxlbIPoYHMi+PguLil0jHgC7eOyaUcrZ0g==
  dependencies:
    "@react-aria/i18n" "^3.11.1"
    "@react-aria/label" "^3.7.8"
    "@react-aria/utils" "^3.24.1"
    "@react-types/progress" "^3.5.4"
    "@react-types/shared" "^3.23.1"
    "@swc/helpers" "^0.5.0"

"@react-aria/radio@3.10.4":
  version "3.10.4"
  resolved "https://registry.npmjs.org/@react-aria/radio/-/radio-3.10.4.tgz"
  integrity sha512-3fmoMcQtCpgjTwJReFjnvIE/C7zOZeCeWUn4JKDqz9s1ILYsC3Rk5zZ4q66tFn6v+IQnecrKT52wH6+hlVLwTA==
  dependencies:
    "@react-aria/focus" "^3.17.1"
    "@react-aria/form" "^3.0.5"
    "@react-aria/i18n" "^3.11.1"
    "@react-aria/interactions" "^3.21.3"
    "@react-aria/label" "^3.7.8"
    "@react-aria/utils" "^3.24.1"
    "@react-stately/radio" "^3.10.4"
    "@react-types/radio" "^3.8.1"
    "@react-types/shared" "^3.23.1"
    "@swc/helpers" "^0.5.0"

"@react-aria/selection@^3.18.1", "@react-aria/selection@3.18.1":
  version "3.18.1"
  resolved "https://registry.npmjs.org/@react-aria/selection/-/selection-3.18.1.tgz"
  integrity sha512-GSqN2jX6lh7v+ldqhVjAXDcrWS3N4IsKXxO6L6Ygsye86Q9q9Mq9twWDWWu5IjHD6LoVZLUBCMO+ENGbOkyqeQ==
  dependencies:
    "@react-aria/focus" "^3.17.1"
    "@react-aria/i18n" "^3.11.1"
    "@react-aria/interactions" "^3.21.3"
    "@react-aria/utils" "^3.24.1"
    "@react-stately/selection" "^3.15.1"
    "@react-types/shared" "^3.23.1"
    "@swc/helpers" "^0.5.0"

"@react-aria/selection@^3.21.0":
  version "3.21.0"
  resolved "https://registry.npmjs.org/@react-aria/selection/-/selection-3.21.0.tgz"
  integrity sha512-52JJ6hlPcM+gt0VV3DBmz6Kj1YAJr13TfutrKfGWcK36LvNCBm1j0N+TDqbdnlp8Nue6w0+5FIwZq44XPYiBGg==
  dependencies:
    "@react-aria/focus" "^3.19.0"
    "@react-aria/i18n" "^3.12.4"
    "@react-aria/interactions" "^3.22.5"
    "@react-aria/utils" "^3.26.0"
    "@react-stately/selection" "^3.18.0"
    "@react-types/shared" "^3.26.0"
    "@swc/helpers" "^0.5.0"

"@react-aria/slider@3.7.8":
  version "3.7.8"
  resolved "https://registry.npmjs.org/@react-aria/slider/-/slider-3.7.8.tgz"
  integrity sha512-MYvPcM0K8jxEJJicUK2+WxUkBIM/mquBxOTOSSIL3CszA80nXIGVnLlCUnQV3LOUzpWtabbWaZokSPtGgOgQOw==
  dependencies:
    "@react-aria/focus" "^3.17.1"
    "@react-aria/i18n" "^3.11.1"
    "@react-aria/interactions" "^3.21.3"
    "@react-aria/label" "^3.7.8"
    "@react-aria/utils" "^3.24.1"
    "@react-stately/slider" "^3.5.4"
    "@react-types/shared" "^3.23.1"
    "@react-types/slider" "^3.7.3"
    "@swc/helpers" "^0.5.0"

"@react-aria/spinbutton@^3.6.5":
  version "3.6.10"
  resolved "https://registry.npmjs.org/@react-aria/spinbutton/-/spinbutton-3.6.10.tgz"
  integrity sha512-nhYEYk7xUNOZDaqiQ5w/nHH9ouqjJbabTWXH+KK7UR1oVGfo4z1wG94l8KWF3Z6SGGnBxzLJyTBguZ4g9aYTSg==
  dependencies:
    "@react-aria/i18n" "^3.12.4"
    "@react-aria/live-announcer" "^3.4.1"
    "@react-aria/utils" "^3.26.0"
    "@react-types/button" "^3.10.1"
    "@react-types/shared" "^3.26.0"
    "@swc/helpers" "^0.5.0"

"@react-aria/ssr@^3.9.4", "@react-aria/ssr@3.9.4":
  version "3.9.4"
  resolved "https://registry.npmjs.org/@react-aria/ssr/-/ssr-3.9.4.tgz"
  integrity sha512-4jmAigVq409qcJvQyuorsmBR4+9r3+JEC60wC+Y0MZV0HCtTmm8D9guYXlJMdx0SSkgj0hHAyFm/HvPNFofCoQ==
  dependencies:
    "@swc/helpers" "^0.5.0"

"@react-aria/ssr@^3.9.7":
  version "3.9.7"
  resolved "https://registry.npmjs.org/@react-aria/ssr/-/ssr-3.9.7.tgz"
  integrity sha512-GQygZaGlmYjmYM+tiNBA5C6acmiDWF52Nqd40bBp0Znk4M4hP+LTmI0lpI1BuKMw45T8RIhrAsICIfKwZvi2Gg==
  dependencies:
    "@swc/helpers" "^0.5.0"

"@react-aria/switch@3.6.4":
  version "3.6.4"
  resolved "https://registry.npmjs.org/@react-aria/switch/-/switch-3.6.4.tgz"
  integrity sha512-2nVqz4ZuJyof47IpGSt3oZRmp+EdS8wzeDYgf42WHQXrx4uEOk1mdLJ20+NnsYhj/2NHZsvXVrjBeKMjlMs+0w==
  dependencies:
    "@react-aria/toggle" "^3.10.4"
    "@react-stately/toggle" "^3.7.4"
    "@react-types/switch" "^3.5.3"
    "@swc/helpers" "^0.5.0"

"@react-aria/table@3.14.1":
  version "3.14.1"
  resolved "https://registry.npmjs.org/@react-aria/table/-/table-3.14.1.tgz"
  integrity sha512-WaPgQe4zQF5OaluO5rm+Y2nEoFR63vsLd4BT4yjK1uaFhKhDY2Zk+1SCVQvBLLKS4WK9dhP05nrNzT0vp/ZPOw==
  dependencies:
    "@react-aria/focus" "^3.17.1"
    "@react-aria/grid" "^3.9.1"
    "@react-aria/i18n" "^3.11.1"
    "@react-aria/interactions" "^3.21.3"
    "@react-aria/live-announcer" "^3.3.4"
    "@react-aria/utils" "^3.24.1"
    "@react-aria/visually-hidden" "^3.8.12"
    "@react-stately/collections" "^3.10.7"
    "@react-stately/flags" "^3.0.3"
    "@react-stately/table" "^3.11.8"
    "@react-stately/virtualizer" "^3.7.1"
    "@react-types/checkbox" "^3.8.1"
    "@react-types/grid" "^3.2.6"
    "@react-types/shared" "^3.23.1"
    "@react-types/table" "^3.9.5"
    "@swc/helpers" "^0.5.0"

"@react-aria/tabs@3.9.1":
  version "3.9.1"
  resolved "https://registry.npmjs.org/@react-aria/tabs/-/tabs-3.9.1.tgz"
  integrity sha512-S5v/0sRcOaSXaJYZuuy1ZVzYc7JD4sDyseG1133GjyuNjJOFHgoWMb+b4uxNIJbZxnLgynn/ZDBZSO+qU+fIxw==
  dependencies:
    "@react-aria/focus" "^3.17.1"
    "@react-aria/i18n" "^3.11.1"
    "@react-aria/selection" "^3.18.1"
    "@react-aria/utils" "^3.24.1"
    "@react-stately/tabs" "^3.6.6"
    "@react-types/shared" "^3.23.1"
    "@react-types/tabs" "^3.3.7"
    "@swc/helpers" "^0.5.0"

"@react-aria/textfield@^3.14.5", "@react-aria/textfield@3.14.5":
  version "3.14.5"
  resolved "https://registry.npmjs.org/@react-aria/textfield/-/textfield-3.14.5.tgz"
  integrity sha512-hj7H+66BjB1iTKKaFXwSZBZg88YT+wZboEXZ0DNdQB2ytzoz/g045wBItUuNi4ZjXI3P+0AOZznVMYadWBAmiA==
  dependencies:
    "@react-aria/focus" "^3.17.1"
    "@react-aria/form" "^3.0.5"
    "@react-aria/label" "^3.7.8"
    "@react-aria/utils" "^3.24.1"
    "@react-stately/form" "^3.0.3"
    "@react-stately/utils" "^3.10.1"
    "@react-types/shared" "^3.23.1"
    "@react-types/textfield" "^3.9.3"
    "@swc/helpers" "^0.5.0"

"@react-aria/toggle@^3.10.4":
  version "3.10.10"
  resolved "https://registry.npmjs.org/@react-aria/toggle/-/toggle-3.10.10.tgz"
  integrity sha512-QwMT/vTNrbrILxWVHfd9zVQ3mV2NdBwyRu+DphVQiFAXcmc808LEaIX2n0lI6FCsUDC9ZejCyvzd91/YemdZ1Q==
  dependencies:
    "@react-aria/focus" "^3.19.0"
    "@react-aria/interactions" "^3.22.5"
    "@react-aria/utils" "^3.26.0"
    "@react-stately/toggle" "^3.8.0"
    "@react-types/checkbox" "^3.9.0"
    "@react-types/shared" "^3.26.0"
    "@swc/helpers" "^0.5.0"

"@react-aria/tooltip@3.7.4":
  version "3.7.4"
  resolved "https://registry.npmjs.org/@react-aria/tooltip/-/tooltip-3.7.4.tgz"
  integrity sha512-+XRx4HlLYqWY3fB8Z60bQi/rbWDIGlFUtXYbtoa1J+EyRWfhpvsYImP8qeeNO/vgjUtDy1j9oKa8p6App9mBMQ==
  dependencies:
    "@react-aria/focus" "^3.17.1"
    "@react-aria/interactions" "^3.21.3"
    "@react-aria/utils" "^3.24.1"
    "@react-stately/tooltip" "^3.4.9"
    "@react-types/shared" "^3.23.1"
    "@react-types/tooltip" "^3.4.9"
    "@swc/helpers" "^0.5.0"

"@react-aria/utils@^3.24.1", "@react-aria/utils@3.24.1":
  version "3.24.1"
  resolved "https://registry.npmjs.org/@react-aria/utils/-/utils-3.24.1.tgz"
  integrity sha512-O3s9qhPMd6n42x9sKeJ3lhu5V1Tlnzhu6Yk8QOvDuXf7UGuUjXf9mzfHJt1dYzID4l9Fwm8toczBzPM9t0jc8Q==
  dependencies:
    "@react-aria/ssr" "^3.9.4"
    "@react-stately/utils" "^3.10.1"
    "@react-types/shared" "^3.23.1"
    "@swc/helpers" "^0.5.0"
    clsx "^2.0.0"

"@react-aria/utils@^3.26.0":
  version "3.26.0"
  resolved "https://registry.npmjs.org/@react-aria/utils/-/utils-3.26.0.tgz"
  integrity sha512-LkZouGSjjQ0rEqo4XJosS4L3YC/zzQkfRM3KoqK6fUOmUJ9t0jQ09WjiF+uOoG9u+p30AVg3TrZRUWmoTS+koQ==
  dependencies:
    "@react-aria/ssr" "^3.9.7"
    "@react-stately/utils" "^3.10.5"
    "@react-types/shared" "^3.26.0"
    "@swc/helpers" "^0.5.0"
    clsx "^2.0.0"

"@react-aria/visually-hidden@^3.8.12", "@react-aria/visually-hidden@3.8.12":
  version "3.8.12"
  resolved "https://registry.npmjs.org/@react-aria/visually-hidden/-/visually-hidden-3.8.12.tgz"
  integrity sha512-Bawm+2Cmw3Xrlr7ARzl2RLtKh0lNUdJ0eNqzWcyx4c0VHUAWtThmH5l+HRqFUGzzutFZVo89SAy40BAbd0gjVw==
  dependencies:
    "@react-aria/interactions" "^3.21.3"
    "@react-aria/utils" "^3.24.1"
    "@react-types/shared" "^3.23.1"
    "@swc/helpers" "^0.5.0"

"@react-stately/calendar@^3.5.1", "@react-stately/calendar@3.5.1":
  version "3.5.1"
  resolved "https://registry.npmjs.org/@react-stately/calendar/-/calendar-3.5.1.tgz"
  integrity sha512-7l7QhqGUJ5AzWHfvZzbTe3J4t72Ht5BmhW4hlVI7flQXtfrmYkVtl3ZdytEZkkHmWGYZRW9b4IQTQGZxhtlElA==
  dependencies:
    "@internationalized/date" "^3.5.4"
    "@react-stately/utils" "^3.10.1"
    "@react-types/calendar" "^3.4.6"
    "@react-types/shared" "^3.23.1"
    "@swc/helpers" "^0.5.0"

"@react-stately/checkbox@^3.6.5", "@react-stately/checkbox@3.6.5":
  version "3.6.5"
  resolved "https://registry.npmjs.org/@react-stately/checkbox/-/checkbox-3.6.5.tgz"
  integrity sha512-IXV3f9k+LtmfQLE+DKIN41Q5QB/YBLDCB1YVx5PEdRp52S9+EACD5683rjVm8NVRDwjMi2SP6RnFRk7fVb5Azg==
  dependencies:
    "@react-stately/form" "^3.0.3"
    "@react-stately/utils" "^3.10.1"
    "@react-types/checkbox" "^3.8.1"
    "@react-types/shared" "^3.23.1"
    "@swc/helpers" "^0.5.0"

"@react-stately/collections@^3.10.7", "@react-stately/collections@3.10.7":
  version "3.10.7"
  resolved "https://registry.npmjs.org/@react-stately/collections/-/collections-3.10.7.tgz"
  integrity sha512-KRo5O2MWVL8n3aiqb+XR3vP6akmHLhLWYZEmPKjIv0ghQaEebBTrN3wiEjtd6dzllv0QqcWvDLM1LntNfJ2TsA==
  dependencies:
    "@react-types/shared" "^3.23.1"
    "@swc/helpers" "^0.5.0"

"@react-stately/collections@^3.12.0":
  version "3.12.0"
  resolved "https://registry.npmjs.org/@react-stately/collections/-/collections-3.12.0.tgz"
  integrity sha512-MfR9hwCxe5oXv4qrLUnjidwM50U35EFmInUeFf8i9mskYwWlRYS0O1/9PZ0oF1M0cKambaRHKEy98jczgb9ycA==
  dependencies:
    "@react-types/shared" "^3.26.0"
    "@swc/helpers" "^0.5.0"

"@react-stately/combobox@^3.8.4", "@react-stately/combobox@3.8.4":
  version "3.8.4"
  resolved "https://registry.npmjs.org/@react-stately/combobox/-/combobox-3.8.4.tgz"
  integrity sha512-iLVGvKRRz0TeJXZhZyK783hveHpYA6xovOSdzSD+WGYpiPXo1QrcrNoH3AE0Z2sHtorU+8nc0j58vh5PB+m2AA==
  dependencies:
    "@react-stately/collections" "^3.10.7"
    "@react-stately/form" "^3.0.3"
    "@react-stately/list" "^3.10.5"
    "@react-stately/overlays" "^3.6.7"
    "@react-stately/select" "^3.6.4"
    "@react-stately/utils" "^3.10.1"
    "@react-types/combobox" "^3.11.1"
    "@react-types/shared" "^3.23.1"
    "@swc/helpers" "^0.5.0"

"@react-stately/data@^3.10.1":
  version "3.10.3"
  resolved "https://registry.npmjs.org/@react-stately/data/-/data-3.10.3.tgz"
  integrity sha512-cC9mxCZU4N9GbdOB4g2/J8+W+860GvBd874to0ObSc/XOR4VbuIsxAFIabW5UwmJV+XaqqK4TUBG0C6YScXeWQ==
  dependencies:
    "@react-types/shared" "^3.21.0"
    "@swc/helpers" "^0.5.0"

"@react-stately/datepicker@^3.9.4", "@react-stately/datepicker@3.9.4":
  version "3.9.4"
  resolved "https://registry.npmjs.org/@react-stately/datepicker/-/datepicker-3.9.4.tgz"
  integrity sha512-yBdX01jn6gq4NIVvHIqdjBUPo+WN8Bujc4OnPw+ZnfA4jI0eIgq04pfZ84cp1LVXW0IB0VaCu1AlQ/kvtZjfGA==
  dependencies:
    "@internationalized/date" "^3.5.4"
    "@internationalized/string" "^3.2.3"
    "@react-stately/form" "^3.0.3"
    "@react-stately/overlays" "^3.6.7"
    "@react-stately/utils" "^3.10.1"
    "@react-types/datepicker" "^3.7.4"
    "@react-types/shared" "^3.23.1"
    "@swc/helpers" "^0.5.0"

"@react-stately/flags@^3.0.3":
  version "3.0.5"
  resolved "https://registry.npmjs.org/@react-stately/flags/-/flags-3.0.5.tgz"
  integrity sha512-6wks4csxUwPCp23LgJSnkBRhrWpd9jGd64DjcCTNB2AHIFu7Ab1W59pJpUL6TW7uAxVxdNKjgn6D1hlBy8qWsA==
  dependencies:
    "@swc/helpers" "^0.5.0"

"@react-stately/form@^3.0.3", "@react-stately/form@3.0.3":
  version "3.0.3"
  resolved "https://registry.npmjs.org/@react-stately/form/-/form-3.0.3.tgz"
  integrity sha512-92YYBvlHEWUGUpXgIaQ48J50jU9XrxfjYIN8BTvvhBHdD63oWgm8DzQnyT/NIAMzdLnhkg7vP+fjG8LjHeyIAg==
  dependencies:
    "@react-types/shared" "^3.23.1"
    "@swc/helpers" "^0.5.0"

"@react-stately/form@^3.1.0":
  version "3.1.0"
  resolved "https://registry.npmjs.org/@react-stately/form/-/form-3.1.0.tgz"
  integrity sha512-E2wxNQ0QaTyDHD0nJFtTSnEH9A3bpJurwxhS4vgcUmESHgjFEMLlC9irUSZKgvOgb42GAq+fHoWBsgKeTp9Big==
  dependencies:
    "@react-types/shared" "^3.26.0"
    "@swc/helpers" "^0.5.0"

"@react-stately/grid@^3.10.0", "@react-stately/grid@^3.8.7":
  version "3.10.0"
  resolved "https://registry.npmjs.org/@react-stately/grid/-/grid-3.10.0.tgz"
  integrity sha512-ii+DdsOBvCnHMgL0JvUfFwO1kiAPP19Bpdpl6zn/oOltk6F5TmnoyNrzyz+2///1hCiySI3FE1O7ujsAQs7a6Q==
  dependencies:
    "@react-stately/collections" "^3.12.0"
    "@react-stately/selection" "^3.18.0"
    "@react-types/grid" "^3.2.10"
    "@react-types/shared" "^3.26.0"
    "@swc/helpers" "^0.5.0"

"@react-stately/list@^3.10.5", "@react-stately/list@3.10.5":
  version "3.10.5"
  resolved "https://registry.npmjs.org/@react-stately/list/-/list-3.10.5.tgz"
  integrity sha512-fV9plO+6QDHiewsYIhboxcDhF17GO95xepC5ki0bKXo44gr14g/LSo/BMmsaMnV+1BuGdBunB05bO4QOIaigXA==
  dependencies:
    "@react-stately/collections" "^3.10.7"
    "@react-stately/selection" "^3.15.1"
    "@react-stately/utils" "^3.10.1"
    "@react-types/shared" "^3.23.1"
    "@swc/helpers" "^0.5.0"

"@react-stately/list@^3.11.1":
  version "3.11.1"
  resolved "https://registry.npmjs.org/@react-stately/list/-/list-3.11.1.tgz"
  integrity sha512-UCOpIvqBOjwLtk7zVTYWuKU1m1Oe61Q5lNar/GwHaV1nAiSQ8/yYlhr40NkBEs9X3plEfsV28UIpzOrYnu1tPg==
  dependencies:
    "@react-stately/collections" "^3.12.0"
    "@react-stately/selection" "^3.18.0"
    "@react-stately/utils" "^3.10.5"
    "@react-types/shared" "^3.26.0"
    "@swc/helpers" "^0.5.0"

"@react-stately/menu@^3.7.1", "@react-stately/menu@3.7.1":
  version "3.7.1"
  resolved "https://registry.npmjs.org/@react-stately/menu/-/menu-3.7.1.tgz"
  integrity sha512-mX1w9HHzt+xal1WIT2xGrTQsoLvDwuB2R1Er1MBABs//MsJzccycatcgV/J/28m6tO5M9iuFQQvLV+i1dCtodg==
  dependencies:
    "@react-stately/overlays" "^3.6.7"
    "@react-types/menu" "^3.9.9"
    "@react-types/shared" "^3.23.1"
    "@swc/helpers" "^0.5.0"

"@react-stately/overlays@^3.6.12":
  version "3.6.12"
  resolved "https://registry.npmjs.org/@react-stately/overlays/-/overlays-3.6.12.tgz"
  integrity sha512-QinvZhwZgj8obUyPIcyURSCjTZlqZYRRCS60TF8jH8ZpT0tEAuDb3wvhhSXuYA3Xo9EHLwvLjEf3tQKKdAQArw==
  dependencies:
    "@react-stately/utils" "^3.10.5"
    "@react-types/overlays" "^3.8.11"
    "@swc/helpers" "^0.5.0"

"@react-stately/overlays@^3.6.7", "@react-stately/overlays@3.6.7":
  version "3.6.7"
  resolved "https://registry.npmjs.org/@react-stately/overlays/-/overlays-3.6.7.tgz"
  integrity sha512-6zp8v/iNUm6YQap0loaFx6PlvN8C0DgWHNlrlzMtMmNuvjhjR0wYXVaTfNoUZBWj25tlDM81ukXOjpRXg9rLrw==
  dependencies:
    "@react-stately/utils" "^3.10.1"
    "@react-types/overlays" "^3.8.7"
    "@swc/helpers" "^0.5.0"

"@react-stately/radio@^3.10.4", "@react-stately/radio@3.10.4":
  version "3.10.4"
  resolved "https://registry.npmjs.org/@react-stately/radio/-/radio-3.10.4.tgz"
  integrity sha512-kCIc7tAl4L7Hu4Wt9l2jaa+MzYmAJm0qmC8G8yPMbExpWbLRu6J8Un80GZu+JxvzgDlqDyrVvyv9zFifwH/NkQ==
  dependencies:
    "@react-stately/form" "^3.0.3"
    "@react-stately/utils" "^3.10.1"
    "@react-types/radio" "^3.8.1"
    "@react-types/shared" "^3.23.1"
    "@swc/helpers" "^0.5.0"

"@react-stately/select@^3.6.4":
  version "3.6.9"
  resolved "https://registry.npmjs.org/@react-stately/select/-/select-3.6.9.tgz"
  integrity sha512-vASUDv7FhEYQURzM+JIwcusPv7/x/l3zHc/oKJPvoCl3aa9pwS8hZwS82SC00o2iFnrDscfDJju4IE/cd4hucg==
  dependencies:
    "@react-stately/form" "^3.1.0"
    "@react-stately/list" "^3.11.1"
    "@react-stately/overlays" "^3.6.12"
    "@react-types/select" "^3.9.8"
    "@react-types/shared" "^3.26.0"
    "@swc/helpers" "^0.5.0"

"@react-stately/selection@^3.15.1", "@react-stately/selection@^3.18.0":
  version "3.18.0"
  resolved "https://registry.npmjs.org/@react-stately/selection/-/selection-3.18.0.tgz"
  integrity sha512-6EaNNP3exxBhW2LkcRR4a3pg+3oDguZlBSqIVVR7lyahv/D8xXHRC4dX+m0mgGHJpsgjs7664Xx6c8v193TFxg==
  dependencies:
    "@react-stately/collections" "^3.12.0"
    "@react-stately/utils" "^3.10.5"
    "@react-types/shared" "^3.26.0"
    "@swc/helpers" "^0.5.0"

"@react-stately/slider@^3.5.4", "@react-stately/slider@3.5.4":
  version "3.5.4"
  resolved "https://registry.npmjs.org/@react-stately/slider/-/slider-3.5.4.tgz"
  integrity sha512-Jsf7K17dr93lkNKL9ij8HUcoM1sPbq8TvmibD6DhrK9If2lje+OOL8y4n4qreUnfMT56HCAeS9wCO3fg3eMyrw==
  dependencies:
    "@react-stately/utils" "^3.10.1"
    "@react-types/shared" "^3.23.1"
    "@react-types/slider" "^3.7.3"
    "@swc/helpers" "^0.5.0"

"@react-stately/table@^3.11.8", "@react-stately/table@3.11.8":
  version "3.11.8"
  resolved "https://registry.npmjs.org/@react-stately/table/-/table-3.11.8.tgz"
  integrity sha512-EdyRW3lT1/kAVDp5FkEIi1BQ7tvmD2YgniGdLuW/l9LADo0T+oxZqruv60qpUS6sQap+59Riaxl91ClDxrJnpg==
  dependencies:
    "@react-stately/collections" "^3.10.7"
    "@react-stately/flags" "^3.0.3"
    "@react-stately/grid" "^3.8.7"
    "@react-stately/selection" "^3.15.1"
    "@react-stately/utils" "^3.10.1"
    "@react-types/grid" "^3.2.6"
    "@react-types/shared" "^3.23.1"
    "@react-types/table" "^3.9.5"
    "@swc/helpers" "^0.5.0"

"@react-stately/tabs@^3.6.6", "@react-stately/tabs@3.6.6":
  version "3.6.6"
  resolved "https://registry.npmjs.org/@react-stately/tabs/-/tabs-3.6.6.tgz"
  integrity sha512-sOLxorH2uqjAA+v1ppkMCc2YyjgqvSGeBDgtR/lyPSDd4CVMoTExszROX2dqG0c8il9RQvzFuufUtQWMY6PgSA==
  dependencies:
    "@react-stately/list" "^3.10.5"
    "@react-types/shared" "^3.23.1"
    "@react-types/tabs" "^3.3.7"
    "@swc/helpers" "^0.5.0"

"@react-stately/toggle@^3.7.4", "@react-stately/toggle@3.7.4":
  version "3.7.4"
  resolved "https://registry.npmjs.org/@react-stately/toggle/-/toggle-3.7.4.tgz"
  integrity sha512-CoYFe9WrhLkDP4HGDpJYQKwfiYCRBAeoBQHv+JWl5eyK61S8xSwoHsveYuEZ3bowx71zyCnNAqWRrmNOxJ4CKA==
  dependencies:
    "@react-stately/utils" "^3.10.1"
    "@react-types/checkbox" "^3.8.1"
    "@swc/helpers" "^0.5.0"

"@react-stately/toggle@^3.8.0":
  version "3.8.0"
  resolved "https://registry.npmjs.org/@react-stately/toggle/-/toggle-3.8.0.tgz"
  integrity sha512-pyt/k/J8BwE/2g6LL6Z6sMSWRx9HEJB83Sm/MtovXnI66sxJ2EfQ1OaXB7Su5PEL9OMdoQF6Mb+N1RcW3zAoPw==
  dependencies:
    "@react-stately/utils" "^3.10.5"
    "@react-types/checkbox" "^3.9.0"
    "@react-types/shared" "^3.26.0"
    "@swc/helpers" "^0.5.0"

"@react-stately/tooltip@^3.4.9", "@react-stately/tooltip@3.4.9":
  version "3.4.9"
  resolved "https://registry.npmjs.org/@react-stately/tooltip/-/tooltip-3.4.9.tgz"
  integrity sha512-P7CDJsdoKarz32qFwf3VNS01lyC+63gXpDZG31pUu+EO5BeQd4WKN/AH1Beuswpr4GWzxzFc1aXQgERFGVzraA==
  dependencies:
    "@react-stately/overlays" "^3.6.7"
    "@react-types/tooltip" "^3.4.9"
    "@swc/helpers" "^0.5.0"

"@react-stately/tree@^3.8.1", "@react-stately/tree@3.8.1":
  version "3.8.1"
  resolved "https://registry.npmjs.org/@react-stately/tree/-/tree-3.8.1.tgz"
  integrity sha512-LOdkkruJWch3W89h4B/bXhfr0t0t1aRfEp+IMrrwdRAl23NaPqwl5ILHs4Xu5XDHqqhg8co73pHrJwUyiTWEjw==
  dependencies:
    "@react-stately/collections" "^3.10.7"
    "@react-stately/selection" "^3.15.1"
    "@react-stately/utils" "^3.10.1"
    "@react-types/shared" "^3.23.1"
    "@swc/helpers" "^0.5.0"

"@react-stately/utils@^3.10.1", "@react-stately/utils@3.10.1":
  version "3.10.1"
  resolved "https://registry.npmjs.org/@react-stately/utils/-/utils-3.10.1.tgz"
  integrity sha512-VS/EHRyicef25zDZcM/ClpzYMC5i2YGN6uegOeQawmgfGjb02yaCX0F0zR69Pod9m2Hr3wunTbtpgVXvYbZItg==
  dependencies:
    "@swc/helpers" "^0.5.0"

"@react-stately/utils@^3.10.5":
  version "3.10.5"
  resolved "https://registry.npmjs.org/@react-stately/utils/-/utils-3.10.5.tgz"
  integrity sha512-iMQSGcpaecghDIh3mZEpZfoFH3ExBwTtuBEcvZ2XnGzCgQjeYXcMdIUwAfVQLXFTdHUHGF6Gu6/dFrYsCzySBQ==
  dependencies:
    "@swc/helpers" "^0.5.0"

"@react-stately/virtualizer@^3.7.1", "@react-stately/virtualizer@3.7.1":
  version "3.7.1"
  resolved "https://registry.npmjs.org/@react-stately/virtualizer/-/virtualizer-3.7.1.tgz"
  integrity sha512-voHgE6EQ+oZaLv6u2umKxakvIKNkCQuUihqKACTjdslp7SJh4Mvs3oLBI0hf0JOh+rCcFIKDvQtFwy1fXFRYBA==
  dependencies:
    "@react-aria/utils" "^3.24.1"
    "@react-types/shared" "^3.23.1"
    "@swc/helpers" "^0.5.0"

"@react-types/accordion@3.0.0-alpha.21":
  version "3.0.0-alpha.21"
  resolved "https://registry.npmjs.org/@react-types/accordion/-/accordion-3.0.0-alpha.21.tgz"
  integrity sha512-cbE06jH/ZoI+1898xd7ocQ/A/Rtkz8wTJAVOYgc8VRY1SYNQ/XZTGH5T6dD6aERAmiDwL/kjD7xhsE80DyaEKA==
  dependencies:
    "@react-types/shared" "^3.23.1"

"@react-types/breadcrumbs@^3.7.5", "@react-types/breadcrumbs@3.7.5":
  version "3.7.5"
  resolved "https://registry.npmjs.org/@react-types/breadcrumbs/-/breadcrumbs-3.7.5.tgz"
  integrity sha512-lV9IDYsMiu2TgdMIjEmsOE0YWwjb3jhUNK1DCZZfq6uWuiHLgyx2EncazJBUWSjHJ4ta32j7xTuXch+8Ai6u/A==
  dependencies:
    "@react-types/link" "^3.5.5"
    "@react-types/shared" "^3.23.1"

"@react-types/button@^3.10.1":
  version "3.10.1"
  resolved "https://registry.npmjs.org/@react-types/button/-/button-3.10.1.tgz"
  integrity sha512-XTtap8o04+4QjPNAshFWOOAusUTxQlBjU2ai0BTVLShQEjHhRVDBIWsI2B2FKJ4KXT6AZ25llaxhNrreWGonmA==
  dependencies:
    "@react-types/shared" "^3.26.0"

"@react-types/button@^3.9.4", "@react-types/button@3.9.4":
  version "3.9.4"
  resolved "https://registry.npmjs.org/@react-types/button/-/button-3.9.4.tgz"
  integrity sha512-raeQBJUxBp0axNF74TXB8/H50GY8Q3eV6cEKMbZFP1+Dzr09Ngv0tJBeW0ewAxAguNH5DRoMUAUGIXtSXskVdA==
  dependencies:
    "@react-types/shared" "^3.23.1"

"@react-types/calendar@^3.4.6", "@react-types/calendar@3.4.6":
  version "3.4.6"
  resolved "https://registry.npmjs.org/@react-types/calendar/-/calendar-3.4.6.tgz"
  integrity sha512-WSntZPwtvsIYWvBQRAPvuCn55UTJBZroTvX0vQvWykJRQnPAI20G1hMQ3dNsnAL+gLZUYxBXn66vphmjUuSYew==
  dependencies:
    "@internationalized/date" "^3.5.4"
    "@react-types/shared" "^3.23.1"

"@react-types/checkbox@^3.8.1", "@react-types/checkbox@3.8.1":
  version "3.8.1"
  resolved "https://registry.npmjs.org/@react-types/checkbox/-/checkbox-3.8.1.tgz"
  integrity sha512-5/oVByPw4MbR/8QSdHCaalmyWC71H/QGgd4aduTJSaNi825o+v/hsN2/CH7Fq9atkLKsC8fvKD00Bj2VGaKriQ==
  dependencies:
    "@react-types/shared" "^3.23.1"

"@react-types/checkbox@^3.9.0":
  version "3.9.0"
  resolved "https://registry.npmjs.org/@react-types/checkbox/-/checkbox-3.9.0.tgz"
  integrity sha512-9hbHx0Oo2Hp5a8nV8Q75LQR0DHtvOIJbFaeqESSopqmV9EZoYjtY/h0NS7cZetgahQgnqYWQi44XGooMDCsmxA==
  dependencies:
    "@react-types/shared" "^3.26.0"

"@react-types/combobox@^3.11.1", "@react-types/combobox@3.11.1":
  version "3.11.1"
  resolved "https://registry.npmjs.org/@react-types/combobox/-/combobox-3.11.1.tgz"
  integrity sha512-UNc3OHt5cUt5gCTHqhQIqhaWwKCpaNciD8R7eQazmHiA9fq8ROlV+7l3gdNgdhJbTf5Bu/V5ISnN7Y1xwL3zqQ==
  dependencies:
    "@react-types/shared" "^3.23.1"

"@react-types/datepicker@^3.7.4", "@react-types/datepicker@3.7.4":
  version "3.7.4"
  resolved "https://registry.npmjs.org/@react-types/datepicker/-/datepicker-3.7.4.tgz"
  integrity sha512-ZfvgscvNzBJpYyVWg3nstJtA/VlWLwErwSkd1ivZYam859N30w8yH+4qoYLa6FzWLCFlrsRHyvtxlEM7lUAt5A==
  dependencies:
    "@internationalized/date" "^3.5.4"
    "@react-types/calendar" "^3.4.6"
    "@react-types/overlays" "^3.8.7"
    "@react-types/shared" "^3.23.1"

"@react-types/dialog@^3.5.10":
  version "3.5.14"
  resolved "https://registry.npmjs.org/@react-types/dialog/-/dialog-3.5.14.tgz"
  integrity sha512-OXWMjrALwrlgw8aHD8SeRm/s3tbAssdaEh2h73KUSeFau3fU3n5mfKv+WnFqsEaOtN261o48l7hTlS6615H9AA==
  dependencies:
    "@react-types/overlays" "^3.8.11"
    "@react-types/shared" "^3.26.0"

"@react-types/grid@^3.2.10":
  version "3.2.10"
  resolved "https://registry.npmjs.org/@react-types/grid/-/grid-3.2.10.tgz"
  integrity sha512-Z5cG0ITwqjUE4kWyU5/7VqiPl4wqMJ7kG/ZP7poAnLmwRsR8Ai0ceVn+qzp5nTA19cgURi8t3LsXn3Ar1FBoog==
  dependencies:
    "@react-types/shared" "^3.26.0"

"@react-types/grid@^3.2.6", "@react-types/grid@3.2.6":
  version "3.2.6"
  resolved "https://registry.npmjs.org/@react-types/grid/-/grid-3.2.6.tgz"
  integrity sha512-XfHenL2jEBUYrhKiPdeM24mbLRXUn79wVzzMhrNYh24nBwhsPPpxF+gjFddT3Cy8dt6tRInfT6pMEu9nsXwaHw==
  dependencies:
    "@react-types/shared" "^3.23.1"

"@react-types/link@^3.5.5", "@react-types/link@3.5.5":
  version "3.5.5"
  resolved "https://registry.npmjs.org/@react-types/link/-/link-3.5.5.tgz"
  integrity sha512-G6P5WagHDR87npN7sEuC5IIgL1GsoY4WFWKO4734i2CXRYx24G9P0Su3AX4GA3qpspz8sK1AWkaCzBMmvnunfw==
  dependencies:
    "@react-types/shared" "^3.23.1"

"@react-types/listbox@^3.4.9":
  version "3.5.3"
  resolved "https://registry.npmjs.org/@react-types/listbox/-/listbox-3.5.3.tgz"
  integrity sha512-v1QXd9/XU3CCKr2Vgs7WLcTr6VMBur7CrxHhWZQQFExsf9bgJ/3wbUdjy4aThY/GsYHiaS38EKucCZFr1QAfqA==
  dependencies:
    "@react-types/shared" "^3.26.0"

"@react-types/menu@^3.9.9", "@react-types/menu@3.9.9":
  version "3.9.9"
  resolved "https://registry.npmjs.org/@react-types/menu/-/menu-3.9.9.tgz"
  integrity sha512-FamUaPVs1Fxr4KOMI0YcR2rYZHoN7ypGtgiEiJ11v/tEPjPPGgeKDxii0McCrdOkjheatLN1yd2jmMwYj6hTDg==
  dependencies:
    "@react-types/overlays" "^3.8.7"
    "@react-types/shared" "^3.23.1"

"@react-types/overlays@^3.8.11":
  version "3.8.11"
  resolved "https://registry.npmjs.org/@react-types/overlays/-/overlays-3.8.11.tgz"
  integrity sha512-aw7T0rwVI3EuyG5AOaEIk8j7dZJQ9m34XAztXJVZ/W2+4pDDkLDbJ/EAPnuo2xGYRGhowuNDn4tDju01eHYi+w==
  dependencies:
    "@react-types/shared" "^3.26.0"

"@react-types/overlays@^3.8.7", "@react-types/overlays@3.8.7":
  version "3.8.7"
  resolved "https://registry.npmjs.org/@react-types/overlays/-/overlays-3.8.7.tgz"
  integrity sha512-zCOYvI4at2DkhVpviIClJ7bRrLXYhSg3Z3v9xymuPH3mkiuuP/dm8mUCtkyY4UhVeUTHmrQh1bzaOP00A+SSQA==
  dependencies:
    "@react-types/shared" "^3.23.1"

"@react-types/progress@^3.5.4", "@react-types/progress@3.5.4":
  version "3.5.4"
  resolved "https://registry.npmjs.org/@react-types/progress/-/progress-3.5.4.tgz"
  integrity sha512-JNc246sTjasPyx5Dp7/s0rp3Bz4qlu4LrZTulZlxWyb53WgBNL7axc26CCi+I20rWL9+c7JjhrRxnLl/1cLN5g==
  dependencies:
    "@react-types/shared" "^3.23.1"

"@react-types/radio@^3.8.1", "@react-types/radio@3.8.1":
  version "3.8.1"
  resolved "https://registry.npmjs.org/@react-types/radio/-/radio-3.8.1.tgz"
  integrity sha512-bK0gio/qj1+0Ldu/3k/s9BaOZvnnRgvFtL3u5ky479+aLG5qf1CmYed3SKz8ErZ70JkpuCSrSwSCFf0t1IHovw==
  dependencies:
    "@react-types/shared" "^3.23.1"

"@react-types/select@^3.9.8":
  version "3.9.8"
  resolved "https://registry.npmjs.org/@react-types/select/-/select-3.9.8.tgz"
  integrity sha512-RGsYj2oFjXpLnfcvWMBQnkcDuKkwT43xwYWZGI214/gp/B64tJiIUgTM5wFTRAeGDX23EePkhCQF+9ctnqFd6g==
  dependencies:
    "@react-types/shared" "^3.26.0"

"@react-types/select@3.9.4":
  version "3.9.4"
  resolved "https://registry.npmjs.org/@react-types/select/-/select-3.9.4.tgz"
  integrity sha512-xI7dnOW2st91fPPcv6hdtrTdcfetYiqZuuVPZ5TRobY7Q10/Zqqe/KqtOw1zFKUj9xqNJe4Ov3xP5GSdcO60Eg==
  dependencies:
    "@react-types/shared" "^3.23.1"

"@react-types/shared@^3.21.0", "@react-types/shared@^3.23.1", "@react-types/shared@3.23.1":
  version "3.23.1"
  resolved "https://registry.npmjs.org/@react-types/shared/-/shared-3.23.1.tgz"
  integrity sha512-5d+3HbFDxGZjhbMBeFHRQhexMFt4pUce3okyRtUVKbbedQFUrtXSBg9VszgF2RTeQDKDkMCIQDtz5ccP/Lk1gw==

"@react-types/shared@^3.26.0":
  version "3.26.0"
  resolved "https://registry.npmjs.org/@react-types/shared/-/shared-3.26.0.tgz"
  integrity sha512-6FuPqvhmjjlpEDLTiYx29IJCbCNWPlsyO+ZUmCUXzhUv2ttShOXfw8CmeHWHftT/b2KweAWuzqSlfeXPR76jpw==

"@react-types/slider@^3.7.3":
  version "3.7.7"
  resolved "https://registry.npmjs.org/@react-types/slider/-/slider-3.7.7.tgz"
  integrity sha512-lYTR9zXQV2fSEm/G3gwDENWiki1IXd/oorsgf0zu1DBi2SQDbOsLsGUXiwvD24Xy6OkUuhAqjLPPexezo7+u9g==
  dependencies:
    "@react-types/shared" "^3.26.0"

"@react-types/switch@^3.5.3":
  version "3.5.7"
  resolved "https://registry.npmjs.org/@react-types/switch/-/switch-3.5.7.tgz"
  integrity sha512-1IKiq510rPTHumEZuhxuazuXBa2Cuxz6wBIlwf3NCVmgWEvU+uk1ETG0sH2yymjwCqhtJDKXi+qi9HSgPEDwAg==
  dependencies:
    "@react-types/shared" "^3.26.0"

"@react-types/table@^3.9.5", "@react-types/table@3.9.5":
  version "3.9.5"
  resolved "https://registry.npmjs.org/@react-types/table/-/table-3.9.5.tgz"
  integrity sha512-fgM2j9F/UR4Anmd28CueghCgBwOZoCVyN8fjaIFPd2MN4gCwUUfANwxLav65gZk4BpwUXGoQdsW+X50L3555mg==
  dependencies:
    "@react-types/grid" "^3.2.6"
    "@react-types/shared" "^3.23.1"

"@react-types/tabs@^3.3.7", "@react-types/tabs@3.3.7":
  version "3.3.7"
  resolved "https://registry.npmjs.org/@react-types/tabs/-/tabs-3.3.7.tgz"
  integrity sha512-ZdLe5xOcFX6+/ni45Dl2jO0jFATpTnoSqj6kLIS/BYv8oh0n817OjJkLf+DS3CLfNjApJWrHqAk34xNh6nRnEg==
  dependencies:
    "@react-types/shared" "^3.23.1"

"@react-types/textfield@^3.9.3", "@react-types/textfield@3.9.3":
  version "3.9.3"
  resolved "https://registry.npmjs.org/@react-types/textfield/-/textfield-3.9.3.tgz"
  integrity sha512-DoAY6cYOL0pJhgNGI1Rosni7g72GAt4OVr2ltEx2S9ARmFZ0DBvdhA9lL2nywcnKMf27PEJcKMXzXc10qaHsJw==
  dependencies:
    "@react-types/shared" "^3.23.1"

"@react-types/tooltip@^3.4.9", "@react-types/tooltip@3.4.9":
  version "3.4.9"
  resolved "https://registry.npmjs.org/@react-types/tooltip/-/tooltip-3.4.9.tgz"
  integrity sha512-wZ+uF1+Zc43qG+cOJzioBmLUNjRa7ApdcT0LI1VvaYvH5GdfjzUJOorLX9V/vAci0XMJ50UZ+qsh79aUlw2yqg==
  dependencies:
    "@react-types/overlays" "^3.8.7"
    "@react-types/shared" "^3.23.1"

"@rushstack/eslint-patch@^1.1.3":
  version "1.5.1"
  resolved "https://registry.npmjs.org/@rushstack/eslint-patch/-/eslint-patch-1.5.1.tgz"
  integrity sha512-6i/8UoL0P5y4leBIGzvkZdS85RDMG9y1ihZzmTZQ5LdHUYmZ7pKFoj8X0236s3lusPs1Fa5HTQUpwI+UfTcmeA==

"@swc/helpers@^0.5.0":
  version "0.5.3"
  resolved "https://registry.npmjs.org/@swc/helpers/-/helpers-0.5.3.tgz"
  integrity sha512-FaruWX6KdudYloq1AHD/4nU+UsMTdNE8CKyrseXWEcgjDAbvkwJg2QGPAnfIJLIWsjZOSPLOAykK6fuYp4vp4A==
  dependencies:
    tslib "^2.4.0"

"@tinymce/tinymce-react@^4.3.0":
  version "4.3.0"
  resolved "https://registry.npmjs.org/@tinymce/tinymce-react/-/tinymce-react-4.3.0.tgz"
  integrity sha512-iB4cUsYfcJL4NGuKhqCGYuTmFTje3nPxyPv1HxprTsp/YMGuuiiSNWrv3zwI31QX5Cn8qeq9MrMDnbxuRugHyg==
  dependencies:
    prop-types "^15.6.2"
    tinymce "^6.3.1"

"@types/cookie@^0.4.1":
  version "0.4.1"
  resolved "https://registry.npmjs.org/@types/cookie/-/cookie-0.4.1.tgz"
  integrity sha512-XW/Aa8APYr6jSVVA1y/DEIZX0/GMKLEVekNG727R8cs56ahETkRAy/3DR7+fJyh7oUgGwNQaRfXCun0+KbWY7Q==

"@types/eslint-scope@^3.7.7":
  version "3.7.7"
  resolved "https://registry.npmjs.org/@types/eslint-scope/-/eslint-scope-3.7.7.tgz"
  integrity sha512-MzMFlSLBqNF2gcHWO0G1vP/YQyfvrxZ0bF+u7mzUdZ1/xK4A4sru+nraZz5i3iEIk1l1uyicaDVTB4QbbEkAYg==
  dependencies:
    "@types/eslint" "*"
    "@types/estree" "*"

"@types/eslint@*":
  version "9.6.1"
  resolved "https://registry.npmjs.org/@types/eslint/-/eslint-9.6.1.tgz"
  integrity sha512-FXx2pKgId/WyYo2jXw63kk7/+TY7u7AziEJxJAnSFzHlqTAS3Ync6SvgYAN/k4/PQpnnVuzoMuVnByKK2qp0ag==
  dependencies:
    "@types/estree" "*"
    "@types/json-schema" "*"

"@types/estree@*", "@types/estree@^1.0.6":
  version "1.0.6"
  resolved "https://registry.npmjs.org/@types/estree/-/estree-1.0.6.tgz"
  integrity sha512-AYnb1nQyY49te+VRAVgmzfcgjYS91mY5P0TKUDCLEM+gNnA+3T6rWITXRLYCpahpqSQbN5cE+gHpnPyXjHWxcw==

"@types/is-hotkey@^0.1.1":
  version "0.1.8"
  resolved "https://registry.npmjs.org/@types/is-hotkey/-/is-hotkey-0.1.8.tgz"
  integrity sha512-4zW6OgrfVWR14IqHt32L5zpsE5IJgAu9uimQmAOFPdKPdv+M5RgXeoB2UCJZSKvVNGzUdLgbKdtCSZ66N2HdTA==

"@types/json-schema@*", "@types/json-schema@^7.0.8":
  version "7.0.14"
  resolved "https://registry.npmjs.org/@types/json-schema/-/json-schema-7.0.14.tgz"
  integrity sha512-U3PUjAudAdJBeC2pgN8uTIKgxrb4nlDF3SF0++EldXQvQBGkpFZMSnwQiIoDU77tv45VgNkl/L4ouD+rEomujw==

"@types/json5@^0.0.29":
  version "0.0.29"
  resolved "https://registry.npmjs.org/@types/json5/-/json5-0.0.29.tgz"
  integrity sha512-dRLjCWHYg4oaA77cxO64oO+7JwCwnIzkZPdrrC71jQmQtlhM556pwKo5bUzqvZndkVbeFLIIi+9TC40JNF5hNQ==

"@types/lodash.debounce@^4.0.7":
  version "4.0.9"
  resolved "https://registry.npmjs.org/@types/lodash.debounce/-/lodash.debounce-4.0.9.tgz"
  integrity sha512-Ma5JcgTREwpLRwMM+XwBR7DaWe96nC38uCBDFKZWbNKD+osjVzdpnUSwBcqCptrp16sSOLBAUb50Car5I0TCsQ==
  dependencies:
    "@types/lodash" "*"

"@types/lodash@*", "@types/lodash@^4.14.149":
  version "4.17.13"
  resolved "https://registry.npmjs.org/@types/lodash/-/lodash-4.17.13.tgz"
  integrity sha512-lfx+dftrEZcdBPczf9d0Qv0x+j/rfNCMuC6OcfXmO8gkfeNAY88PgKUbvG56whcN23gc27yenwF6oJZXGFpYxg==

"@types/node@*", "@types/node@17.0.39":
  version "17.0.39"
  resolved "https://registry.npmjs.org/@types/node/-/node-17.0.39.tgz"
  integrity sha512-JDU3YLlnPK3WDao6/DlXLOgSNpG13ct+CwIO17V8q0/9fWJyeMJJ/VyZ1lv8kDprihvZMydzVwf0tQOqGiY2Nw==

"@types/node@^16.10.2":
  version "16.18.59"
  resolved "https://registry.npmjs.org/@types/node/-/node-16.18.59.tgz"
  integrity sha512-PJ1w2cNeKUEdey4LiPra0ZuxZFOGvetswE8qHRriV/sUkL5Al4tTmPV9D2+Y/TPIxTHHgxTfRjZVKWhPw/ORhQ==

"@types/nprogress@0.2.0":
  version "0.2.0"
  resolved "https://registry.npmjs.org/@types/nprogress/-/nprogress-0.2.0.tgz"
  integrity sha512-1cYJrqq9GezNFPsWTZpFut/d4CjpZqA0vhqDUPFWYKF1oIyBz5qnoYMzR+0C/T96t3ebLAC1SSnwrVOm5/j74A==

"@types/numeral@2.0.2":
  version "2.0.2"
  resolved "https://registry.npmjs.org/@types/numeral/-/numeral-2.0.2.tgz"
  integrity sha512-A8F30k2gYJ/6e07spSCPpkuZu79LCnkPTvqmIWQzNGcrzwFKpVOydG41lNt5wZXjSI149qjyzC2L1+F2PD/NUA==

"@types/parse-json@^4.0.0":
  version "4.0.1"
  resolved "https://registry.npmjs.org/@types/parse-json/-/parse-json-4.0.1.tgz"
  integrity sha512-3YmXzzPAdOTVljVMkTMBdBEvlOLg2cDQaDhnnhT3nT9uDbnJzjWhKlzb+desT12Y7tGqaN6d+AbozcKzyL36Ng==

"@types/prop-types@*", "@types/prop-types@^15.7.8":
  version "15.7.9"
  resolved "https://registry.npmjs.org/@types/prop-types/-/prop-types-15.7.9.tgz"
  integrity sha512-n1yyPsugYNSmHgxDFjicaI2+gCNjsBck8UX9kuofAKlc0h1bL+20oSF72KeNaW2DUlesbEVCFgyV2dPGTiY42g==

"@types/raf@^3.4.0":
  version "3.4.2"
  resolved "https://registry.npmjs.org/@types/raf/-/raf-3.4.2.tgz"
  integrity sha512-sM4HyDVlDFl4goOXPF+g9nNHJFZQGot+HgySjM4cRjqXzjdatcEvYrtG4Ia8XumR9T6k8G2tW9B7hnUj51Uf0A==

"@types/react-dom@17.0.13":
  version "17.0.13"
  resolved "https://registry.npmjs.org/@types/react-dom/-/react-dom-17.0.13.tgz"
  integrity sha512-wEP+B8hzvy6ORDv1QBhcQia4j6ea4SFIBttHYpXKPFZRviBvknq0FRh3VrIxeXUmsPkwuXVZrVGG7KUVONmXCQ==
  dependencies:
    "@types/react" "*"

"@types/react-qr-reader@^2.1.6":
  version "2.1.6"
  resolved "https://registry.npmjs.org/@types/react-qr-reader/-/react-qr-reader-2.1.6.tgz"
  integrity sha512-KF3WXsCUczlVasxLTiXNy0bO3043g/qWyYdklFK2xyZuqVyQZyzAY5Cg0+55DZ1WFmyQoL5eIqWndnlMk6RyWg==
  dependencies:
    "@types/react" "*"

"@types/react-transition-group@^4.4.4":
  version "4.4.8"
  resolved "https://registry.npmjs.org/@types/react-transition-group/-/react-transition-group-4.4.8.tgz"
  integrity sha512-QmQ22q+Pb+HQSn04NL3HtrqHwYMf4h3QKArOy5F8U5nEVMaihBs3SR10WiOM1iwPz5jIo8x/u11al+iEGZZrvg==
  dependencies:
    "@types/react" "*"

"@types/react@*":
  version "18.2.33"
  resolved "https://registry.npmjs.org/@types/react/-/react-18.2.33.tgz"
  integrity sha512-v+I7S+hu3PIBoVkKGpSYYpiBT1ijqEzWpzQD62/jm4K74hPpSP7FF9BnKG6+fg2+62weJYkkBWDJlZt5JO/9hg==
  dependencies:
    "@types/prop-types" "*"
    "@types/scheduler" "*"
    csstype "^3.0.2"

"@types/react@^16.8.0 || ^17.0.0 || ^18.0.0", "@types/react@^16.8.6 || ^17.0.0", "@types/react@^16.9.0 || ^17.0.0 || ^18.0.0", "@types/react@^17.0.0", "@types/react@^17.0.0 || ^18.0.0", "@types/react@17.0.40":
  version "17.0.40"
  resolved "https://registry.npmjs.org/@types/react/-/react-17.0.40.tgz"
  integrity sha512-UrXhD/JyLH+W70nNSufXqMZNuUD2cXHu6UjCllC6pmOQgBX4SGXOH8fjRka0O0Ee0HrFxapDD8Bwn81Kmiz6jQ==
  dependencies:
    "@types/prop-types" "*"
    "@types/scheduler" "*"
    csstype "^3.0.2"

"@types/scheduler@*":
  version "0.16.5"
  resolved "https://registry.npmjs.org/@types/scheduler/-/scheduler-0.16.5.tgz"
  integrity sha512-s/FPdYRmZR8SjLWGMCuax7r3qCWQw9QKHzXVukAuuIJkXkDRwp+Pu5LMIVFi0Fxbav35WURicYr8u1QsoybnQw==

"@typescript-eslint/parser@^5.21.0":
  version "5.62.0"
  resolved "https://registry.npmjs.org/@typescript-eslint/parser/-/parser-5.62.0.tgz"
  integrity sha512-VlJEV0fOQ7BExOsHYAGrgbEiZoi8D+Bl2+f6V2RrXerRSylnp+ZBHmPvaIa8cz0Ajx7WO7Z5RqfgYg7ED1nRhA==
  dependencies:
    "@typescript-eslint/scope-manager" "5.62.0"
    "@typescript-eslint/types" "5.62.0"
    "@typescript-eslint/typescript-estree" "5.62.0"
    debug "^4.3.4"

"@typescript-eslint/scope-manager@5.62.0":
  version "5.62.0"
  resolved "https://registry.npmjs.org/@typescript-eslint/scope-manager/-/scope-manager-5.62.0.tgz"
  integrity sha512-VXuvVvZeQCQb5Zgf4HAxc04q5j+WrNAtNh9OwCsCgpKqESMTu3tF/jhZ3xG6T4NZwWl65Bg8KuS2uEvhSfLl0w==
  dependencies:
    "@typescript-eslint/types" "5.62.0"
    "@typescript-eslint/visitor-keys" "5.62.0"

"@typescript-eslint/types@5.62.0":
  version "5.62.0"
  resolved "https://registry.npmjs.org/@typescript-eslint/types/-/types-5.62.0.tgz"
  integrity sha512-87NVngcbVXUahrRTqIK27gD2t5Cu1yuCXxbLcFtCzZGlfyVWWh8mLHkoxzjsB6DDNnvdL+fW8MiwPEJyGJQDgQ==

"@typescript-eslint/typescript-estree@5.62.0":
  version "5.62.0"
  resolved "https://registry.npmjs.org/@typescript-eslint/typescript-estree/-/typescript-estree-5.62.0.tgz"
  integrity sha512-CmcQ6uY7b9y694lKdRB8FEel7JbU/40iSAPomu++SjLMntB+2Leay2LO6i8VnJk58MtE9/nQSFIH6jpyRWyYzA==
  dependencies:
    "@typescript-eslint/types" "5.62.0"
    "@typescript-eslint/visitor-keys" "5.62.0"
    debug "^4.3.4"
    globby "^11.1.0"
    is-glob "^4.0.3"
    semver "^7.3.7"
    tsutils "^3.21.0"

"@typescript-eslint/visitor-keys@5.62.0":
  version "5.62.0"
  resolved "https://registry.npmjs.org/@typescript-eslint/visitor-keys/-/visitor-keys-5.62.0.tgz"
  integrity sha512-07ny+LHRzQXepkGg6w0mFY41fVUNBrL2Roj/++7V1txKugfjm/Ci/qSND03r2RhlJhJYMcTn9AhhSSqQp0Ysyw==
  dependencies:
    "@typescript-eslint/types" "5.62.0"
    eslint-visitor-keys "^3.3.0"

"@webassemblyjs/ast@^1.12.1", "@webassemblyjs/ast@1.14.1":
  version "1.14.1"
  resolved "https://registry.npmjs.org/@webassemblyjs/ast/-/ast-1.14.1.tgz"
  integrity sha512-nuBEDgQfm1ccRp/8bCQrx1frohyufl4JlbMMZ4P1wpeOfDhF6FQkxZJ1b/e+PLwr6X1Nhw6OLme5usuBWYBvuQ==
  dependencies:
    "@webassemblyjs/helper-numbers" "1.13.2"
    "@webassemblyjs/helper-wasm-bytecode" "1.13.2"

"@webassemblyjs/floating-point-hex-parser@1.13.2":
  version "1.13.2"
  resolved "https://registry.npmjs.org/@webassemblyjs/floating-point-hex-parser/-/floating-point-hex-parser-1.13.2.tgz"
  integrity sha512-6oXyTOzbKxGH4steLbLNOu71Oj+C8Lg34n6CqRvqfS2O71BxY6ByfMDRhBytzknj9yGUPVJ1qIKhRlAwO1AovA==

"@webassemblyjs/helper-api-error@1.13.2":
  version "1.13.2"
  resolved "https://registry.npmjs.org/@webassemblyjs/helper-api-error/-/helper-api-error-1.13.2.tgz"
  integrity sha512-U56GMYxy4ZQCbDZd6JuvvNV/WFildOjsaWD3Tzzvmw/mas3cXzRJPMjP83JqEsgSbyrmaGjBfDtV7KDXV9UzFQ==

"@webassemblyjs/helper-buffer@1.14.1":
  version "1.14.1"
  resolved "https://registry.npmjs.org/@webassemblyjs/helper-buffer/-/helper-buffer-1.14.1.tgz"
  integrity sha512-jyH7wtcHiKssDtFPRB+iQdxlDf96m0E39yb0k5uJVhFGleZFoNw1c4aeIcVUPPbXUVJ94wwnMOAqUHyzoEPVMA==

"@webassemblyjs/helper-numbers@1.13.2":
  version "1.13.2"
  resolved "https://registry.npmjs.org/@webassemblyjs/helper-numbers/-/helper-numbers-1.13.2.tgz"
  integrity sha512-FE8aCmS5Q6eQYcV3gI35O4J789wlQA+7JrqTTpJqn5emA4U2hvwJmvFRC0HODS+3Ye6WioDklgd6scJ3+PLnEA==
  dependencies:
    "@webassemblyjs/floating-point-hex-parser" "1.13.2"
    "@webassemblyjs/helper-api-error" "1.13.2"
    "@xtuc/long" "4.2.2"

"@webassemblyjs/helper-wasm-bytecode@1.13.2":
  version "1.13.2"
  resolved "https://registry.npmjs.org/@webassemblyjs/helper-wasm-bytecode/-/helper-wasm-bytecode-1.13.2.tgz"
  integrity sha512-3QbLKy93F0EAIXLh0ogEVR6rOubA9AoZ+WRYhNbFyuB70j3dRdwH9g+qXhLAO0kiYGlg3TxDV+I4rQTr/YNXkA==

"@webassemblyjs/helper-wasm-section@1.14.1":
  version "1.14.1"
  resolved "https://registry.npmjs.org/@webassemblyjs/helper-wasm-section/-/helper-wasm-section-1.14.1.tgz"
  integrity sha512-ds5mXEqTJ6oxRoqjhWDU83OgzAYjwsCV8Lo/N+oRsNDmx/ZDpqalmrtgOMkHwxsG0iI//3BwWAErYRHtgn0dZw==
  dependencies:
    "@webassemblyjs/ast" "1.14.1"
    "@webassemblyjs/helper-buffer" "1.14.1"
    "@webassemblyjs/helper-wasm-bytecode" "1.13.2"
    "@webassemblyjs/wasm-gen" "1.14.1"

"@webassemblyjs/ieee754@1.13.2":
  version "1.13.2"
  resolved "https://registry.npmjs.org/@webassemblyjs/ieee754/-/ieee754-1.13.2.tgz"
  integrity sha512-4LtOzh58S/5lX4ITKxnAK2USuNEvpdVV9AlgGQb8rJDHaLeHciwG4zlGr0j/SNWlr7x3vO1lDEsuePvtcDNCkw==
  dependencies:
    "@xtuc/ieee754" "^1.2.0"

"@webassemblyjs/leb128@1.13.2":
  version "1.13.2"
  resolved "https://registry.npmjs.org/@webassemblyjs/leb128/-/leb128-1.13.2.tgz"
  integrity sha512-Lde1oNoIdzVzdkNEAWZ1dZ5orIbff80YPdHx20mrHwHrVNNTjNr8E3xz9BdpcGqRQbAEa+fkrCb+fRFTl/6sQw==
  dependencies:
    "@xtuc/long" "4.2.2"

"@webassemblyjs/utf8@1.13.2":
  version "1.13.2"
  resolved "https://registry.npmjs.org/@webassemblyjs/utf8/-/utf8-1.13.2.tgz"
  integrity sha512-3NQWGjKTASY1xV5m7Hr0iPeXD9+RDobLll3T9d2AO+g3my8xy5peVyjSag4I50mR1bBSN/Ct12lo+R9tJk0NZQ==

"@webassemblyjs/wasm-edit@^1.12.1":
  version "1.14.1"
  resolved "https://registry.npmjs.org/@webassemblyjs/wasm-edit/-/wasm-edit-1.14.1.tgz"
  integrity sha512-RNJUIQH/J8iA/1NzlE4N7KtyZNHi3w7at7hDjvRNm5rcUXa00z1vRz3glZoULfJ5mpvYhLybmVcwcjGrC1pRrQ==
  dependencies:
    "@webassemblyjs/ast" "1.14.1"
    "@webassemblyjs/helper-buffer" "1.14.1"
    "@webassemblyjs/helper-wasm-bytecode" "1.13.2"
    "@webassemblyjs/helper-wasm-section" "1.14.1"
    "@webassemblyjs/wasm-gen" "1.14.1"
    "@webassemblyjs/wasm-opt" "1.14.1"
    "@webassemblyjs/wasm-parser" "1.14.1"
    "@webassemblyjs/wast-printer" "1.14.1"

"@webassemblyjs/wasm-gen@1.14.1":
  version "1.14.1"
  resolved "https://registry.npmjs.org/@webassemblyjs/wasm-gen/-/wasm-gen-1.14.1.tgz"
  integrity sha512-AmomSIjP8ZbfGQhumkNvgC33AY7qtMCXnN6bL2u2Js4gVCg8fp735aEiMSBbDR7UQIj90n4wKAFUSEd0QN2Ukg==
  dependencies:
    "@webassemblyjs/ast" "1.14.1"
    "@webassemblyjs/helper-wasm-bytecode" "1.13.2"
    "@webassemblyjs/ieee754" "1.13.2"
    "@webassemblyjs/leb128" "1.13.2"
    "@webassemblyjs/utf8" "1.13.2"

"@webassemblyjs/wasm-opt@1.14.1":
  version "1.14.1"
  resolved "https://registry.npmjs.org/@webassemblyjs/wasm-opt/-/wasm-opt-1.14.1.tgz"
  integrity sha512-PTcKLUNvBqnY2U6E5bdOQcSM+oVP/PmrDY9NzowJjislEjwP/C4an2303MCVS2Mg9d3AJpIGdUFIQQWbPds0Sw==
  dependencies:
    "@webassemblyjs/ast" "1.14.1"
    "@webassemblyjs/helper-buffer" "1.14.1"
    "@webassemblyjs/wasm-gen" "1.14.1"
    "@webassemblyjs/wasm-parser" "1.14.1"

"@webassemblyjs/wasm-parser@^1.12.1", "@webassemblyjs/wasm-parser@1.14.1":
  version "1.14.1"
  resolved "https://registry.npmjs.org/@webassemblyjs/wasm-parser/-/wasm-parser-1.14.1.tgz"
  integrity sha512-JLBl+KZ0R5qB7mCnud/yyX08jWFw5MsoalJ1pQ4EdFlgj9VdXKGuENGsiCIjegI1W7p91rUlcB/LB5yRJKNTcQ==
  dependencies:
    "@webassemblyjs/ast" "1.14.1"
    "@webassemblyjs/helper-api-error" "1.13.2"
    "@webassemblyjs/helper-wasm-bytecode" "1.13.2"
    "@webassemblyjs/ieee754" "1.13.2"
    "@webassemblyjs/leb128" "1.13.2"
    "@webassemblyjs/utf8" "1.13.2"

"@webassemblyjs/wast-printer@1.14.1":
  version "1.14.1"
  resolved "https://registry.npmjs.org/@webassemblyjs/wast-printer/-/wast-printer-1.14.1.tgz"
  integrity sha512-kPSSXE6De1XOR820C90RIo2ogvZG+c3KiHzqUoO/F34Y2shGzesfqv7o57xrxovZJH/MetF5UjroJ/R/3isoiw==
  dependencies:
    "@webassemblyjs/ast" "1.14.1"
    "@xtuc/long" "4.2.2"

"@xtuc/ieee754@^1.2.0":
  version "1.2.0"
  resolved "https://registry.npmjs.org/@xtuc/ieee754/-/ieee754-1.2.0.tgz"
  integrity sha512-DX8nKgqcGwsc0eJSqYt5lwP4DH5FlHnmuWWBRy7X0NcaGR0ZtuyeESgMwTYVEtxmsNGY+qit4QYT/MIYTOTPeA==

"@xtuc/long@4.2.2":
  version "4.2.2"
  resolved "https://registry.npmjs.org/@xtuc/long/-/long-4.2.2.tgz"
  integrity sha512-NuHqBY1PB/D8xU6s/thBgOAiAP7HOYDQ32+BFZILJ8ivkUkAHQnWfn6WhL79Owj1qmUnoN/YPhktdIoucipkAQ==

"@zxing/browser@0.0.7":
  version "0.0.7"
  resolved "https://registry.npmjs.org/@zxing/browser/-/browser-0.0.7.tgz"
  integrity sha512-AepzMgDnD6EjxewqmXpHJsi4S3Gw9ilZJLIbTf6fWuWySEcHBodnGu3p7FWlgq1Sd5QyfPhTum5z3CBkkhMVng==
  optionalDependencies:
    "@zxing/text-encoding" "^0.9.0"

"@zxing/library@^0.18.3":
  version "0.18.6"
  resolved "https://registry.npmjs.org/@zxing/library/-/library-0.18.6.tgz"
  integrity sha512-bulZ9JHoLFd9W36pi+7e7DnEYNJhljYjZ1UTsKPOoLMU3qtC+REHITeCRNx40zTRJZx18W5TBRXt5pq2Uopjsw==
  dependencies:
    ts-custom-error "^3.0.0"
  optionalDependencies:
    "@zxing/text-encoding" "~0.9.0"

"@zxing/text-encoding@^0.9.0", "@zxing/text-encoding@~0.9.0":
  version "0.9.0"
  resolved "https://registry.npmjs.org/@zxing/text-encoding/-/text-encoding-0.9.0.tgz"
  integrity sha512-U/4aVJ2mxI0aDNI8Uq0wEhMgY+u4CNtEb0om3+y3+niDAsoTCOB33UF0sxpzqzdqXLqmvc+vZyAt4O8pPdfkwA==

abbrev@1:
  version "1.1.1"
  resolved "https://registry.npmjs.org/abbrev/-/abbrev-1.1.1.tgz"
  integrity sha512-nne9/IiQ/hzIhY6pdDnbBtz7DjPTKrY00P/zvPSm5pOFkl6xuGrGnXn/VtTNNfNtAfZ9/1RtehkszU9qcTii0Q==

acorn-jsx@^5.3.2:
  version "5.3.2"
  resolved "https://registry.npmjs.org/acorn-jsx/-/acorn-jsx-5.3.2.tgz"
  integrity sha512-rq9s+JNhf0IChjtDXxllJ7g41oZk5SlXtp0LHwyA5cejwn7vKmKp4pPri6YEePv2PU65sAsegbXtIinmDFDXgQ==

"acorn@^6.0.0 || ^7.0.0 || ^8.0.0", acorn@^8.14.0, acorn@^8.8.2, acorn@^8.9.0:
  version "8.14.0"
  resolved "https://registry.npmjs.org/acorn/-/acorn-8.14.0.tgz"
  integrity sha512-cl669nCJTZBsL97OF4kUQm5g5hC2uihk0NxY3WENAC0TYdILVkAyHymAntgxGkl7K+t0cXIrH5siy5S4XkFycA==

add-px-to-style@1.0.0:
  version "1.0.0"
  resolved "https://registry.npmjs.org/add-px-to-style/-/add-px-to-style-1.0.0.tgz"
  integrity sha512-YMyxSlXpPjD8uWekCQGuN40lV4bnZagUwqa2m/uFv1z/tNImSk9fnXVMUI5qwME/zzI3MMQRvjZ+69zyfSSyew==

agent-base@6:
  version "6.0.2"
  resolved "https://registry.npmjs.org/agent-base/-/agent-base-6.0.2.tgz"
  integrity sha512-RZNwNclF7+MS/8bDg70amg32dyeZGZxiDuQmZxKLAlQjr3jGyLx+4Kkk58UO7D2QdgFIQCovuSuZESne6RG6XQ==
  dependencies:
    debug "4"

ajv-keywords@^3.5.2:
  version "3.5.2"
  resolved "https://registry.npmjs.org/ajv-keywords/-/ajv-keywords-3.5.2.tgz"
  integrity sha512-5p6WTN0DdTGVQk6VjcEju19IgaHudalcfabD7yhDGeA6bcQnmL+CpveLJq/3hvfwd1aof6L386Ougkx6RfyMIQ==

ajv@^6.10.0, ajv@^6.12.4, ajv@^6.12.5, ajv@^6.9.1:
  version "6.12.6"
  resolved "https://registry.npmjs.org/ajv/-/ajv-6.12.6.tgz"
  integrity sha512-j3fVLgvTo527anyYyJOGTYJbG+vnnQYvE0m5mmkc1TK+nxAppkCLMIL0aZ4dblVCNoGShhm+kzE4ZUykBoMg4g==
  dependencies:
    fast-deep-equal "^3.1.1"
    fast-json-stable-stringify "^2.0.0"
    json-schema-traverse "^0.4.1"
    uri-js "^4.2.2"

ansi-regex@^5.0.1:
  version "5.0.1"
  resolved "https://registry.npmjs.org/ansi-regex/-/ansi-regex-5.0.1.tgz"
  integrity sha512-quJQXlTSUGL2LH9SUXo8VwsY4soanhgo6LNSm84E1LBcE8s3O0wpdiRzyR9z/ZZJMlMWv37qOOb9pdJlMUEKFQ==

ansi-regex@^6.0.1:
  version "6.1.0"
  resolved "https://registry.npmjs.org/ansi-regex/-/ansi-regex-6.1.0.tgz"
  integrity sha512-7HSX4QQb4CspciLpVFwyRe79O3xsIZDDLER21kERQ71oaPodF8jL725AgJMFAYbooIqolJoRLuM81SpeUkpkvA==

ansi-styles@^3.2.1:
  version "3.2.1"
  resolved "https://registry.npmjs.org/ansi-styles/-/ansi-styles-3.2.1.tgz"
  integrity sha512-VT0ZI6kZRdTh8YyJw3SMbYm/u+NqfsAxEpWO0Pf9sq8/e94WxxOpPKx9FR1FlyCtOVDNOQ+8ntlqFxiRc+r5qA==
  dependencies:
    color-convert "^1.9.0"

ansi-styles@^4.0.0:
  version "4.3.0"
  resolved "https://registry.npmjs.org/ansi-styles/-/ansi-styles-4.3.0.tgz"
  integrity sha512-zbB9rCJAT1rbjiVDb2hqKFHNYLxgtk8NURxZ3IZwD3F6NtxbXZQCnnSi1Lkx+IDohdPlFp222wVALIheZJQSEg==
  dependencies:
    color-convert "^2.0.1"

ansi-styles@^4.1.0:
  version "4.3.0"
  resolved "https://registry.npmjs.org/ansi-styles/-/ansi-styles-4.3.0.tgz"
  integrity sha512-zbB9rCJAT1rbjiVDb2hqKFHNYLxgtk8NURxZ3IZwD3F6NtxbXZQCnnSi1Lkx+IDohdPlFp222wVALIheZJQSEg==
  dependencies:
    color-convert "^2.0.1"

ansi-styles@^6.1.0:
  version "6.2.1"
  resolved "https://registry.npmjs.org/ansi-styles/-/ansi-styles-6.2.1.tgz"
  integrity sha512-bN798gFfQX+viw3R7yrGWRqnrN2oRkEkUjjl4JNn4E8GxxbjtG3FbrEIIY3l8/hrwUwIeCZvi4QuOTP4MErVug==

any-promise@^1.0.0:
  version "1.3.0"
  resolved "https://registry.npmjs.org/any-promise/-/any-promise-1.3.0.tgz"
  integrity sha512-7UvmKalWRt1wgjL1RrGxoSJW/0QZFIegpeGvZG9kjp8vrRu55XTHbwnqq2GpXm9uLbcuhxm3IqX9OB4MZR1b2A==

anymatch@~3.1.2:
  version "3.1.3"
  resolved "https://registry.npmjs.org/anymatch/-/anymatch-3.1.3.tgz"
  integrity sha512-KMReFUr0B4t+D+OBkjR3KYqvocp2XaSzO55UcB6mgQMd3KbcE+mWTyvVV7D/zsdEbNnV6acZUutkiHQXvTr1Rw==
  dependencies:
    normalize-path "^3.0.0"
    picomatch "^2.0.4"

apexcharts@^3.18.0, apexcharts@3.35.3:
  version "3.35.3"
  resolved "https://registry.npmjs.org/apexcharts/-/apexcharts-3.35.3.tgz"
  integrity sha512-UDlxslJr3DG63I/SgoiivIu4lpP25GMaKFK8NvCHmTksTQshx4ng3oPPrYvdsBFOvD/ajPYIh/p7rNB0jq8vXg==
  dependencies:
    svg.draggable.js "^2.2.2"
    svg.easing.js "^2.0.0"
    svg.filter.js "^2.0.2"
    svg.pathmorphing.js "^0.1.3"
    svg.resize.js "^1.4.3"
    svg.select.js "^3.0.1"

"aproba@^1.0.3 || ^2.0.0":
  version "2.0.0"
  resolved "https://registry.npmjs.org/aproba/-/aproba-2.0.0.tgz"
  integrity sha512-lYe4Gx7QT+MKGbDsA+Z+he/Wtef0BiwDOlK/XkBrdfsh9J/jPPXbX0tE9x9cl27Tmu5gg3QUbUrQYa/y+KOHPQ==

are-we-there-yet@^2.0.0:
  version "2.0.0"
  resolved "https://registry.npmjs.org/are-we-there-yet/-/are-we-there-yet-2.0.0.tgz"
  integrity sha512-Ci/qENmwHnsYo9xKIcUJN5LeDKdJ6R1Z1j9V/J5wyq8nh/mYPEpIKJbBZXtZjG04HiK7zV/p6Vs9952MrMeUIw==
  dependencies:
    delegates "^1.0.0"
    readable-stream "^3.6.0"

arg@^5.0.2:
  version "5.0.2"
  resolved "https://registry.npmjs.org/arg/-/arg-5.0.2.tgz"
  integrity sha512-PYjyFOLKQ9y57JvQ6QLo8dAgNqswh8M1RMJYdQduT6xbWSgK36P/Z/v+p888pM69jMMfS8Xd8F6I1kQ/I9HUGg==

argparse@^2.0.1:
  version "2.0.1"
  resolved "https://registry.npmjs.org/argparse/-/argparse-2.0.1.tgz"
  integrity sha512-8+9WqebbFzpX9OR+Wa6O29asIogeRMzcGtAINdpMHHyAg10f05aSFVBbcEqGf/PXw1EjAZ+q2/bEBg3DvurK3Q==

aria-query@^5.1.3:
  version "5.3.0"
  resolved "https://registry.npmjs.org/aria-query/-/aria-query-5.3.0.tgz"
  integrity sha512-b0P0sZPKtyu8HkeRAfCq0IfURZK+SuwMjY1UXGBU27wpAiTwQAIlq56IbIO+ytk/JjS1fMR14ee5WBBfKi5J6A==
  dependencies:
    dequal "^2.0.3"

array-buffer-byte-length@^1.0.0:
  version "1.0.0"
  resolved "https://registry.npmjs.org/array-buffer-byte-length/-/array-buffer-byte-length-1.0.0.tgz"
  integrity sha512-LPuwb2P+NrQw3XhxGc36+XSvuBPopovXYTR9Ew++Du9Yb/bx5AzBfrIsBoj0EZUifjQU+sHL21sseZ3jerWO/A==
  dependencies:
    call-bind "^1.0.2"
    is-array-buffer "^3.0.1"

array-includes@^3.1.6, array-includes@^3.1.7:
  version "3.1.7"
  resolved "https://registry.npmjs.org/array-includes/-/array-includes-3.1.7.tgz"
  integrity sha512-dlcsNBIiWhPkHdOEEKnehA+RNUWDc4UqFtnIXU4uuYDPtA4LDkr7qip2p0VvFAEXNDr0yWZ9PJyIRiGjRLQzwQ==
  dependencies:
    call-bind "^1.0.2"
    define-properties "^1.2.0"
    es-abstract "^1.22.1"
    get-intrinsic "^1.2.1"
    is-string "^1.0.7"

array-union@^2.1.0:
  version "2.1.0"
  resolved "https://registry.npmjs.org/array-union/-/array-union-2.1.0.tgz"
  integrity sha512-HGyxoOTYUyCM6stUe6EJgnd4EoewAI7zMdfqO+kGjnlZmBDz/cR5pf8r/cR4Wq60sL/p0IkcjUEEPwS3GFrIyw==

array.prototype.findlastindex@^1.2.3:
  version "1.2.3"
  resolved "https://registry.npmjs.org/array.prototype.findlastindex/-/array.prototype.findlastindex-1.2.3.tgz"
  integrity sha512-LzLoiOMAxvy+Gd3BAq3B7VeIgPdo+Q8hthvKtXybMvRV0jrXfJM/t8mw7nNlpEcVlVUnCnM2KSX4XU5HmpodOA==
  dependencies:
    call-bind "^1.0.2"
    define-properties "^1.2.0"
    es-abstract "^1.22.1"
    es-shim-unscopables "^1.0.0"
    get-intrinsic "^1.2.1"

array.prototype.flat@^1.3.1, array.prototype.flat@^1.3.2:
  version "1.3.2"
  resolved "https://registry.npmjs.org/array.prototype.flat/-/array.prototype.flat-1.3.2.tgz"
  integrity sha512-djYB+Zx2vLewY8RWlNCUdHjDXs2XOgm602S9E7P/UpHgfeHL00cRiIF+IN/G/aUJ7kGPb6yO/ErDI5V2s8iycA==
  dependencies:
    call-bind "^1.0.2"
    define-properties "^1.2.0"
    es-abstract "^1.22.1"
    es-shim-unscopables "^1.0.0"

array.prototype.flatmap@^1.3.1, array.prototype.flatmap@^1.3.2:
  version "1.3.2"
  resolved "https://registry.npmjs.org/array.prototype.flatmap/-/array.prototype.flatmap-1.3.2.tgz"
  integrity sha512-Ewyx0c9PmpcsByhSW4r+9zDU7sGjFc86qf/kKtuSCRdhfbk0SNLLkaT5qvcHnRGgc5NP/ly/y+qkXkqONX54CQ==
  dependencies:
    call-bind "^1.0.2"
    define-properties "^1.2.0"
    es-abstract "^1.22.1"
    es-shim-unscopables "^1.0.0"

array.prototype.tosorted@^1.1.1:
  version "1.1.2"
  resolved "https://registry.npmjs.org/array.prototype.tosorted/-/array.prototype.tosorted-1.1.2.tgz"
  integrity sha512-HuQCHOlk1Weat5jzStICBCd83NxiIMwqDg/dHEsoefabn/hJRj5pVdWcPUSpRrwhwxZOsQassMpgN/xRYFBMIg==
  dependencies:
    call-bind "^1.0.2"
    define-properties "^1.2.0"
    es-abstract "^1.22.1"
    es-shim-unscopables "^1.0.0"
    get-intrinsic "^1.2.1"

arraybuffer.prototype.slice@^1.0.2:
  version "1.0.2"
  resolved "https://registry.npmjs.org/arraybuffer.prototype.slice/-/arraybuffer.prototype.slice-1.0.2.tgz"
  integrity sha512-yMBKppFur/fbHu9/6USUe03bZ4knMYiwFBcyiaXB8Go0qNehwX6inYPzK9U0NeQvGxKthcmHcaR8P5MStSRBAw==
  dependencies:
    array-buffer-byte-length "^1.0.0"
    call-bind "^1.0.2"
    define-properties "^1.2.0"
    es-abstract "^1.22.1"
    get-intrinsic "^1.2.1"
    is-array-buffer "^3.0.2"
    is-shared-array-buffer "^1.0.2"

ast-types-flow@^0.0.7:
  version "0.0.7"
  resolved "https://registry.npmjs.org/ast-types-flow/-/ast-types-flow-0.0.7.tgz"
  integrity sha512-eBvWn1lvIApYMhzQMsu9ciLfkBY499mFZlNqG+/9WR7PVlroQw0vG30cOQQbaKz3sCEc44TAOu2ykzqXSNnwag==

asynciterator.prototype@^1.0.0:
  version "1.0.0"
  resolved "https://registry.npmjs.org/asynciterator.prototype/-/asynciterator.prototype-1.0.0.tgz"
  integrity sha512-wwHYEIS0Q80f5mosx3L/dfG5t5rjEa9Ft51GTaNt862EnpyGHpgz2RkZvLPp1oF5TnAiTohkEKVEu8pQPJI7Vg==
  dependencies:
    has-symbols "^1.0.3"

asynckit@^0.4.0:
  version "0.4.0"
  resolved "https://registry.npmjs.org/asynckit/-/asynckit-0.4.0.tgz"
  integrity sha512-Oei9OH4tRh0YqU3GxhX79dM/mwVgvbZJaSNaRk+bshkj0S5cfHcgYakreBjrHwatXKbz+IoIdYLxrKim2MjW0Q==

atob@^2.1.2:
  version "2.1.2"
  resolved "https://registry.npmjs.org/atob/-/atob-2.1.2.tgz"
  integrity sha512-Wm6ukoaOGJi/73p/cl2GvLjTI5JM1k/O14isD73YML8StrH/7/lRFgmg8nICZgD3bZZvjwCGxtMOD3wWNAu8cg==

autoprefixer@^10.4.15:
  version "10.4.16"
  resolved "https://registry.npmjs.org/autoprefixer/-/autoprefixer-10.4.16.tgz"
  integrity sha512-7vd3UC6xKp0HLfua5IjZlcXvGAGy7cBAXTg2lyQ/8WpNhd6SiZ8Be+xm3FyBSYJx5GKcpRCzBh7RH4/0dnY+uQ==
  dependencies:
    browserslist "^4.21.10"
    caniuse-lite "^1.0.30001538"
    fraction.js "^4.3.6"
    normalize-range "^0.1.2"
    picocolors "^1.0.0"
    postcss-value-parser "^4.2.0"

available-typed-arrays@^1.0.5:
  version "1.0.5"
  resolved "https://registry.npmjs.org/available-typed-arrays/-/available-typed-arrays-1.0.5.tgz"
  integrity sha512-DMD0KiN46eipeziST1LPP/STfDU0sufISXmjSgvVsoU2tqxctQeASejWcfNtxYKqETM1UxQ8sp2OrSBWpHY6sw==

axe-core@^4.6.2:
  version "4.8.2"
  resolved "https://registry.npmjs.org/axe-core/-/axe-core-4.8.2.tgz"
  integrity sha512-/dlp0fxyM3R8YW7MFzaHWXrf4zzbr0vaYb23VBFCl83R7nWNPg/yaQw2Dc8jzCMmDVLhSdzH8MjrsuIUuvX+6g==

axios@^1.7.8:
  version "1.7.8"
  resolved "https://registry.npmjs.org/axios/-/axios-1.7.8.tgz"
  integrity sha512-Uu0wb7KNqK2t5K+YQyVCLM76prD5sRFjKHbJYCP1J7JFGEQ6nN7HWn9+04LAeiJ3ji54lgS/gZCH1oxyrf1SPw==
  dependencies:
    follow-redirects "^1.15.6"
    form-data "^4.0.0"
    proxy-from-env "^1.1.0"

axobject-query@^3.1.1:
  version "3.2.1"
  resolved "https://registry.npmjs.org/axobject-query/-/axobject-query-3.2.1.tgz"
  integrity sha512-jsyHu61e6N4Vbz/v18DHwWYKK0bSWLqn47eeDSKPB7m8tqMHF9YJ+mhIk2lVteyZrY8tnSj/jHOv4YiTCuCJgg==
  dependencies:
    dequal "^2.0.3"

babel-plugin-macros@^3.1.0:
  version "3.1.0"
  resolved "https://registry.npmjs.org/babel-plugin-macros/-/babel-plugin-macros-3.1.0.tgz"
  integrity sha512-Cg7TFGpIr01vOQNODXOOaGz2NpCU5gl8x1qJFbb6hbZxR7XrcE2vtbAsTAbJ7/xwJtUuJEw8K8Zr/AE0LHlesg==
  dependencies:
    "@babel/runtime" "^7.12.5"
    cosmiconfig "^7.0.0"
    resolve "^1.19.0"

balanced-match@^1.0.0:
  version "1.0.2"
  resolved "https://registry.npmjs.org/balanced-match/-/balanced-match-1.0.2.tgz"
  integrity sha512-3oSeUO0TMV67hN1AmbXsK4yaqU7tjiHlbxRDZOpH0KW9+CeX4bRAaX0Anxt0tx2MrpRpWwQaPwIlISEJhYU5Pw==

base64-arraybuffer@^1.0.2:
  version "1.0.2"
  resolved "https://registry.npmjs.org/base64-arraybuffer/-/base64-arraybuffer-1.0.2.tgz"
  integrity sha512-I3yl4r9QB5ZRY3XuJVEPfc2XhZO6YweFPI+UovAzn+8/hb3oJ6lnysaFcjVpkCPfVWFUDvoZ8kmVDP7WyRtYtQ==

bcrypt@^5.1.1:
  version "5.1.1"
  resolved "https://registry.npmjs.org/bcrypt/-/bcrypt-5.1.1.tgz"
  integrity sha512-AGBHOG5hPYZ5Xl9KXzU5iKq9516yEmvCKDg3ecP5kX2aB6UqTeXZxk2ELnDgDm6BQSMlLt9rDB4LoSMx0rYwww==
  dependencies:
    "@mapbox/node-pre-gyp" "^1.0.11"
    node-addon-api "^5.0.0"

big.js@^5.2.2:
  version "5.2.2"
  resolved "https://registry.npmjs.org/big.js/-/big.js-5.2.2.tgz"
  integrity sha512-vyL2OymJxmarO8gxMr0mhChsO9QGwhynfuu4+MHTAW6czfq9humCB7rKpUjDd9YUiDPU4mzpyupFSvOClAwbmQ==

binary-extensions@^2.0.0:
  version "2.3.0"
  resolved "https://registry.npmjs.org/binary-extensions/-/binary-extensions-2.3.0.tgz"
  integrity sha512-Ceh+7ox5qe7LJuLHoY0feh3pHuUDHAcRUeyL2VYghZwfpkNIy/+8Ocg0a3UuSoYzavmylwuLWQOf3hl0jjMMIw==

brace-expansion@^1.1.7:
  version "1.1.11"
  resolved "https://registry.npmjs.org/brace-expansion/-/brace-expansion-1.1.11.tgz"
  integrity sha512-iCuPHDFgrHX7H2vEI/5xpz07zSHB00TpugqhmYtVmMO6518mCuRMoOYFldEBl0g187ufozdaHgWKcYFb61qGiA==
  dependencies:
    balanced-match "^1.0.0"
    concat-map "0.0.1"

brace-expansion@^2.0.1:
  version "2.0.1"
  resolved "https://registry.npmjs.org/brace-expansion/-/brace-expansion-2.0.1.tgz"
  integrity sha512-XnAIvQ8eM+kC6aULx6wuQiwVsnzsi9d3WxzV3FpWTGA19F621kwdbsAcFKXgKUHZWsy+mY6iL1sHTxWEFCytDA==
  dependencies:
    balanced-match "^1.0.0"

braces@^3.0.3, braces@~3.0.2:
  version "3.0.3"
  resolved "https://registry.npmjs.org/braces/-/braces-3.0.3.tgz"
  integrity sha512-yQbXgO/OSZVD2IsiLlro+7Hf6Q18EJrKSEsdoMzKePKXct3gvD8oLcOQdIzGupr5Fj+EDe8gO/lxc1BzfMpxvA==
  dependencies:
    fill-range "^7.1.1"

browserslist@^4.21.10, browserslist@^4.24.0, "browserslist@>= 4.21.0":
  version "4.24.2"
  resolved "https://registry.npmjs.org/browserslist/-/browserslist-4.24.2.tgz"
  integrity sha512-ZIc+Q62revdMcqC6aChtW4jz3My3klmCO1fEmINZY/8J3EpBg5/A/D0AKmBveUh6pgoeycoMkVMko84tuYS+Gg==
  dependencies:
    caniuse-lite "^1.0.30001669"
    electron-to-chromium "^1.5.41"
    node-releases "^2.0.18"
    update-browserslist-db "^1.1.1"

btoa@^1.2.1:
  version "1.2.1"
  resolved "https://registry.npmjs.org/btoa/-/btoa-1.2.1.tgz"
  integrity sha512-SB4/MIGlsiVkMcHmT+pSmIPoNDoHg+7cMzmt3Uxt628MTz2487DKSqK/fuhFBrkuqrYv5UCEnACpF4dTFNKc/g==

buffer-equal-constant-time@1.0.1:
  version "1.0.1"
  resolved "https://registry.npmjs.org/buffer-equal-constant-time/-/buffer-equal-constant-time-1.0.1.tgz"
  integrity sha512-zRpUiDwd/xk6ADqPMATG8vc9VPrkck7T07OIx0gnjmJAnHnTVXNQG3vfvWNuiZIkwu9KrKdA1iJKfsfTVxE6NA==

buffer-from@^1.0.0:
  version "1.1.2"
  resolved "https://registry.npmjs.org/buffer-from/-/buffer-from-1.1.2.tgz"
  integrity sha512-E+XQCRwSbaaiChtv6k6Dwgc+bx+Bs6vuKJHHl5kox/BaKbhiXzqQOwK4cO22yElGp2OCmjwVhT3HmxgyPGnJfQ==

buffer-from@~0.1.1:
  version "0.1.2"
  resolved "https://registry.npmjs.org/buffer-from/-/buffer-from-0.1.2.tgz"
  integrity sha512-RiWIenusJsmI2KcvqQABB83tLxCByE3upSP8QU3rJDMVFGPWLvPQJt/O1Su9moRWeH7d+Q2HYb68f6+v+tw2vg==

call-bind@^1.0.0, call-bind@^1.0.2, call-bind@^1.0.4, call-bind@^1.0.5:
  version "1.0.5"
  resolved "https://registry.npmjs.org/call-bind/-/call-bind-1.0.5.tgz"
  integrity sha512-C3nQxfFZxFRVoJoGKKI8y3MOEo129NQ+FgQ08iye+Mk4zNZZGdjfs06bVTr+DBSlA66Q2VEcMki/cUCP4SercQ==
  dependencies:
    function-bind "^1.1.2"
    get-intrinsic "^1.2.1"
    set-function-length "^1.1.1"

callsites@^3.0.0:
  version "3.1.0"
  resolved "https://registry.npmjs.org/callsites/-/callsites-3.1.0.tgz"
  integrity sha512-P8BjAsXvZS+VIDUI11hHCQEv74YT67YUi5JJFNWIqL235sBmjX4+qx9Muvls5ivyNENctx46xQLQ3aTuE7ssaQ==

camelcase-css@^2.0.1:
  version "2.0.1"
  resolved "https://registry.npmjs.org/camelcase-css/-/camelcase-css-2.0.1.tgz"
  integrity sha512-QOSvevhslijgYwRx6Rv7zKdMF8lbRmx+uQGx2+vDc+KI/eBnsy9kit5aj23AgGu3pa4t9AgwbnXWqS+iOY+2aA==

caniuse-lite@^1.0.30001332, caniuse-lite@^1.0.30001538, caniuse-lite@^1.0.30001669:
  version "1.0.30001684"
  resolved "https://registry.npmjs.org/caniuse-lite/-/caniuse-lite-1.0.30001684.tgz"
  integrity sha512-G1LRwLIQjBQoyq0ZJGqGIJUXzJ8irpbjHLpVRXDvBEScFJ9b17sgK6vlx0GAJFE21okD7zXl08rRRUfq6HdoEQ==

canvg@^3.0.6:
  version "3.0.10"
  resolved "https://registry.npmjs.org/canvg/-/canvg-3.0.10.tgz"
  integrity sha512-qwR2FRNO9NlzTeKIPIKpnTY6fqwuYSequ8Ru8c0YkYU7U0oW+hLUvWadLvAu1Rl72OMNiFhoLu4f8eUjQ7l/+Q==
  dependencies:
    "@babel/runtime" "^7.12.5"
    "@types/raf" "^3.4.0"
    core-js "^3.8.3"
    raf "^3.4.1"
    regenerator-runtime "^0.13.7"
    rgbcolor "^1.0.1"
    stackblur-canvas "^2.0.0"
    svg-pathdata "^6.0.3"

chalk@^2.4.2:
  version "2.4.2"
  resolved "https://registry.npmjs.org/chalk/-/chalk-2.4.2.tgz"
  integrity sha512-Mti+f9lpJNcwF4tWV8/OrTTtF1gZi+f8FqlyAdouralcFWFQWF2+NgCHShjkCb+IFBLq9buZwE1xckQU4peSuQ==
  dependencies:
    ansi-styles "^3.2.1"
    escape-string-regexp "^1.0.5"
    supports-color "^5.3.0"

chalk@^4.0.0:
  version "4.1.2"
  resolved "https://registry.npmjs.org/chalk/-/chalk-4.1.2.tgz"
  integrity sha512-oKnbhFyRIXpUuez8iBMmyEa4nbj4IOQyuhc/wy9kY7/WVPcwIO9VA668Pu8RkO7+0G76SLROeyw9CpQ061i4mA==
  dependencies:
    ansi-styles "^4.1.0"
    supports-color "^7.1.0"

chokidar@^3.6.0:
  version "3.6.0"
  resolved "https://registry.npmjs.org/chokidar/-/chokidar-3.6.0.tgz"
  integrity sha512-7VT13fmjotKpGipCW9JEQAusEPE+Ei8nl6/g4FBAmIm0GOOLMua9NDDo/DWp0ZAxCr3cPq5ZpBqmPAQgDda2Pw==
  dependencies:
    anymatch "~3.1.2"
    braces "~3.0.2"
    glob-parent "~5.1.2"
    is-binary-path "~2.1.0"
    is-glob "~4.0.1"
    normalize-path "~3.0.0"
    readdirp "~3.6.0"
  optionalDependencies:
    fsevents "~2.3.2"

chownr@^2.0.0:
  version "2.0.0"
  resolved "https://registry.npmjs.org/chownr/-/chownr-2.0.0.tgz"
  integrity sha512-bIomtDF5KGpdogkLd9VspvFzk9KfpyyGlS8YFVZl7TGPBHL5snIOnxeshwVgPteQ9b4Eydl+pVbIyE1DcvCWgQ==

chrome-trace-event@^1.0.2:
  version "1.0.4"
  resolved "https://registry.npmjs.org/chrome-trace-event/-/chrome-trace-event-1.0.4.tgz"
  integrity sha512-rNjApaLzuwaOTjCiT8lSDdGN1APCiqkChLMJxJPWLunPAt5fy8xgU9/jNOchV84wfIxrA0lRQB7oCT8jrn/wrQ==

ckeditor5@39.0.2:
  version "39.0.2"
  resolved "https://registry.npmjs.org/ckeditor5/-/ckeditor5-39.0.2.tgz"
  integrity sha512-7wuhb5LYeAZ9PaEZlNNAOnfDLWozt3+tOdc1St7AYR4PO1UW/5crWHvs+6jejxB56Zk6LUaHUS6ECecdapihmA==
  dependencies:
    "@ckeditor/ckeditor5-clipboard" "39.0.2"
    "@ckeditor/ckeditor5-core" "39.0.2"
    "@ckeditor/ckeditor5-engine" "39.0.2"
    "@ckeditor/ckeditor5-enter" "39.0.2"
    "@ckeditor/ckeditor5-paragraph" "39.0.2"
    "@ckeditor/ckeditor5-select-all" "39.0.2"
    "@ckeditor/ckeditor5-typing" "39.0.2"
    "@ckeditor/ckeditor5-ui" "39.0.2"
    "@ckeditor/ckeditor5-undo" "39.0.2"
    "@ckeditor/ckeditor5-upload" "39.0.2"
    "@ckeditor/ckeditor5-utils" "39.0.2"
    "@ckeditor/ckeditor5-watchdog" "39.0.2"
    "@ckeditor/ckeditor5-widget" "39.0.2"

classnames@^2.2.5:
  version "2.3.2"
  resolved "https://registry.npmjs.org/classnames/-/classnames-2.3.2.tgz"
  integrity sha512-CSbhY4cFEJRe6/GQzIk5qXZ4Jeg5pcsP7b5peFSDpffpe1cqjASH/n9UTjBwOp6XpMSTwQ8Za2K5V02ueA7Tmw==

clsx@^1.1.1:
  version "1.2.1"
  resolved "https://registry.npmjs.org/clsx/-/clsx-1.2.1.tgz"
  integrity sha512-EcR6r5a8bj6pu3ycsa/E/cKVGuTgZJZdsyUYHOksG/UHIiKfjxzRxYJpyVBwYaQeOvghal9fcc4PidlgzugAQg==

clsx@^1.2.1:
  version "1.2.1"
  resolved "https://registry.npmjs.org/clsx/-/clsx-1.2.1.tgz"
  integrity sha512-EcR6r5a8bj6pu3ycsa/E/cKVGuTgZJZdsyUYHOksG/UHIiKfjxzRxYJpyVBwYaQeOvghal9fcc4PidlgzugAQg==

clsx@^2.0.0:
  version "2.0.0"
  resolved "https://registry.npmjs.org/clsx/-/clsx-2.0.0.tgz"
  integrity sha512-rQ1+kcj+ttHG0MKVGBUXwayCCF1oh39BF5COIpRzuCEv8Mwjv0XucrI2ExNTOn9IlLifGClWQcU9BrZORvtw6Q==

clsx@1.1.1:
  version "1.1.1"
  resolved "https://registry.npmjs.org/clsx/-/clsx-1.1.1.tgz"
  integrity sha512-6/bPho624p3S2pMyvP5kKBPXnI3ufHLObBFCfgx+LkeR5lg2XYy2hqZqUf45ypD8COn2bhgGJSUE+l5dhNBieA==

color-convert@^1.9.0:
  version "1.9.3"
  resolved "https://registry.npmjs.org/color-convert/-/color-convert-1.9.3.tgz"
  integrity sha512-QfAUtd+vFdAtFQcC8CCyYt1fYWxSqAiK2cSD6zDB8N3cpsEBAvRxp9zOGg6G/SHHJYAT88/az/IuDGALsNVbGg==
  dependencies:
    color-name "1.1.3"

color-convert@^2.0.1, color-convert@2.0.1:
  version "2.0.1"
  resolved "https://registry.npmjs.org/color-convert/-/color-convert-2.0.1.tgz"
  integrity sha512-RRECPsj7iu/xb5oKYcsFHSppFNnsj/52OVTRKb4zP5onXwVF3zVmmToNcOfGC+CRDpfK/U584fMg38ZHCaElKQ==
  dependencies:
    color-name "~1.1.4"

color-name@^1.0.0, color-name@~1.1.4:
  version "1.1.4"
  resolved "https://registry.npmjs.org/color-name/-/color-name-1.1.4.tgz"
  integrity sha512-dOy+3AuW3a2wNbZHIuMZpTcgjGuLU/uBL/ubcZF9OXbDo8ff4O8yVp5Bf0efS8uEoYo5q4Fx7dY9OgQGXgAsQA==

color-name@1.1.3:
  version "1.1.3"
  resolved "https://registry.npmjs.org/color-name/-/color-name-1.1.3.tgz"
  integrity sha512-72fSenhMw2HZMTVHeCA9KCmpEIbzWiQsjN+BHcBbS9vr1mtt+vJjPdksIBNUmKAW8TFUDPJK5SUU3QhE9NEXDw==

color-parse@1.4.2:
  version "1.4.2"
  resolved "https://registry.npmjs.org/color-parse/-/color-parse-1.4.2.tgz"
  integrity sha512-RI7s49/8yqDj3fECFZjUI1Yi0z/Gq1py43oNJivAIIDSyJiOZLfYCRQEgn8HEVAj++PcRe8AnL2XF0fRJ3BTnA==
  dependencies:
    color-name "^1.0.0"

color-string@^1.9.0:
  version "1.9.1"
  resolved "https://registry.npmjs.org/color-string/-/color-string-1.9.1.tgz"
  integrity sha512-shrVawQFojnZv6xM40anx4CkoDP+fZsw/ZerEMsW/pyzsRbElpsL/DBVW7q3ExxwusdNXI3lXpuhEZkzs8p5Eg==
  dependencies:
    color-name "^1.0.0"
    simple-swizzle "^0.2.2"

color-support@^1.1.2:
  version "1.1.3"
  resolved "https://registry.npmjs.org/color-support/-/color-support-1.1.3.tgz"
  integrity sha512-qiBjkpbMLO/HL68y+lh4q0/O1MZFj2RX6X/KmMa3+gJD3z+WwI1ZzDHysvqHGS3mP6mznPckpXmw1nI9cJjyRg==

color@^4.2.3:
  version "4.2.3"
  resolved "https://registry.npmjs.org/color/-/color-4.2.3.tgz"
  integrity sha512-1rXeuUUiGGrykh+CeBdu5Ie7OJwinCgQY0bc7GCRxy5xVHy+moaqkpL/jqQq0MtQOeYcrqEz4abc5f0KtU7W4A==
  dependencies:
    color-convert "^2.0.1"
    color-string "^1.9.0"

color2k@^2.0.2:
  version "2.0.2"
  resolved "https://registry.npmjs.org/color2k/-/color2k-2.0.2.tgz"
  integrity sha512-kJhwH5nAwb34tmyuqq/lgjEKzlFXn1U99NlnB6Ws4qVaERcRUYeYP1cBw6BJ4vxaWStAUEef4WMr7WjOCnBt8w==

combined-stream@^1.0.8:
  version "1.0.8"
  resolved "https://registry.npmjs.org/combined-stream/-/combined-stream-1.0.8.tgz"
  integrity sha512-FQN4MRfuJeHf7cBbBMJFXhKSDq+2kAArBlmRBvcvFE5BB1HZKXtSFASDhdlz9zOYwxh8lDdnvmMOe/+5cdoEdg==
  dependencies:
    delayed-stream "~1.0.0"

commander@^2.20.0:
  version "2.20.3"
  resolved "https://registry.npmjs.org/commander/-/commander-2.20.3.tgz"
  integrity sha512-GpVkmM8vF2vQUkj2LvZmD35JxeJOLCwJ9cUkugyk2nuhbv3+mJvpLYYt+0+USMxE+oj+ey/lJEnhZw75x/OMcQ==

commander@^4.0.0:
  version "4.1.1"
  resolved "https://registry.npmjs.org/commander/-/commander-4.1.1.tgz"
  integrity sha512-NOKm8xhkzAjzFx8B2v5OAHT+u5pRQc2UCa2Vq9jYL/31o2wi9mxBA7LIFs3sV5VSC49z6pEhfbMULvShKj26WA==

compute-scroll-into-view@^1.0.20:
  version "1.0.20"
  resolved "https://registry.npmjs.org/compute-scroll-into-view/-/compute-scroll-into-view-1.0.20.tgz"
  integrity sha512-UCB0ioiyj8CRjtrvaceBLqqhZCVP+1B8+NWQhmdsm0VXOJtobBCf1dBQmebCCo34qZmUwZfIH2MZLqNHazrfjg==

compute-scroll-into-view@^3.0.2:
  version "3.1.0"
  resolved "https://registry.npmjs.org/compute-scroll-into-view/-/compute-scroll-into-view-3.1.0.tgz"
  integrity sha512-rj8l8pD4bJ1nx+dAkMhV1xB5RuZEyVysfxJqB1pRchh1KVvwOv9b7CGB8ZfjTImVv2oF+sYMUkMZq6Na5Ftmbg==

concat-map@0.0.1:
  version "0.0.1"
  resolved "https://registry.npmjs.org/concat-map/-/concat-map-0.0.1.tgz"
  integrity sha512-/Srv4dswyQNBfohGpz9o6Yb3Gz3SrUDqBH5rTuhGR7ahtlbYKnVxw2bCFMRljaA7EXHaXZ8wsHdodFvbkhKmqg==

console-control-strings@^1.0.0, console-control-strings@^1.1.0:
  version "1.1.0"
  resolved "https://registry.npmjs.org/console-control-strings/-/console-control-strings-1.1.0.tgz"
  integrity sha512-ty/fTekppD2fIwRvnZAVdeOiGd1c7YXEixbgJTNzqcxJWKQnjJ/V1bNEEE6hygpM3WjwHFUVK6HTjWSzV4a8sQ==

context-api@0.0.2:
  version "0.0.2"
  resolved "https://registry.npmjs.org/context-api/-/context-api-0.0.2.tgz"
  integrity sha512-/92pnGJS5irtzF2SNFjnNl2c2EIbFiyB88N8/r0CS9xnE3F/+wQKBV2ym7Fr7XVUqV9QvTkmEDuwdiyzaX3M8w==
  dependencies:
    immer "^9.0.1"

convert-source-map@^1.5.0:
  version "1.9.0"
  resolved "https://registry.npmjs.org/convert-source-map/-/convert-source-map-1.9.0.tgz"
  integrity sha512-ASFBup0Mz1uyiIjANan1jzLQami9z1PoYSZCiiYW2FczPbenXc45FZdBZLzOT+r6+iciuEModtmCti+hjaAk0A==

cookie@^0.4.0:
  version "0.4.2"
  resolved "https://registry.npmjs.org/cookie/-/cookie-0.4.2.tgz"
  integrity sha512-aSWTXFzaKWkvHO1Ny/s+ePFpvKsPnjc551iI41v3ny/ow6tBG5Vd+FuqGNhh1LxOmVzOlGUriIlOaokOvhaStA==

cookie@^0.5.0:
  version "0.5.0"
  resolved "https://registry.npmjs.org/cookie/-/cookie-0.5.0.tgz"
  integrity sha512-YZ3GUyn/o8gfKJlnlX7g7xq4gyO6OSuhGPKaaGssGB2qgDUS0gPgtTvoyZLTt9Ab6dC4hfc9dV5arkvc/OCmrw==

cookies-next@^4.0.0:
  version "4.0.0"
  resolved "https://registry.npmjs.org/cookies-next/-/cookies-next-4.0.0.tgz"
  integrity sha512-3TyzeltFCGgdOlVOVTPClSq+YV9ZCdOyA3aHRZv9f5aSgg7EyI4NSvXFOCgzT/xIxeHR4Rz8/z5Tdo9oPqaVpA==
  dependencies:
    "@types/cookie" "^0.4.1"
    "@types/node" "^16.10.2"
    cookie "^0.4.0"

core-js@^3.6.0, core-js@^3.8.3:
  version "3.33.2"
  resolved "https://registry.npmjs.org/core-js/-/core-js-3.33.2.tgz"
  integrity sha512-XeBzWI6QL3nJQiHmdzbAOiMYqjrb7hwU7A39Qhvd/POSa/t9E1AeZyEZx3fNvp/vtM8zXwhoL0FsiS0hD0pruQ==

core-util-is@~1.0.0:
  version "1.0.3"
  resolved "https://registry.npmjs.org/core-util-is/-/core-util-is-1.0.3.tgz"
  integrity sha512-ZQBvi1DcpJ4GDqanjucZ2Hj3wEO5pZDS89BWbkcrvdxksJorwUDDZamX9ldFkp9aw2lmBDLgkObEA4DWNJ9FYQ==

cosmiconfig@^7.0.0:
  version "7.1.0"
  resolved "https://registry.npmjs.org/cosmiconfig/-/cosmiconfig-7.1.0.tgz"
  integrity sha512-AdmX6xUzdNASswsFtmwSt7Vj8po9IuqXm0UXz7QKPuEUmPB4XyjGfaAr2PSuELMwkRMVH1EpIkX5bTZGRB3eCA==
  dependencies:
    "@types/parse-json" "^4.0.0"
    import-fresh "^3.2.1"
    parse-json "^5.0.0"
    path-type "^4.0.0"
    yaml "^1.10.0"

cross-spawn@^7.0.0, cross-spawn@^7.0.2:
  version "7.0.3"
  resolved "https://registry.npmjs.org/cross-spawn/-/cross-spawn-7.0.3.tgz"
  integrity sha512-iRDPJKUPVEND7dHPO8rkbOnPpyDygcDFtWjpeWNCgy8WP2rXcxXL8TskReQl6OrB2G7+UJrags1q15Fudc7G6w==
  dependencies:
    path-key "^3.1.0"
    shebang-command "^2.0.0"
    which "^2.0.1"

css-line-break@^2.1.0:
  version "2.1.0"
  resolved "https://registry.npmjs.org/css-line-break/-/css-line-break-2.1.0.tgz"
  integrity sha512-FHcKFCZcAha3LwfVBhCQbW2nCNbkZXn7KVUJcsT5/P8YmfsVja0FMPJr0B903j/E69HUphKiV9iQArX8SDYA4w==
  dependencies:
    utrie "^1.0.2"

css-vendor@^2.0.8:
  version "2.0.8"
  resolved "https://registry.npmjs.org/css-vendor/-/css-vendor-2.0.8.tgz"
  integrity sha512-x9Aq0XTInxrkuFeHKbYC7zWY8ai7qJ04Kxd9MnvbC1uO5DagxoHQjm4JvG+vCdXOoFtCjbL2XSZfxmoYa9uQVQ==
  dependencies:
    "@babel/runtime" "^7.8.3"
    is-in-browser "^1.0.2"

cssesc@^3.0.0:
  version "3.0.0"
  resolved "https://registry.npmjs.org/cssesc/-/cssesc-3.0.0.tgz"
  integrity sha512-/Tb/JcjK111nNScGob5MNtsntNM1aCNUDipB/TkwZFhyDrrE47SOx/18wF2bbjgc3ZzCSKW1T5nt5EbFoAz/Vg==

csstype@^3.0.11, csstype@^3.0.2, csstype@^3.1.0, csstype@^3.1.2:
  version "3.1.2"
  resolved "https://registry.npmjs.org/csstype/-/csstype-3.1.2.tgz"
  integrity sha512-I7K1Uu0MBPzaFKg4nI5Q7Vs2t+3gWWW648spaF+Rg7pI9ds18Ugn+lvg4SHczUdKlHI5LWBXyqfS8+DufyBsgQ==

damerau-levenshtein@^1.0.8:
  version "1.0.8"
  resolved "https://registry.npmjs.org/damerau-levenshtein/-/damerau-levenshtein-1.0.8.tgz"
  integrity sha512-sdQSFB7+llfUcQHUQO3+B8ERRj0Oa4w9POWMI/puGtuf7gFywGmkaLCElnudfTiKZV+NvHqL0ifzdrI8Ro7ESA==

date-fns@^2.0.0, date-fns@^2.25.0, date-fns@2.28.0:
  version "2.28.0"
  resolved "https://registry.npmjs.org/date-fns/-/date-fns-2.28.0.tgz"
  integrity sha512-8d35hViGYx/QH0icHYCeLmsLmMUheMmTyV9Fcm6gvNwdw31yXXH+O85sOBJ+OLnLQMKZowvpKb6FgMIQjcpvQw==

dayjs@^1.10.7, dayjs@^1.11.10, dayjs@^1.8.17:
  version "1.11.10"
  resolved "https://registry.npmjs.org/dayjs/-/dayjs-1.11.10.tgz"
  integrity sha512-vjAczensTgRcqDERK0SR2XMwsF/tSvnvlv6VcF2GIhg6Sx4yOIt/irsr1RDJsKiIyBzJDpCoXiWWq28MqH2cnQ==

debug@^2.6.6:
  version "2.6.9"
  resolved "https://registry.npmjs.org/debug/-/debug-2.6.9.tgz"
  integrity sha512-bC7ElrdJaJnPbAP+1EotYvqZsb3ecl5wi6Bfi6BJTUcNowp6cvspg0jXznRTKDjm/E7AdgFBVeAPVMNcKGsHMA==
  dependencies:
    ms "2.0.0"

debug@^3.2.7:
  version "3.2.7"
  resolved "https://registry.npmjs.org/debug/-/debug-3.2.7.tgz"
  integrity sha512-CFjzYYAi4ThfiQvizrFQevTTXHtnCqWfe7x1AhgEscTz6ZbLbfoLRLPugTQyBth6f8ZERVUSyWHFD/7Wu4t1XQ==
  dependencies:
    ms "^2.1.1"

debug@^4.1.1, debug@^4.3.2, debug@^4.3.4, debug@4:
  version "4.3.4"
  resolved "https://registry.npmjs.org/debug/-/debug-4.3.4.tgz"
  integrity sha512-PRWFHuSU3eDtQJPvnNY7Jcket1j0t5OuOsFzPPzsekD52Zl8qUfFIPEiswXqIvHWGVHOgX+7G/vCNNhehwxfkQ==
  dependencies:
    ms "2.1.2"

deep-is@^0.1.3:
  version "0.1.4"
  resolved "https://registry.npmjs.org/deep-is/-/deep-is-0.1.4.tgz"
  integrity sha512-oIPzksmTg4/MriiaYGO+okXDT7ztn/w3Eptv/+gSIdMdKsJo0u4CfYNFJPy+4SKMuCqGw2wxnA+URMg3t8a/bQ==

deepmerge@4.3.1:
  version "4.3.1"
  resolved "https://registry.npmjs.org/deepmerge/-/deepmerge-4.3.1.tgz"
  integrity sha512-3sUqbMEc77XqpdNO7FRyRog+eW3ph+GYCbj+rK+uYyRMuwsVy0rMiVtPn+QJlKFvWP/1PYpapqYn0Me2knFn+A==

define-data-property@^1.0.1, define-data-property@^1.1.1:
  version "1.1.1"
  resolved "https://registry.npmjs.org/define-data-property/-/define-data-property-1.1.1.tgz"
  integrity sha512-E7uGkTzkk1d0ByLeSc6ZsFS79Axg+m1P/VsgYsxHgiuc3tFSj+MjMIwe90FC4lOAZzNBdY7kkO2P2wKdsQ1vgQ==
  dependencies:
    get-intrinsic "^1.2.1"
    gopd "^1.0.1"
    has-property-descriptors "^1.0.0"

define-properties@^1.1.3, define-properties@^1.1.4, define-properties@^1.2.0, define-properties@^1.2.1:
  version "1.2.1"
  resolved "https://registry.npmjs.org/define-properties/-/define-properties-1.2.1.tgz"
  integrity sha512-8QmQKqEASLd5nx0U1B1okLElbUuuttJ/AnYmRXbbbGDWh6uS208EjD4Xqq/I9wK7u0v6O08XhTWnt5XtEbR6Dg==
  dependencies:
    define-data-property "^1.0.1"
    has-property-descriptors "^1.0.0"
    object-keys "^1.1.1"

delayed-stream@~1.0.0:
  version "1.0.0"
  resolved "https://registry.npmjs.org/delayed-stream/-/delayed-stream-1.0.0.tgz"
  integrity sha512-ZySD7Nf91aLB0RxL4KGrKHBXl7Eds1DAmEdcoVawXnLD7SDhpNgtuII2aAkg7a7QS41jxPSZ17p4VdGnMHk3MQ==

delegates@^1.0.0:
  version "1.0.0"
  resolved "https://registry.npmjs.org/delegates/-/delegates-1.0.0.tgz"
  integrity sha512-bd2L678uiWATM6m5Z1VzNCErI3jiGzt6HGY8OVICs40JQq/HALfbyNJmp0UDakEY4pMMaN0Ly5om/B1VI/+xfQ==

dequal@^2.0.3:
  version "2.0.3"
  resolved "https://registry.npmjs.org/dequal/-/dequal-2.0.3.tgz"
  integrity sha512-0je+qPKHEMohvfRTCEo3CrPG6cAzAYgmzKyxRiYSSDkS6eGJdyVJm7WaYA5ECaAD9wLB2T4EEeymA5aFVcYXCA==

detect-libc@^2.0.0:
  version "2.0.2"
  resolved "https://registry.npmjs.org/detect-libc/-/detect-libc-2.0.2.tgz"
  integrity sha512-UX6sGumvvqSaXgdKGUsgZWqcUyIXZ/vZTrlRT/iobiKhGL0zL4d3osHj3uqllWJK+i+sixDS/3COVEOFbupFyw==

detect-node-es@^1.1.0:
  version "1.1.0"
  resolved "https://registry.npmjs.org/detect-node-es/-/detect-node-es-1.1.0.tgz"
  integrity sha512-ypdmJU/TbBby2Dxibuv7ZLW3Bs1QEmM7nHjEANfohJLvE0XVujisn1qPJcZxg+qDucsr+bP6fLD1rPS3AhJ7EQ==

didyoumean@^1.2.2:
  version "1.2.2"
  resolved "https://registry.npmjs.org/didyoumean/-/didyoumean-1.2.2.tgz"
  integrity sha512-gxtyfqMg7GKyhQmb056K7M3xszy/myH8w+B4RT+QXBQsvAOdc3XymqDDPHx1BgPgsdAA5SIifona89YtRATDzw==

dir-glob@^3.0.1:
  version "3.0.1"
  resolved "https://registry.npmjs.org/dir-glob/-/dir-glob-3.0.1.tgz"
  integrity sha512-WkrWp9GR4KXfKGYzOLmTuGVi1UWFfws377n9cc55/tb6DuqyF6pcQ5AbiHEshaDpY9v6oaSr2XCDidGmMwdzIA==
  dependencies:
    path-type "^4.0.0"

direction@^1.0.3:
  version "1.0.4"
  resolved "https://registry.npmjs.org/direction/-/direction-1.0.4.tgz"
  integrity sha512-GYqKi1aH7PJXxdhTeZBFrg8vUBeKXi+cNprXsC1kpJcbcVnV9wBsrOu1cQEdG0WeQwlfHiy3XvnKfIrJ2R0NzQ==

dlv@^1.1.3:
  version "1.1.3"
  resolved "https://registry.npmjs.org/dlv/-/dlv-1.1.3.tgz"
  integrity sha512-+HlytyjlPKnIG8XuRG8WvmBP8xs8P71y+SKKS6ZXWoEgLuePxtDoUEiH7WkdePWrQ5JBpE6aoVqfZfJUQkjXwA==

doctrine@^2.1.0:
  version "2.1.0"
  resolved "https://registry.npmjs.org/doctrine/-/doctrine-2.1.0.tgz"
  integrity sha512-35mSku4ZXK0vfCuHEDAwt55dg2jNajHZ1odvF+8SSr82EsZY4QmXfuWso8oEd8zRhVObSN18aM0CjSdoBX7zIw==
  dependencies:
    esutils "^2.0.2"

doctrine@^3.0.0:
  version "3.0.0"
  resolved "https://registry.npmjs.org/doctrine/-/doctrine-3.0.0.tgz"
  integrity sha512-yS+Q5i3hBf7GBkd4KG8a7eBNNWNGLTaEwwYWUijIYM7zrlYDM0BFXHjjPWlWZ1Rg7UaddZeIDmi9jF3HmqiQ2w==
  dependencies:
    esutils "^2.0.2"

dom-css@^2.0.0:
  version "2.1.0"
  resolved "https://registry.npmjs.org/dom-css/-/dom-css-2.1.0.tgz"
  integrity sha512-w9kU7FAbaSh3QKijL6n59ofAhkkmMJ31GclJIz/vyQdjogfyxcB6Zf8CZyibOERI5o0Hxz30VmJS7+7r5fEj2Q==
  dependencies:
    add-px-to-style "1.0.0"
    prefix-style "2.0.1"
    to-camel-case "1.0.0"

dom-helpers@^5.0.1:
  version "5.2.1"
  resolved "https://registry.npmjs.org/dom-helpers/-/dom-helpers-5.2.1.tgz"
  integrity sha512-nRCa7CK3VTrM2NmGkIy4cbK7IZlgBE/PYMn55rrXefr5xXDP0LdtfPnblFDoVdcAfslJ7or6iqAUnx0CCGIWQA==
  dependencies:
    "@babel/runtime" "^7.8.7"
    csstype "^3.0.2"

dompurify@^2.2.0:
  version "2.4.7"
  resolved "https://registry.npmjs.org/dompurify/-/dompurify-2.4.7.tgz"
  integrity sha512-kxxKlPEDa6Nc5WJi+qRgPbOAbgTpSULL+vI3NUXsZMlkJxTqYI9wg5ZTay2sFrdZRWHPWNi+EdAhcJf81WtoMQ==

duplexer2@^0.1.2:
  version "0.1.4"
  resolved "https://registry.npmjs.org/duplexer2/-/duplexer2-0.1.4.tgz"
  integrity sha512-asLFVfWWtJ90ZyOUHMqk7/S2w2guQKxUI2itj3d92ADHhxUSbCMGi1f1cBcJ7xM1To+pE/Khbwo1yuNbMEPKeA==
  dependencies:
    readable-stream "^2.0.2"

eastasianwidth@^0.2.0:
  version "0.2.0"
  resolved "https://registry.npmjs.org/eastasianwidth/-/eastasianwidth-0.2.0.tgz"
  integrity sha512-I88TYZWc9XiYHRQ4/3c5rjjfgkjhLyW2luGIheGERbNQ6OY7yTybanSpDXZa8y7VUP9YmDcYa+eyq4ca7iLqWA==

ecdsa-sig-formatter@1.0.11:
  version "1.0.11"
  resolved "https://registry.npmjs.org/ecdsa-sig-formatter/-/ecdsa-sig-formatter-1.0.11.tgz"
  integrity sha512-nagl3RYrbNv6kQkeJIpt6NJZy8twLB/2vtz6yN9Z4vRKHN4/QZJIEbqohALSgwKdnksuY3k5Addp5lg8sVoVcQ==
  dependencies:
    safe-buffer "^5.0.1"

electron-to-chromium@^1.5.41:
  version "1.5.67"
  resolved "https://registry.npmjs.org/electron-to-chromium/-/electron-to-chromium-1.5.67.tgz"
  integrity sha512-nz88NNBsD7kQSAGGJyp8hS6xSPtWwqNogA0mjtc2nUYeEf3nURK9qpV18TuBdDmEDgVWotS8Wkzf+V52dSQ/LQ==

emoji-regex@^8.0.0:
  version "8.0.0"
  resolved "https://registry.npmjs.org/emoji-regex/-/emoji-regex-8.0.0.tgz"
  integrity sha512-MSjYzcWNOA0ewAHpz0MxpYFvwg6yjy1NG3xteoqz644VCo/RPgnr1/GGt+ic3iJTzQ8Eu3TdM14SawnVUmGE6A==

emoji-regex@^9.2.2:
  version "9.2.2"
  resolved "https://registry.npmjs.org/emoji-regex/-/emoji-regex-9.2.2.tgz"
  integrity sha512-L18DaJsXSUk2+42pv8mLs5jJT2hqFkFE4j21wOmgbUqsZ2hL72NsUU785g9RXgo3s0ZNgVl42TiHp3ZtOv/Vyg==

emojis-list@^3.0.0:
  version "3.0.0"
  resolved "https://registry.npmjs.org/emojis-list/-/emojis-list-3.0.0.tgz"
  integrity sha512-/kyM18EfinwXZbno9FyUGeFh87KC8HRQBQGildHZbEuRyWFOmv1U10o9BBp8XVZDVNNuQKyIGIu5ZYAAXJ0V2Q==

enhanced-resolve@^5.17.1, enhanced-resolve@^5.7.0:
  version "5.17.1"
  resolved "https://registry.npmjs.org/enhanced-resolve/-/enhanced-resolve-5.17.1.tgz"
  integrity sha512-LMHl3dXhTcfv8gM4kEzIUeTQ+7fpdA0l2tUf34BddXPkz2A5xJ5L/Pchd5BL6rdccM9QGvu0sWZzK1Z1t4wwyg==
  dependencies:
    graceful-fs "^4.2.4"
    tapable "^2.2.0"

enquire.js@^2.1.6:
  version "2.1.6"
  resolved "https://registry.npmjs.org/enquire.js/-/enquire.js-2.1.6.tgz"
  integrity sha512-/KujNpO+PT63F7Hlpu4h3pE3TokKRHN26JYmQpPyjkRD/N57R7bPDNojMXdi7uveAKjYB7yQnartCxZnFWr0Xw==

error-ex@^1.3.1:
  version "1.3.2"
  resolved "https://registry.npmjs.org/error-ex/-/error-ex-1.3.2.tgz"
  integrity sha512-7dFHNmqeFSEt2ZBsCriorKnn3Z2pj+fd9kmI6QoWw4//DL+icEBfc0U7qJCisqrTsKTjw4fNFy2pW9OqStD84g==
  dependencies:
    is-arrayish "^0.2.1"

es-abstract@^1.22.1:
  version "1.22.3"
  resolved "https://registry.npmjs.org/es-abstract/-/es-abstract-1.22.3.tgz"
  integrity sha512-eiiY8HQeYfYH2Con2berK+To6GrK2RxbPawDkGq4UiCQQfZHb6wX9qQqkbpPqaxQFcl8d9QzZqo0tGE0VcrdwA==
  dependencies:
    array-buffer-byte-length "^1.0.0"
    arraybuffer.prototype.slice "^1.0.2"
    available-typed-arrays "^1.0.5"
    call-bind "^1.0.5"
    es-set-tostringtag "^2.0.1"
    es-to-primitive "^1.2.1"
    function.prototype.name "^1.1.6"
    get-intrinsic "^1.2.2"
    get-symbol-description "^1.0.0"
    globalthis "^1.0.3"
    gopd "^1.0.1"
    has-property-descriptors "^1.0.0"
    has-proto "^1.0.1"
    has-symbols "^1.0.3"
    hasown "^2.0.0"
    internal-slot "^1.0.5"
    is-array-buffer "^3.0.2"
    is-callable "^1.2.7"
    is-negative-zero "^2.0.2"
    is-regex "^1.1.4"
    is-shared-array-buffer "^1.0.2"
    is-string "^1.0.7"
    is-typed-array "^1.1.12"
    is-weakref "^1.0.2"
    object-inspect "^1.13.1"
    object-keys "^1.1.1"
    object.assign "^4.1.4"
    regexp.prototype.flags "^1.5.1"
    safe-array-concat "^1.0.1"
    safe-regex-test "^1.0.0"
    string.prototype.trim "^1.2.8"
    string.prototype.trimend "^1.0.7"
    string.prototype.trimstart "^1.0.7"
    typed-array-buffer "^1.0.0"
    typed-array-byte-length "^1.0.0"
    typed-array-byte-offset "^1.0.0"
    typed-array-length "^1.0.4"
    unbox-primitive "^1.0.2"
    which-typed-array "^1.1.13"

es-iterator-helpers@^1.0.12:
  version "1.0.15"
  resolved "https://registry.npmjs.org/es-iterator-helpers/-/es-iterator-helpers-1.0.15.tgz"
  integrity sha512-GhoY8uYqd6iwUl2kgjTm4CZAf6oo5mHK7BPqx3rKgx893YSsy0LGHV6gfqqQvZt/8xM8xeOnfXBCfqclMKkJ5g==
  dependencies:
    asynciterator.prototype "^1.0.0"
    call-bind "^1.0.2"
    define-properties "^1.2.1"
    es-abstract "^1.22.1"
    es-set-tostringtag "^2.0.1"
    function-bind "^1.1.1"
    get-intrinsic "^1.2.1"
    globalthis "^1.0.3"
    has-property-descriptors "^1.0.0"
    has-proto "^1.0.1"
    has-symbols "^1.0.3"
    internal-slot "^1.0.5"
    iterator.prototype "^1.1.2"
    safe-array-concat "^1.0.1"

es-module-lexer@^1.2.1:
  version "1.5.4"
  resolved "https://registry.npmjs.org/es-module-lexer/-/es-module-lexer-1.5.4.tgz"
  integrity sha512-MVNK56NiMrOwitFB7cqDwq0CQutbw+0BvLshJSse0MUNU+y1FC3bUS/AQg7oUng+/wKrrki7JfmwtVHkVfPLlw==

es-set-tostringtag@^2.0.1:
  version "2.0.2"
  resolved "https://registry.npmjs.org/es-set-tostringtag/-/es-set-tostringtag-2.0.2.tgz"
  integrity sha512-BuDyupZt65P9D2D2vA/zqcI3G5xRsklm5N3xCwuiy+/vKy8i0ifdsQP1sLgO4tZDSCaQUSnmC48khknGMV3D2Q==
  dependencies:
    get-intrinsic "^1.2.2"
    has-tostringtag "^1.0.0"
    hasown "^2.0.0"

es-shim-unscopables@^1.0.0:
  version "1.0.2"
  resolved "https://registry.npmjs.org/es-shim-unscopables/-/es-shim-unscopables-1.0.2.tgz"
  integrity sha512-J3yBRXCzDu4ULnQwxyToo/OjdMx6akgVC7K6few0a7F/0wLtmKKN7I73AH5T2836UuXRqN7Qg+IIUw/+YJksRw==
  dependencies:
    hasown "^2.0.0"

es-to-primitive@^1.2.1:
  version "1.2.1"
  resolved "https://registry.npmjs.org/es-to-primitive/-/es-to-primitive-1.2.1.tgz"
  integrity sha512-QCOllgZJtaUo9miYBcLChTUaHNjJF3PYs1VidD7AwiEj1kYxKeQTctLAezAOH5ZKRH0g2IgPn6KwB4IT8iRpvA==
  dependencies:
    is-callable "^1.1.4"
    is-date-object "^1.0.1"
    is-symbol "^1.0.2"

escalade@^3.1.1, escalade@^3.2.0:
  version "3.2.0"
  resolved "https://registry.npmjs.org/escalade/-/escalade-3.2.0.tgz"
  integrity sha512-WUj2qlxaQtO4g6Pq5c29GTcWGDyd8itL8zTlipgECz3JesAiiOKotd8JU6otB3PACgG6xkJUyVhboMS+bje/jA==

escape-string-regexp@^1.0.5:
  version "1.0.5"
  resolved "https://registry.npmjs.org/escape-string-regexp/-/escape-string-regexp-1.0.5.tgz"
  integrity sha512-vbRorB5FUQWvla16U8R/qgaFIya2qGzwDrNmCZuYKrbdSUMG6I1ZCGQRefkRVhuOkIGVne7BQ35DSfo1qvJqFg==

escape-string-regexp@^4.0.0:
  version "4.0.0"
  resolved "https://registry.npmjs.org/escape-string-regexp/-/escape-string-regexp-4.0.0.tgz"
  integrity sha512-TtpcNJ3XAzx3Gq8sWRzJaVajRs0uVxA2YAkdb1jm2YkPz4G6egUFAyA3n5vtEIZefPk5Wa4UXbKuS5fKkJWdgA==

eslint-config-next@12.1.6:
  version "12.1.6"
  resolved "https://registry.npmjs.org/eslint-config-next/-/eslint-config-next-12.1.6.tgz"
  integrity sha512-qoiS3g/EPzfCTkGkaPBSX9W0NGE/B1wNO3oWrd76QszVGrdpLggNqcO8+LR6MB0CNqtp9Q8NoeVrxNVbzM9hqA==
  dependencies:
    "@next/eslint-plugin-next" "12.1.6"
    "@rushstack/eslint-patch" "^1.1.3"
    "@typescript-eslint/parser" "^5.21.0"
    eslint-import-resolver-node "^0.3.6"
    eslint-import-resolver-typescript "^2.7.1"
    eslint-plugin-import "^2.26.0"
    eslint-plugin-jsx-a11y "^6.5.1"
    eslint-plugin-react "^7.29.4"
    eslint-plugin-react-hooks "^4.5.0"

eslint-config-prettier@8.5.0:
  version "8.5.0"
  resolved "https://registry.npmjs.org/eslint-config-prettier/-/eslint-config-prettier-8.5.0.tgz"
  integrity sha512-obmWKLUNCnhtQRKc+tmnYuQl0pFU1ibYJQ5BGhTVB08bHe9wC8qUeG7c08dj9XX+AuPj1YSGSQIHl1pnDHZR0Q==

eslint-import-resolver-node@^0.3.6, eslint-import-resolver-node@^0.3.9:
  version "0.3.9"
  resolved "https://registry.npmjs.org/eslint-import-resolver-node/-/eslint-import-resolver-node-0.3.9.tgz"
  integrity sha512-WFj2isz22JahUv+B788TlO3N6zL3nNJGU8CcZbPZvVEkBPaJdCV4vy5wyghty5ROFbCRnm132v8BScu5/1BQ8g==
  dependencies:
    debug "^3.2.7"
    is-core-module "^2.13.0"
    resolve "^1.22.4"

eslint-import-resolver-typescript@^2.7.1:
  version "2.7.1"
  resolved "https://registry.npmjs.org/eslint-import-resolver-typescript/-/eslint-import-resolver-typescript-2.7.1.tgz"
  integrity sha512-00UbgGwV8bSgUv34igBDbTOtKhqoRMy9bFjNehT40bXg6585PNIct8HhXZ0SybqB9rWtXj9crcku8ndDn/gIqQ==
  dependencies:
    debug "^4.3.4"
    glob "^7.2.0"
    is-glob "^4.0.3"
    resolve "^1.22.0"
    tsconfig-paths "^3.14.1"

eslint-module-utils@^2.8.0:
  version "2.8.0"
  resolved "https://registry.npmjs.org/eslint-module-utils/-/eslint-module-utils-2.8.0.tgz"
  integrity sha512-aWajIYfsqCKRDgUfjEXNN/JlrzauMuSEy5sbd7WXbtW3EH6A6MpwEh42c7qD+MqQo9QMJ6fWLAeIJynx0g6OAw==
  dependencies:
    debug "^3.2.7"

eslint-plugin-import@*, eslint-plugin-import@^2.26.0:
  version "2.29.0"
  resolved "https://registry.npmjs.org/eslint-plugin-import/-/eslint-plugin-import-2.29.0.tgz"
  integrity sha512-QPOO5NO6Odv5lpoTkddtutccQjysJuFxoPS7fAHO+9m9udNHvTCPSAMW9zGAYj8lAIdr40I8yPCdUYrncXtrwg==
  dependencies:
    array-includes "^3.1.7"
    array.prototype.findlastindex "^1.2.3"
    array.prototype.flat "^1.3.2"
    array.prototype.flatmap "^1.3.2"
    debug "^3.2.7"
    doctrine "^2.1.0"
    eslint-import-resolver-node "^0.3.9"
    eslint-module-utils "^2.8.0"
    hasown "^2.0.0"
    is-core-module "^2.13.1"
    is-glob "^4.0.3"
    minimatch "^3.1.2"
    object.fromentries "^2.0.7"
    object.groupby "^1.0.1"
    object.values "^1.1.7"
    semver "^6.3.1"
    tsconfig-paths "^3.14.2"

eslint-plugin-jsx-a11y@^6.5.1:
  version "6.7.1"
  resolved "https://registry.npmjs.org/eslint-plugin-jsx-a11y/-/eslint-plugin-jsx-a11y-6.7.1.tgz"
  integrity sha512-63Bog4iIethyo8smBklORknVjB0T2dwB8Mr/hIC+fBS0uyHdYYpzM/Ed+YC8VxTjlXHEWFOdmgwcDn1U2L9VCA==
  dependencies:
    "@babel/runtime" "^7.20.7"
    aria-query "^5.1.3"
    array-includes "^3.1.6"
    array.prototype.flatmap "^1.3.1"
    ast-types-flow "^0.0.7"
    axe-core "^4.6.2"
    axobject-query "^3.1.1"
    damerau-levenshtein "^1.0.8"
    emoji-regex "^9.2.2"
    has "^1.0.3"
    jsx-ast-utils "^3.3.3"
    language-tags "=1.0.5"
    minimatch "^3.1.2"
    object.entries "^1.1.6"
    object.fromentries "^2.0.6"
    semver "^6.3.0"

eslint-plugin-prettier@4.0.0:
  version "4.0.0"
  resolved "https://registry.npmjs.org/eslint-plugin-prettier/-/eslint-plugin-prettier-4.0.0.tgz"
  integrity sha512-98MqmCJ7vJodoQK359bqQWaxOE0CS8paAz/GgjaZLyex4TTk3g9HugoO89EqWCrFiOqn9EVvcoo7gZzONCWVwQ==
  dependencies:
    prettier-linter-helpers "^1.0.0"

eslint-plugin-react-hooks@^4.5.0:
  version "4.6.0"
  resolved "https://registry.npmjs.org/eslint-plugin-react-hooks/-/eslint-plugin-react-hooks-4.6.0.tgz"
  integrity sha512-oFc7Itz9Qxh2x4gNHStv3BqJq54ExXmfC+a1NjAta66IAN87Wu0R/QArgIS9qKzX3dXKPI9H5crl9QchNMY9+g==

eslint-plugin-react@^7.29.4:
  version "7.33.2"
  resolved "https://registry.npmjs.org/eslint-plugin-react/-/eslint-plugin-react-7.33.2.tgz"
  integrity sha512-73QQMKALArI8/7xGLNI/3LylrEYrlKZSb5C9+q3OtOewTnMQi5cT+aE9E41sLCmli3I9PGGmD1yiZydyo4FEPw==
  dependencies:
    array-includes "^3.1.6"
    array.prototype.flatmap "^1.3.1"
    array.prototype.tosorted "^1.1.1"
    doctrine "^2.1.0"
    es-iterator-helpers "^1.0.12"
    estraverse "^5.3.0"
    jsx-ast-utils "^2.4.1 || ^3.0.0"
    minimatch "^3.1.2"
    object.entries "^1.1.6"
    object.fromentries "^2.0.6"
    object.hasown "^1.1.2"
    object.values "^1.1.6"
    prop-types "^15.8.1"
    resolve "^2.0.0-next.4"
    semver "^6.3.1"
    string.prototype.matchall "^4.0.8"

eslint-scope@^7.1.1:
  version "7.2.2"
  resolved "https://registry.npmjs.org/eslint-scope/-/eslint-scope-7.2.2.tgz"
  integrity sha512-dOt21O7lTMhDM+X9mB4GX+DZrZtCUJPL/wlcTqxyrx5IvO0IYtILdtrQGQp+8n5S0gwSVmOf9NQrjMOgfQZlIg==
  dependencies:
    esrecurse "^4.3.0"
    estraverse "^5.2.0"

eslint-scope@5.1.1:
  version "5.1.1"
  resolved "https://registry.npmjs.org/eslint-scope/-/eslint-scope-5.1.1.tgz"
  integrity sha512-2NxwbF/hZ0KpepYN0cNbo+FN6XoK7GaHlQhgx/hIZl6Va0bF45RQOOwhLIy8lQDbuCiadSLCBnH2CFYquit5bw==
  dependencies:
    esrecurse "^4.3.0"
    estraverse "^4.1.1"

eslint-utils@^3.0.0:
  version "3.0.0"
  resolved "https://registry.npmjs.org/eslint-utils/-/eslint-utils-3.0.0.tgz"
  integrity sha512-uuQC43IGctw68pJA1RgbQS8/NP7rch6Cwd4j3ZBtgo4/8Flj4eGE7ZYSZRN3iq5pVUv6GPdW5Z1RFleo84uLDA==
  dependencies:
    eslint-visitor-keys "^2.0.0"

eslint-visitor-keys@^2.0.0:
  version "2.1.0"
  resolved "https://registry.npmjs.org/eslint-visitor-keys/-/eslint-visitor-keys-2.1.0.tgz"
  integrity sha512-0rSmRBzXgDzIsD6mGdJgevzgezI534Cer5L/vyMX0kHzT/jiB43jRhd9YUlMGYLQy2zprNmoT8qasCGtY+QaKw==

eslint-visitor-keys@^3.3.0, eslint-visitor-keys@^3.4.1:
  version "3.4.3"
  resolved "https://registry.npmjs.org/eslint-visitor-keys/-/eslint-visitor-keys-3.4.3.tgz"
  integrity sha512-wpc+LXeiyiisxPlEkUzU6svyS1frIO3Mgxj1fdy7Pm8Ygzguax2N3Fa/D/ag1WqbOprdI+uY6wMUl8/a2G+iag==

eslint@*, "eslint@^2 || ^3 || ^4 || ^5 || ^6 || ^7.2.0 || ^8", "eslint@^3 || ^4 || ^5 || ^6 || ^7 || ^8", "eslint@^3.0.0 || ^4.0.0 || ^5.0.0 || ^6.0.0 || ^7.0.0 || ^8.0.0-0", "eslint@^6.0.0 || ^7.0.0 || ^8.0.0", "eslint@^7.23.0 || ^8.0.0", eslint@>=5, eslint@>=7.0.0, eslint@>=7.28.0, eslint@8.17.0:
  version "8.17.0"
  resolved "https://registry.npmjs.org/eslint/-/eslint-8.17.0.tgz"
  integrity sha512-gq0m0BTJfci60Fz4nczYxNAlED+sMcihltndR8t9t1evnU/azx53x3t2UHXC/uRjcbvRw/XctpaNygSTcQD+Iw==
  dependencies:
    "@eslint/eslintrc" "^1.3.0"
    "@humanwhocodes/config-array" "^0.9.2"
    ajv "^6.10.0"
    chalk "^4.0.0"
    cross-spawn "^7.0.2"
    debug "^4.3.2"
    doctrine "^3.0.0"
    escape-string-regexp "^4.0.0"
    eslint-scope "^7.1.1"
    eslint-utils "^3.0.0"
    eslint-visitor-keys "^3.3.0"
    espree "^9.3.2"
    esquery "^1.4.0"
    esutils "^2.0.2"
    fast-deep-equal "^3.1.3"
    file-entry-cache "^6.0.1"
    functional-red-black-tree "^1.0.1"
    glob-parent "^6.0.1"
    globals "^13.15.0"
    ignore "^5.2.0"
    import-fresh "^3.0.0"
    imurmurhash "^0.1.4"
    is-glob "^4.0.0"
    js-yaml "^4.1.0"
    json-stable-stringify-without-jsonify "^1.0.1"
    levn "^0.4.1"
    lodash.merge "^4.6.2"
    minimatch "^3.1.2"
    natural-compare "^1.4.0"
    optionator "^0.9.1"
    regexpp "^3.2.0"
    strip-ansi "^6.0.1"
    strip-json-comments "^3.1.0"
    text-table "^0.2.0"
    v8-compile-cache "^2.0.3"

espree@^9.3.2, espree@^9.4.0:
  version "9.6.1"
  resolved "https://registry.npmjs.org/espree/-/espree-9.6.1.tgz"
  integrity sha512-oruZaFkjorTpF32kDSI5/75ViwGeZginGGy2NoOSg3Q9bnwlnmDm4HLnkl0RE3n+njDXR037aY1+x58Z/zFdwQ==
  dependencies:
    acorn "^8.9.0"
    acorn-jsx "^5.3.2"
    eslint-visitor-keys "^3.4.1"

esquery@^1.4.0:
  version "1.5.0"
  resolved "https://registry.npmjs.org/esquery/-/esquery-1.5.0.tgz"
  integrity sha512-YQLXUplAwJgCydQ78IMJywZCceoqk1oH01OERdSAJc/7U2AylwjhSCLDEtqwg811idIS/9fIU5GjG73IgjKMVg==
  dependencies:
    estraverse "^5.1.0"

esrecurse@^4.3.0:
  version "4.3.0"
  resolved "https://registry.npmjs.org/esrecurse/-/esrecurse-4.3.0.tgz"
  integrity sha512-KmfKL3b6G+RXvP8N1vr3Tq1kL/oCFgn2NYXEtqP8/L3pKapUA4G8cFVaoF3SU323CD4XypR/ffioHmkti6/Tag==
  dependencies:
    estraverse "^5.2.0"

estraverse@^4.1.1:
  version "4.3.0"
  resolved "https://registry.npmjs.org/estraverse/-/estraverse-4.3.0.tgz"
  integrity sha512-39nnKffWz8xN1BU/2c79n9nB9HDzo0niYUqx6xyqUnyoAnQyyWpOTdZEeiCch8BBu515t4wp9ZmgVfVhn9EBpw==

estraverse@^5.1.0, estraverse@^5.2.0, estraverse@^5.3.0:
  version "5.3.0"
  resolved "https://registry.npmjs.org/estraverse/-/estraverse-5.3.0.tgz"
  integrity sha512-MMdARuVEQziNTeJD8DgMqmhwR11BRQ/cBP+pLtYdSTnf3MIO8fFeiINEbX36ZdNlfU/7A9f3gUw49B3oQsvwBA==

esutils@^2.0.2:
  version "2.0.3"
  resolved "https://registry.npmjs.org/esutils/-/esutils-2.0.3.tgz"
  integrity sha512-kVscqXk4OCp68SZ0dkgEKVi6/8ij300KBWTJq32P/dYeWTSwK41WyTxalN1eRmA5Z9UU/LX9D7FWSmV9SAYx6g==

events@^3.2.0:
  version "3.3.0"
  resolved "https://registry.npmjs.org/events/-/events-3.3.0.tgz"
  integrity sha512-mQw+2fkQbALzQ7V0MY0IqdnXNOeTtP4r0lN9z7AAawCXgqea7bDii20AYrIBrFd/Hx0M2Ocz6S111CaFkUcb0Q==

fast-deep-equal@^3.1.1, fast-deep-equal@^3.1.3, fast-deep-equal@3.1.3:
  version "3.1.3"
  resolved "https://registry.npmjs.org/fast-deep-equal/-/fast-deep-equal-3.1.3.tgz"
  integrity sha512-f3qQ9oQy9j2AhBe/H9VC91wLmKBCCU/gDOnKNAYG5hswO7BLKj09Hc5HYNz9cGI++xlpDCIgDaitVs03ATR84Q==

fast-diff@^1.1.2:
  version "1.3.0"
  resolved "https://registry.npmjs.org/fast-diff/-/fast-diff-1.3.0.tgz"
  integrity sha512-VxPP4NqbUjj6MaAOafWeUn2cXWLcCtljklUtZf0Ind4XQ+QPtmA0b18zZy0jIQx+ExRVCR/ZQpBmik5lXshNsw==

fast-glob@^3.2.9:
  version "3.3.1"
  resolved "https://registry.npmjs.org/fast-glob/-/fast-glob-3.3.1.tgz"
  integrity sha512-kNFPyjhh5cKjrUltxs+wFx+ZkbRaxxmZ+X0ZU31SOsxCEtP9VPgtq2teZw1DebupL5GmDaNQ6yKMMVcM41iqDg==
  dependencies:
    "@nodelib/fs.stat" "^2.0.2"
    "@nodelib/fs.walk" "^1.2.3"
    glob-parent "^5.1.2"
    merge2 "^1.3.0"
    micromatch "^4.0.4"

fast-glob@^3.3.2:
  version "3.3.2"
  resolved "https://registry.npmjs.org/fast-glob/-/fast-glob-3.3.2.tgz"
  integrity sha512-oX2ruAFQwf/Orj8m737Y5adxDQO0LAB7/S5MnxCdTNDd4p6BsyIVsv9JQsATbTSq8KHRpLwIHbVlUNatxd+1Ow==
  dependencies:
    "@nodelib/fs.stat" "^2.0.2"
    "@nodelib/fs.walk" "^1.2.3"
    glob-parent "^5.1.2"
    merge2 "^1.3.0"
    micromatch "^4.0.4"

fast-json-stable-stringify@^2.0.0:
  version "2.1.0"
  resolved "https://registry.npmjs.org/fast-json-stable-stringify/-/fast-json-stable-stringify-2.1.0.tgz"
  integrity sha512-lhd/wF+Lk98HZoTCtlVraHtfh5XYijIjalXck7saUtuanSDyLMxnHhSXEDJqHxD7msR8D0uCmqlkwjCV8xvwHw==

fast-levenshtein@^2.0.6:
  version "2.0.6"
  resolved "https://registry.npmjs.org/fast-levenshtein/-/fast-levenshtein-2.0.6.tgz"
  integrity sha512-DCXu6Ifhqcks7TZKY3Hxp3y6qphY5SJZmrWMDrKcERSOXWQdMhU9Ig/PYrzyw/ul9jOIyh0N4M0tbC5hodg8dw==

fastq@^1.6.0:
  version "1.15.0"
  resolved "https://registry.npmjs.org/fastq/-/fastq-1.15.0.tgz"
  integrity sha512-wBrocU2LCXXa+lWBt8RoIRD89Fi8OdABODa/kEnyeyjS5aZO5/GNvI5sEINADqP/h8M29UHTHUb53sUu5Ihqdw==
  dependencies:
    reusify "^1.0.4"

fflate@^0.4.8:
  version "0.4.8"
  resolved "https://registry.npmjs.org/fflate/-/fflate-0.4.8.tgz"
  integrity sha512-FJqqoDBR00Mdj9ppamLa/Y7vxm+PRmNWA67N846RvsoYVMKB4q3y/de5PA7gUmRMYK/8CMz2GDZQmCRN1wBcWA==

file-entry-cache@^6.0.1:
  version "6.0.1"
  resolved "https://registry.npmjs.org/file-entry-cache/-/file-entry-cache-6.0.1.tgz"
  integrity sha512-7Gps/XWymbLk2QLYK4NzpMOrYjMhdIxXuIvy2QBsLE6ljuodKvdkWs/cpyJJ3CVIVpH0Oi1Hvg1ovbMzLdFBBg==
  dependencies:
    flat-cache "^3.0.4"

file-loader@*, file-loader@^6.2.0:
  version "6.2.0"
  resolved "https://registry.npmjs.org/file-loader/-/file-loader-6.2.0.tgz"
  integrity sha512-qo3glqyTa61Ytg4u73GultjHGjdRyig3tG6lPtyX/jOEJvHif9uB0/OCI2Kif6ctF3caQTW2G5gym21oAsI4pw==
  dependencies:
    loader-utils "^2.0.0"
    schema-utils "^3.0.0"

fill-range@^7.1.1:
  version "7.1.1"
  resolved "https://registry.npmjs.org/fill-range/-/fill-range-7.1.1.tgz"
  integrity sha512-YsGpe3WHLK8ZYi4tWDg2Jy3ebRz2rXowDxnld4bkQB00cc/1Zw9AWnC0i9ztDJitivtQvaI9KaLyKrc+hBW0yg==
  dependencies:
    to-regex-range "^5.0.1"

find-root@^1.1.0:
  version "1.1.0"
  resolved "https://registry.npmjs.org/find-root/-/find-root-1.1.0.tgz"
  integrity sha512-NKfW6bec6GfKc0SGx1e07QZY9PE99u0Bft/0rzSD5k3sO/vwkVUpDUKVm5Gpp5Ue3YfShPFTX2070tDs5kB9Ng==

flat-cache@^3.0.4:
  version "3.1.1"
  resolved "https://registry.npmjs.org/flat-cache/-/flat-cache-3.1.1.tgz"
  integrity sha512-/qM2b3LUIaIgviBQovTLvijfyOQXPtSRnRK26ksj2J7rzPIecePUIpJsZ4T02Qg+xiAEKIs5K8dsHEd+VaKa/Q==
  dependencies:
    flatted "^3.2.9"
    keyv "^4.5.3"
    rimraf "^3.0.2"

flat@^5.0.2:
  version "5.0.2"
  resolved "https://registry.npmjs.org/flat/-/flat-5.0.2.tgz"
  integrity sha512-b6suED+5/3rTpUBdG1gupIl8MPFCAMA0QXwmljLhvCUKcUvdE4gWky9zpuGCcXHOsz4J9wPGNWq6OKpmIzz3hQ==

flatted@^3.2.9:
  version "3.2.9"
  resolved "https://registry.npmjs.org/flatted/-/flatted-3.2.9.tgz"
  integrity sha512-36yxDn5H7OFZQla0/jFJmbIKTdZAQHngCedGxiMmpNfEZM0sdEeT+WczLQrjK6D7o2aiyLYDnkw0R3JK0Qv1RQ==

follow-redirects@^1.15.6:
  version "1.15.9"
  resolved "https://registry.npmjs.org/follow-redirects/-/follow-redirects-1.15.9.tgz"
  integrity sha512-gew4GsXizNgdoRyqmyfMHyAmXsZDk6mHkSxZFCzW9gwlbtOW44CDtYavM+y+72qD/Vq2l550kMF52DT8fOLJqQ==

for-each@^0.3.3:
  version "0.3.3"
  resolved "https://registry.npmjs.org/for-each/-/for-each-0.3.3.tgz"
  integrity sha512-jqYfLp7mo9vIyQf8ykW2v7A+2N4QjeCeI5+Dz9XraiO1ign81wjiH7Fb9vSOWvQfNtmSa4H2RoQTrrXivdUZmw==
  dependencies:
    is-callable "^1.1.3"

foreground-child@^3.1.0:
  version "3.3.0"
  resolved "https://registry.npmjs.org/foreground-child/-/foreground-child-3.3.0.tgz"
  integrity sha512-Ld2g8rrAyMYFXBhEqMz8ZAHBi4J4uS1i/CxGMDnjyFWddMXLVcDp051DZfu+t7+ab7Wv6SMqpWmyFIj5UbfFvg==
  dependencies:
    cross-spawn "^7.0.0"
    signal-exit "^4.0.1"

form-data@^4.0.0:
  version "4.0.1"
  resolved "https://registry.npmjs.org/form-data/-/form-data-4.0.1.tgz"
  integrity sha512-tzN8e4TX8+kkxGPK8D5u0FNmjPUjw3lwC9lSLxxoB/+GtsJG91CO8bSWy73APlgAZzZbXEYZJuxjkHH2w+Ezhw==
  dependencies:
    asynckit "^0.4.0"
    combined-stream "^1.0.8"
    mime-types "^2.1.12"

fraction.js@^4.3.6:
  version "4.3.7"
  resolved "https://registry.npmjs.org/fraction.js/-/fraction.js-4.3.7.tgz"
  integrity sha512-ZsDfxO51wGAXREY55a7la9LScWpwv9RxIrYABrlvOFBlH/ShPnrtsXeuUIfXKKOVicNxQ+o8JTbJvjS4M89yew==

framer-motion@^10.16.0, framer-motion@>=10.17.0:
  version "10.18.0"
  resolved "https://registry.npmjs.org/framer-motion/-/framer-motion-10.18.0.tgz"
  integrity sha512-oGlDh1Q1XqYPksuTD/usb0I70hq95OUzmL9+6Zd+Hs4XV0oaISBa/UUMSjYiq6m8EUF32132mOJ8xVZS+I0S6w==
  dependencies:
    tslib "^2.4.0"
  optionalDependencies:
    "@emotion/is-prop-valid" "^0.8.2"

fs-minipass@^2.0.0:
  version "2.1.0"
  resolved "https://registry.npmjs.org/fs-minipass/-/fs-minipass-2.1.0.tgz"
  integrity sha512-V/JgOLFCS+R6Vcq0slCuaeWEdNC3ouDlJMNIsacH2VtALiu9mV4LPrHc5cDl8k5aw6J8jwgWWpiTo5RYhmIzvg==
  dependencies:
    minipass "^3.0.0"

fs.realpath@^1.0.0:
  version "1.0.0"
  resolved "https://registry.npmjs.org/fs.realpath/-/fs.realpath-1.0.0.tgz"
  integrity sha512-OO0pH2lK6a0hZnAdau5ItzHPI6pUlvI7jMVnxUQRtw4owF2wk8lOSabtGDCTP4Ggrg2MbGnWO9X8K1t4+fGMDw==

function-bind@^1.1.1, function-bind@^1.1.2:
  version "1.1.2"
  resolved "https://registry.npmjs.org/function-bind/-/function-bind-1.1.2.tgz"
  integrity sha512-7XHNxH7qX9xG5mIwxkhumTox/MIRNcOgDrxWsMt2pAr23WHp6MrRlN7FBSFpCpr+oVO0F744iUgR82nJMfG2SA==

function.prototype.name@^1.1.5, function.prototype.name@^1.1.6:
  version "1.1.6"
  resolved "https://registry.npmjs.org/function.prototype.name/-/function.prototype.name-1.1.6.tgz"
  integrity sha512-Z5kx79swU5P27WEayXM1tBi5Ze/lbIyiNgU3qyXUOf9b2rgXYyF9Dy9Cx+IQv/Lc8WCG6L82zwUPpSS9hGehIg==
  dependencies:
    call-bind "^1.0.2"
    define-properties "^1.2.0"
    es-abstract "^1.22.1"
    functions-have-names "^1.2.3"

functional-red-black-tree@^1.0.1:
  version "1.0.1"
  resolved "https://registry.npmjs.org/functional-red-black-tree/-/functional-red-black-tree-1.0.1.tgz"
  integrity sha512-dsKNQNdj6xA3T+QlADDA7mOSlX0qiMINjn0cgr+eGHGsbSHzTabcIogz2+p/iqP1Xs6EP/sS2SbqH+brGTbq0g==

functions-have-names@^1.2.3:
  version "1.2.3"
  resolved "https://registry.npmjs.org/functions-have-names/-/functions-have-names-1.2.3.tgz"
  integrity sha512-xckBUXyTIqT97tq2x2AMb+g163b5JFysYk0x4qxNFwbfQkmNZoiRHb6sPzI9/QV33WeuvVYBUIiD4NzNIyqaRQ==

gauge@^3.0.0:
  version "3.0.2"
  resolved "https://registry.npmjs.org/gauge/-/gauge-3.0.2.tgz"
  integrity sha512-+5J6MS/5XksCuXq++uFRsnUd7Ovu1XenbeuIuNRJxYWjgQbPuFhT14lAvsWfqfAmnwluf1OwMjz39HjfLPci0Q==
  dependencies:
    aproba "^1.0.3 || ^2.0.0"
    color-support "^1.1.2"
    console-control-strings "^1.0.0"
    has-unicode "^2.0.1"
    object-assign "^4.1.1"
    signal-exit "^3.0.0"
    string-width "^4.2.3"
    strip-ansi "^6.0.1"
    wide-align "^1.1.2"

get-intrinsic@^1.0.2, get-intrinsic@^1.1.1, get-intrinsic@^1.1.3, get-intrinsic@^1.2.0, get-intrinsic@^1.2.1, get-intrinsic@^1.2.2:
  version "1.2.2"
  resolved "https://registry.npmjs.org/get-intrinsic/-/get-intrinsic-1.2.2.tgz"
  integrity sha512-0gSo4ml/0j98Y3lngkFEot/zhiCeWsbYIlZ+uZOVgzLyLaUw7wxUL+nCTP0XJvJg1AXulJRI3UJi8GsbDuxdGA==
  dependencies:
    function-bind "^1.1.2"
    has-proto "^1.0.1"
    has-symbols "^1.0.3"
    hasown "^2.0.0"

get-nonce@^1.0.0:
  version "1.0.1"
  resolved "https://registry.npmjs.org/get-nonce/-/get-nonce-1.0.1.tgz"
  integrity sha512-FJhYRoDaiatfEkUK8HKlicmu/3SGFD51q3itKDGoSTysQJBnfOcxU5GxnhE1E6soB76MbT0MBtnKJuXyAx+96Q==

get-symbol-description@^1.0.0:
  version "1.0.0"
  resolved "https://registry.npmjs.org/get-symbol-description/-/get-symbol-description-1.0.0.tgz"
  integrity sha512-2EmdH1YvIQiZpltCNgkuiUnyukzxM/R6NDJX31Ke3BG1Nq5b0S2PhX59UKi9vZpPDQVdqn+1IcaAwnzTT5vCjw==
  dependencies:
    call-bind "^1.0.2"
    get-intrinsic "^1.1.1"

glob-parent@^5.1.2, glob-parent@~5.1.2:
  version "5.1.2"
  resolved "https://registry.npmjs.org/glob-parent/-/glob-parent-5.1.2.tgz"
  integrity sha512-AOIgSQCepiJYwP3ARnGx+5VnTu2HBYdzbGP45eLw1vr3zB3vZLeyed1sC9hnbcOc9/SrMyM5RPQrkGz4aS9Zow==
  dependencies:
    is-glob "^4.0.1"

glob-parent@^6.0.1:
  version "6.0.2"
  resolved "https://registry.npmjs.org/glob-parent/-/glob-parent-6.0.2.tgz"
  integrity sha512-XxwI8EOhVQgWp6iDL+3b0r86f4d6AX6zSU55HfB4ydCEuXLXc5FcYeOu+nnGftS4TEju/11rt4KJPTMgbfmv4A==
  dependencies:
    is-glob "^4.0.3"

glob-parent@^6.0.2:
  version "6.0.2"
  resolved "https://registry.npmjs.org/glob-parent/-/glob-parent-6.0.2.tgz"
  integrity sha512-XxwI8EOhVQgWp6iDL+3b0r86f4d6AX6zSU55HfB4ydCEuXLXc5FcYeOu+nnGftS4TEju/11rt4KJPTMgbfmv4A==
  dependencies:
    is-glob "^4.0.3"

glob-to-regexp@^0.4.1:
  version "0.4.1"
  resolved "https://registry.npmjs.org/glob-to-regexp/-/glob-to-regexp-0.4.1.tgz"
  integrity sha512-lkX1HJXwyMcprw/5YUZc2s7DrpAiHB21/V+E1rHUrVNokkvB6bqMzT0VfV6/86ZNabt1k14YOIaT7nDvOX3Iiw==

glob@^10.3.10:
  version "10.4.5"
  resolved "https://registry.npmjs.org/glob/-/glob-10.4.5.tgz"
  integrity sha512-7Bv8RF0k6xjo7d4A/PxYLbUCfb6c+Vpd2/mB2yRDlew7Jb5hEXiCD9ibfO7wpk8i4sevK6DFny9h7EYbM3/sHg==
  dependencies:
    foreground-child "^3.1.0"
    jackspeak "^3.1.2"
    minimatch "^9.0.4"
    minipass "^7.1.2"
    package-json-from-dist "^1.0.0"
    path-scurry "^1.11.1"

glob@^7.1.3, glob@^7.2.0:
  version "7.2.3"
  resolved "https://registry.npmjs.org/glob/-/glob-7.2.3.tgz"
  integrity sha512-nFR0zLpU2YCaRxwoCJvL6UvCH2JFyFVIvwTLsIf21AuHlMskA1hhTdk+LlYJtOlYt9v6dvszD2BGRqBL+iQK9Q==
  dependencies:
    fs.realpath "^1.0.0"
    inflight "^1.0.4"
    inherits "2"
    minimatch "^3.1.1"
    once "^1.3.0"
    path-is-absolute "^1.0.0"

glob@7.1.7:
  version "7.1.7"
  resolved "https://registry.npmjs.org/glob/-/glob-7.1.7.tgz"
  integrity sha512-OvD9ENzPLbegENnYP5UUfJIirTg4+XwMWGaQfQTY0JenxNvvIKP3U3/tAQSPIu/lHxXYSZmpXlUHeqAIdKzBLQ==
  dependencies:
    fs.realpath "^1.0.0"
    inflight "^1.0.4"
    inherits "2"
    minimatch "^3.0.4"
    once "^1.3.0"
    path-is-absolute "^1.0.0"

globals@^13.15.0, globals@^13.19.0:
  version "13.23.0"
  resolved "https://registry.npmjs.org/globals/-/globals-13.23.0.tgz"
  integrity sha512-XAmF0RjlrjY23MA51q3HltdlGxUpXPvg0GioKiD9X6HD28iMjo2dKC8Vqwm7lne4GNr78+RHTfliktR6ZH09wA==
  dependencies:
    type-fest "^0.20.2"

globalthis@^1.0.3:
  version "1.0.3"
  resolved "https://registry.npmjs.org/globalthis/-/globalthis-1.0.3.tgz"
  integrity sha512-sFdI5LyBiNTHjRd7cGPWapiHWMOXKyuBNX/cWJ3NfzrZQVa8GI/8cofCl74AOVqq9W5kNmguTIzJ/1s2gyI9wA==
  dependencies:
    define-properties "^1.1.3"

globby@^11.1.0:
  version "11.1.0"
  resolved "https://registry.npmjs.org/globby/-/globby-11.1.0.tgz"
  integrity sha512-jhIXaOzy1sb8IyocaruWSn1TjmnBVs8Ayhcy83rmxNJ8q2uWKCAj3CnJY+KpGSXCueAPc0i05kVvVKtP1t9S3g==
  dependencies:
    array-union "^2.1.0"
    dir-glob "^3.0.1"
    fast-glob "^3.2.9"
    ignore "^5.2.0"
    merge2 "^1.4.1"
    slash "^3.0.0"

gopd@^1.0.1:
  version "1.0.1"
  resolved "https://registry.npmjs.org/gopd/-/gopd-1.0.1.tgz"
  integrity sha512-d65bNlIadxvpb/A2abVdlqKqV563juRnZ1Wtk6s1sIR8uNsXR70xqIzVqxVf1eTqDunwT2MkczEeaezCKTZhwA==
  dependencies:
    get-intrinsic "^1.1.3"

graceful-fs@^4.1.2, graceful-fs@^4.2.11, graceful-fs@^4.2.4:
  version "4.2.11"
  resolved "https://registry.npmjs.org/graceful-fs/-/graceful-fs-4.2.11.tgz"
  integrity sha512-RbJ5/jmFcNNCcDV5o9eTnBLJ/HszWV0P73bc+Ff4nS/rJj+YaS6IGyiOL0VoBYX+l1Wrl3k63h/KrH+nhJ0XvQ==

has-bigints@^1.0.1, has-bigints@^1.0.2:
  version "1.0.2"
  resolved "https://registry.npmjs.org/has-bigints/-/has-bigints-1.0.2.tgz"
  integrity sha512-tSvCKtBr9lkF0Ex0aQiP9N+OpV4zi2r/Nee5VkRDbaqv35RLYMzbwQfFSZZH0kR+Rd6302UJZ2p/bJCEoR3VoQ==

has-flag@^3.0.0:
  version "3.0.0"
  resolved "https://registry.npmjs.org/has-flag/-/has-flag-3.0.0.tgz"
  integrity sha512-sKJf1+ceQBr4SMkvQnBDNDtf4TXpVhVGateu0t918bl30FnbE2m4vNLX+VWe/dpjlb+HugGYzW7uQXH98HPEYw==

has-flag@^4.0.0:
  version "4.0.0"
  resolved "https://registry.npmjs.org/has-flag/-/has-flag-4.0.0.tgz"
  integrity sha512-EykJT/Q1KjTWctppgIAgfSO0tKVuZUjhgMr17kqTumMl6Afv3EISleU7qZUzoXDFTAHTDC4NOoG/ZxU3EvlMPQ==

has-property-descriptors@^1.0.0:
  version "1.0.1"
  resolved "https://registry.npmjs.org/has-property-descriptors/-/has-property-descriptors-1.0.1.tgz"
  integrity sha512-VsX8eaIewvas0xnvinAe9bw4WfIeODpGYikiWYLH+dma0Jw6KHYqWiWfhQlgOVK8D6PvjubK5Uc4P0iIhIcNVg==
  dependencies:
    get-intrinsic "^1.2.2"

has-proto@^1.0.1:
  version "1.0.1"
  resolved "https://registry.npmjs.org/has-proto/-/has-proto-1.0.1.tgz"
  integrity sha512-7qE+iP+O+bgF9clE5+UoBFzE65mlBiVj3tKCrlNQ0Ogwm0BjpT/gK4SlLYDMybDh5I3TCTKnPPa0oMG7JDYrhg==

has-symbols@^1.0.2, has-symbols@^1.0.3:
  version "1.0.3"
  resolved "https://registry.npmjs.org/has-symbols/-/has-symbols-1.0.3.tgz"
  integrity sha512-l3LCuF6MgDNwTDKkdYGEihYjt5pRPbEg46rtlmnSPlUbgmB8LOIrKJbYYFBSbnPaJexMKtiPO8hmeRjRz2Td+A==

has-tostringtag@^1.0.0:
  version "1.0.0"
  resolved "https://registry.npmjs.org/has-tostringtag/-/has-tostringtag-1.0.0.tgz"
  integrity sha512-kFjcSNhnlGV1kyoGk7OXKSawH5JOb/LzUc5w9B02hOTO0dfFRjbHQKvg1d6cf3HbeUmtU9VbbV3qzZ2Teh97WQ==
  dependencies:
    has-symbols "^1.0.2"

has-unicode@^2.0.1:
  version "2.0.1"
  resolved "https://registry.npmjs.org/has-unicode/-/has-unicode-2.0.1.tgz"
  integrity sha512-8Rf9Y83NBReMnx0gFzA8JImQACstCYWUplepDa9xprwwtmgEZUF0h/i5xSA625zB/I37EtrswSST6OXxwaaIJQ==

has@^1.0.3:
  version "1.0.4"
  resolved "https://registry.npmjs.org/has/-/has-1.0.4.tgz"
  integrity sha512-qdSAmqLF6209RFj4VVItywPMbm3vWylknmB3nvNiUIs72xAimcM8nVYxYr7ncvZq5qzk9MKIZR8ijqD/1QuYjQ==

hasown@^2.0.0:
  version "2.0.0"
  resolved "https://registry.npmjs.org/hasown/-/hasown-2.0.0.tgz"
  integrity sha512-vUptKVTpIJhcczKBbgnS+RtcuYMB8+oNzPK2/Hp3hanz8JmpATdmmgLgSaadVREkDm+e2giHwY3ZRkyjSIDDFA==
  dependencies:
    function-bind "^1.1.2"

hoist-non-react-statics@^3.3.1, hoist-non-react-statics@^3.3.2:
  version "3.3.2"
  resolved "https://registry.npmjs.org/hoist-non-react-statics/-/hoist-non-react-statics-3.3.2.tgz"
  integrity sha512-/gGivxi8JPKWNm/W0jSmzcMPpfpPLc3dY/6GxhX2hQ9iGj3aDfklV4ET7NjKpSinLpJ5vafa9iiGIEZg10SfBw==
  dependencies:
    react-is "^16.7.0"

html-tokenize@^2.0.0:
  version "2.0.1"
  resolved "https://registry.npmjs.org/html-tokenize/-/html-tokenize-2.0.1.tgz"
  integrity sha512-QY6S+hZ0f5m1WT8WffYN+Hg+xm/w5I8XeUcAq/ZYP5wVC8xbKi4Whhru3FtrAebD5EhBW8rmFzkDI6eCAuFe2w==
  dependencies:
    buffer-from "~0.1.1"
    inherits "~2.0.1"
    minimist "~1.2.5"
    readable-stream "~1.0.27-1"
    through2 "~0.4.1"

html2canvas@^1.0.0-rc.5:
  version "1.4.1"
  resolved "https://registry.npmjs.org/html2canvas/-/html2canvas-1.4.1.tgz"
  integrity sha512-fPU6BHNpsyIhr8yyMpTLLxAbkaK8ArIBcmZIRiBLiDhjeqvXolaEmDGmELFuX9I4xDcaKKcJl+TKZLqruBbmWA==
  dependencies:
    css-line-break "^2.1.0"
    text-segmentation "^1.0.3"

https-proxy-agent@^5.0.0:
  version "5.0.1"
  resolved "https://registry.npmjs.org/https-proxy-agent/-/https-proxy-agent-5.0.1.tgz"
  integrity sha512-dFcAjpTQFgoLMzC2VwU+C/CbS7uRL0lWmxDITmqm7C+7F0Odmj6s9l6alZc6AELXhrnggM2CeWSXHGOdX2YtwA==
  dependencies:
    agent-base "6"
    debug "4"

hyphenate-style-name@^1.0.3:
  version "1.0.4"
  resolved "https://registry.npmjs.org/hyphenate-style-name/-/hyphenate-style-name-1.0.4.tgz"
  integrity sha512-ygGZLjmXfPHj+ZWh6LwbC37l43MhfztxetbFCoYTM2VjkIUpeHgSNn7QIyVFj7YQ1Wl9Cbw5sholVJPzWvC2MQ==

ignore@^5.2.0:
  version "5.2.4"
  resolved "https://registry.npmjs.org/ignore/-/ignore-5.2.4.tgz"
  integrity sha512-MAb38BcSbH0eHNBxn7ql2NH/kX33OkB3lZ1BNdh7ENeRChHTYsTvWrMubiIAMNS2llXEEgZ1MUOBtXChP3kaFQ==

immer@^9.0.1, immer@^9.0.6:
  version "9.0.21"
  resolved "https://registry.npmjs.org/immer/-/immer-9.0.21.tgz"
  integrity sha512-bc4NBHqOqSfRW7POMkHd51LvClaeMXpm8dx0e8oE2GORbq5aRK7Bxl4FyzVLdGtLmvLKL7BTDBG5ACQm4HWjTA==

import-fresh@^3.0.0, import-fresh@^3.2.1:
  version "3.3.0"
  resolved "https://registry.npmjs.org/import-fresh/-/import-fresh-3.3.0.tgz"
  integrity sha512-veYYhQa+D1QBKznvhUHxb8faxlrwUnxseDAbAp457E0wLNio2bOSKnjYDhMj+YiAq61xrMGhQk9iXVk5FzgQMw==
  dependencies:
    parent-module "^1.0.0"
    resolve-from "^4.0.0"

imurmurhash@^0.1.4:
  version "0.1.4"
  resolved "https://registry.npmjs.org/imurmurhash/-/imurmurhash-0.1.4.tgz"
  integrity sha512-JmXMZ6wuvDmLiHEml9ykzqO6lwFbof0GG4IkcGaENdCRDDmMVnny7s5HsIgHCbaq0w2MyPhDqkhTUgS2LU2PHA==

inflight@^1.0.4:
  version "1.0.6"
  resolved "https://registry.npmjs.org/inflight/-/inflight-1.0.6.tgz"
  integrity sha512-k92I/b08q4wvFscXCLvqfsHCrjrF7yiXsQuIVvVE7N82W3+aqpzuUdBbfhWcy/FZR3/4IgflMgKLOsvPDrGCJA==
  dependencies:
    once "^1.3.0"
    wrappy "1"

inherits@^2.0.3, inherits@~2.0.1, inherits@~2.0.3, inherits@2:
  version "2.0.4"
  resolved "https://registry.npmjs.org/inherits/-/inherits-2.0.4.tgz"
  integrity sha512-k/vGaX4/Yla3WzyMCvTQOXYeIHvqOKtnqBduzTHpzpQZzAskKMhZ2K+EnBiSM9zGSoIFeMpXKxa4dYeZIQqewQ==

internal-slot@^1.0.5:
  version "1.0.6"
  resolved "https://registry.npmjs.org/internal-slot/-/internal-slot-1.0.6.tgz"
  integrity sha512-Xj6dv+PsbtwyPpEflsejS+oIZxmMlV44zAhG479uYu89MsjcYOhCFnNyKrkJrihbsiasQyY0afoCl/9BLR65bg==
  dependencies:
    get-intrinsic "^1.2.2"
    hasown "^2.0.0"
    side-channel "^1.0.4"

intl-messageformat@^10.1.0:
  version "10.7.7"
  resolved "https://registry.npmjs.org/intl-messageformat/-/intl-messageformat-10.7.7.tgz"
  integrity sha512-F134jIoeYMro/3I0h08D0Yt4N9o9pjddU/4IIxMMURqbAtI2wu70X8hvG1V48W49zXHXv3RKSF/po+0fDfsGjA==
  dependencies:
    "@formatjs/ecma402-abstract" "2.2.4"
    "@formatjs/fast-memoize" "2.2.3"
    "@formatjs/icu-messageformat-parser" "2.9.4"
    tslib "2"

invariant@^2.2.4:
  version "2.2.4"
  resolved "https://registry.npmjs.org/invariant/-/invariant-2.2.4.tgz"
  integrity sha512-phJfQVBuaJM5raOpJjSfkiD6BpbCE4Ns//LaXl6wGYtUBY83nWS6Rf9tXm2e8VaK60JEjYldbPif/A2B1C2gNA==
  dependencies:
    loose-envify "^1.0.0"

is-array-buffer@^3.0.1, is-array-buffer@^3.0.2:
  version "3.0.2"
  resolved "https://registry.npmjs.org/is-array-buffer/-/is-array-buffer-3.0.2.tgz"
  integrity sha512-y+FyyR/w8vfIRq4eQcM1EYgSTnmHXPqaF+IgzgraytCFq5Xh8lllDVmAZolPJiZttZLeFSINPYMaEJ7/vWUa1w==
  dependencies:
    call-bind "^1.0.2"
    get-intrinsic "^1.2.0"
    is-typed-array "^1.1.10"

is-arrayish@^0.2.1:
  version "0.2.1"
  resolved "https://registry.npmjs.org/is-arrayish/-/is-arrayish-0.2.1.tgz"
  integrity sha512-zz06S8t0ozoDXMG+ube26zeCTNXcKIPJZJi8hBrF4idCLms4CG9QtK7qBl1boi5ODzFpjswb5JPmHCbMpjaYzg==

is-arrayish@^0.3.1:
  version "0.3.2"
  resolved "https://registry.npmjs.org/is-arrayish/-/is-arrayish-0.3.2.tgz"
  integrity sha512-eVRqCvVlZbuw3GrM63ovNSNAeA1K16kaR/LRY/92w0zxQ5/1YzwblUX652i4Xs9RwAGjW9d9y6X88t8OaAJfWQ==

is-async-function@^2.0.0:
  version "2.0.0"
  resolved "https://registry.npmjs.org/is-async-function/-/is-async-function-2.0.0.tgz"
  integrity sha512-Y1JXKrfykRJGdlDwdKlLpLyMIiWqWvuSd17TvZk68PLAOGOoF4Xyav1z0Xhoi+gCYjZVeC5SI+hYFOfvXmGRCA==
  dependencies:
    has-tostringtag "^1.0.0"

is-bigint@^1.0.1:
  version "1.0.4"
  resolved "https://registry.npmjs.org/is-bigint/-/is-bigint-1.0.4.tgz"
  integrity sha512-zB9CruMamjym81i2JZ3UMn54PKGsQzsJeo6xvN3HJJ4CAsQNB6iRutp2To77OfCNuoxspsIhzaPoO1zyCEhFOg==
  dependencies:
    has-bigints "^1.0.1"

is-binary-path@~2.1.0:
  version "2.1.0"
  resolved "https://registry.npmjs.org/is-binary-path/-/is-binary-path-2.1.0.tgz"
  integrity sha512-ZMERYes6pDydyuGidse7OsHxtbI7WVeUEozgR/g7rd0xUimYNlvZRE/K2MgZTjWy725IfelLeVcEM97mmtRGXw==
  dependencies:
    binary-extensions "^2.0.0"

is-boolean-object@^1.1.0:
  version "1.1.2"
  resolved "https://registry.npmjs.org/is-boolean-object/-/is-boolean-object-1.1.2.tgz"
  integrity sha512-gDYaKHJmnj4aWxyj6YHyXVpdQawtVLHU5cb+eztPGczf6cjuTdwve5ZIEfgXqH4e57An1D1AKf8CZ3kYrQRqYA==
  dependencies:
    call-bind "^1.0.2"
    has-tostringtag "^1.0.0"

is-callable@^1.1.3, is-callable@^1.1.4, is-callable@^1.2.7:
  version "1.2.7"
  resolved "https://registry.npmjs.org/is-callable/-/is-callable-1.2.7.tgz"
  integrity sha512-1BC0BVFhS/p0qtw6enp8e+8OD0UrK0oFLztSjNzhcKA3WDuJxxAPXzPuPtKkjEY9UUoEWlX/8fgKeu2S8i9JTA==

is-core-module@^2.13.0, is-core-module@^2.13.1:
  version "2.13.1"
  resolved "https://registry.npmjs.org/is-core-module/-/is-core-module-2.13.1.tgz"
  integrity sha512-hHrIjvZsftOsvKSn2TRYl63zvxsgE0K+0mYMoH6gD4omR5IWB2KynivBQczo3+wF1cCkjzvptnI9Q0sPU66ilw==
  dependencies:
    hasown "^2.0.0"

is-date-object@^1.0.1, is-date-object@^1.0.5:
  version "1.0.5"
  resolved "https://registry.npmjs.org/is-date-object/-/is-date-object-1.0.5.tgz"
  integrity sha512-9YQaSxsAiSwcvS33MBk3wTCVnWK+HhF8VZR2jRxehM16QcVOdHqPn4VPHmRK4lSr38n9JriurInLcP90xsYNfQ==
  dependencies:
    has-tostringtag "^1.0.0"

is-extglob@^2.1.1:
  version "2.1.1"
  resolved "https://registry.npmjs.org/is-extglob/-/is-extglob-2.1.1.tgz"
  integrity sha512-SbKbANkN603Vi4jEZv49LeVJMn4yGwsbzZworEoyEiutsN3nJYdbO36zfhGJ6QEDpOZIFkDtnq5JRxmvl3jsoQ==

is-finalizationregistry@^1.0.2:
  version "1.0.2"
  resolved "https://registry.npmjs.org/is-finalizationregistry/-/is-finalizationregistry-1.0.2.tgz"
  integrity sha512-0by5vtUJs8iFQb5TYUHHPudOR+qXYIMKtiUzvLIZITZUjknFmziyBJuLhVRc+Ds0dREFlskDNJKYIdIzu/9pfw==
  dependencies:
    call-bind "^1.0.2"

is-fullwidth-code-point@^3.0.0:
  version "3.0.0"
  resolved "https://registry.npmjs.org/is-fullwidth-code-point/-/is-fullwidth-code-point-3.0.0.tgz"
  integrity sha512-zymm5+u+sCsSWyD9qNaejV3DFvhCKclKdizYaJUuHA83RLjb7nSuGnddCHGv0hk+KY7BMAlsWeK4Ueg6EV6XQg==

is-generator-function@^1.0.10:
  version "1.0.10"
  resolved "https://registry.npmjs.org/is-generator-function/-/is-generator-function-1.0.10.tgz"
  integrity sha512-jsEjy9l3yiXEQ+PsXdmBwEPcOxaXWLspKdplFUVI9vq1iZgIekeC0L167qeu86czQaxed3q/Uzuw0swL0irL8A==
  dependencies:
    has-tostringtag "^1.0.0"

is-glob@^4.0.0, is-glob@^4.0.1, is-glob@^4.0.3, is-glob@~4.0.1:
  version "4.0.3"
  resolved "https://registry.npmjs.org/is-glob/-/is-glob-4.0.3.tgz"
  integrity sha512-xelSayHH36ZgE7ZWhli7pW34hNbNl8Ojv5KVmkJD4hBdD3th8Tfk9vYasLM+mXWOZhFkgZfxhLSnrwRr4elSSg==
  dependencies:
    is-extglob "^2.1.1"

is-hotkey@^0.1.6:
  version "0.1.8"
  resolved "https://registry.npmjs.org/is-hotkey/-/is-hotkey-0.1.8.tgz"
  integrity sha512-qs3NZ1INIS+H+yeo7cD9pDfwYV/jqRh1JG9S9zYrNudkoUQg7OL7ziXqRKu+InFjUIDoP2o6HIkLYMh1pcWgyQ==

is-in-browser@^1.0.2, is-in-browser@^1.1.3:
  version "1.1.3"
  resolved "https://registry.npmjs.org/is-in-browser/-/is-in-browser-1.1.3.tgz"
  integrity sha512-FeXIBgG/CPGd/WUxuEyvgGTEfwiG9Z4EKGxjNMRqviiIIfsmgrpnHLffEDdwUHqNva1VEW91o3xBT/m8Elgl9g==

is-map@^2.0.1:
  version "2.0.2"
  resolved "https://registry.npmjs.org/is-map/-/is-map-2.0.2.tgz"
  integrity sha512-cOZFQQozTha1f4MxLFzlgKYPTyj26picdZTx82hbc/Xf4K/tZOOXSCkMvU4pKioRXGDLJRn0GM7Upe7kR721yg==

is-negative-zero@^2.0.2:
  version "2.0.2"
  resolved "https://registry.npmjs.org/is-negative-zero/-/is-negative-zero-2.0.2.tgz"
  integrity sha512-dqJvarLawXsFbNDeJW7zAz8ItJ9cd28YufuuFzh0G8pNHjJMnY08Dv7sYX2uF5UpQOwieAeOExEYAWWfu7ZZUA==

is-number-object@^1.0.4:
  version "1.0.7"
  resolved "https://registry.npmjs.org/is-number-object/-/is-number-object-1.0.7.tgz"
  integrity sha512-k1U0IRzLMo7ZlYIfzRu23Oh6MiIFasgpb9X76eqfFZAqwH44UI4KTBvBYIZ1dSL9ZzChTB9ShHfLkR4pdW5krQ==
  dependencies:
    has-tostringtag "^1.0.0"

is-number@^7.0.0:
  version "7.0.0"
  resolved "https://registry.npmjs.org/is-number/-/is-number-7.0.0.tgz"
  integrity sha512-41Cifkg6e8TylSpdtTpeLVMqvSBEVzTttHvERD741+pnZ8ANv0004MRL43QKPDlK9cGvNp6NZWZUBlbGXYxxng==

is-plain-object@^5.0.0:
  version "5.0.0"
  resolved "https://registry.npmjs.org/is-plain-object/-/is-plain-object-5.0.0.tgz"
  integrity sha512-VRSzKkbMm5jMDoKLbltAkFQ5Qr7VDiTFGXxYFXXowVj387GeGNOCsOH6Msy00SGZ3Fp84b1Naa1psqgcCIEP5Q==

is-regex@^1.1.4:
  version "1.1.4"
  resolved "https://registry.npmjs.org/is-regex/-/is-regex-1.1.4.tgz"
  integrity sha512-kvRdxDsxZjhzUX07ZnLydzS1TU/TJlTUHHY4YLL87e37oUA49DfkLqgy+VjFocowy29cKvcSiu+kIv728jTTVg==
  dependencies:
    call-bind "^1.0.2"
    has-tostringtag "^1.0.0"

is-set@^2.0.1:
  version "2.0.2"
  resolved "https://registry.npmjs.org/is-set/-/is-set-2.0.2.tgz"
  integrity sha512-+2cnTEZeY5z/iXGbLhPrOAaK/Mau5k5eXq9j14CpRTftq0pAJu2MwVRSZhyZWBzx3o6X795Lz6Bpb6R0GKf37g==

is-shared-array-buffer@^1.0.2:
  version "1.0.2"
  resolved "https://registry.npmjs.org/is-shared-array-buffer/-/is-shared-array-buffer-1.0.2.tgz"
  integrity sha512-sqN2UDu1/0y6uvXyStCOzyhAjCSlHceFoMKJW8W9EU9cvic/QdsZ0kEU93HEy3IUEFZIiH/3w+AH/UQbPHNdhA==
  dependencies:
    call-bind "^1.0.2"

is-string@^1.0.5, is-string@^1.0.7:
  version "1.0.7"
  resolved "https://registry.npmjs.org/is-string/-/is-string-1.0.7.tgz"
  integrity sha512-tE2UXzivje6ofPW7l23cjDOMa09gb7xlAqG6jG5ej6uPV32TlWP3NKPigtaGeHNu9fohccRYvIiZMfOOnOYUtg==
  dependencies:
    has-tostringtag "^1.0.0"

is-symbol@^1.0.2, is-symbol@^1.0.3:
  version "1.0.4"
  resolved "https://registry.npmjs.org/is-symbol/-/is-symbol-1.0.4.tgz"
  integrity sha512-C/CPBqKWnvdcxqIARxyOh4v1UUEOCHpgDa0WYgpKDFMszcrPcffg5uhwSgPCLD2WWxmq6isisz87tzT01tuGhg==
  dependencies:
    has-symbols "^1.0.2"

is-typed-array@^1.1.10, is-typed-array@^1.1.12, is-typed-array@^1.1.9:
  version "1.1.12"
  resolved "https://registry.npmjs.org/is-typed-array/-/is-typed-array-1.1.12.tgz"
  integrity sha512-Z14TF2JNG8Lss5/HMqt0//T9JeHXttXy5pH/DBU4vi98ozO2btxzq9MwYDZYnKwU8nRsz/+GVFVRDq3DkVuSPg==
  dependencies:
    which-typed-array "^1.1.11"

is-weakmap@^2.0.1:
  version "2.0.1"
  resolved "https://registry.npmjs.org/is-weakmap/-/is-weakmap-2.0.1.tgz"
  integrity sha512-NSBR4kH5oVj1Uwvv970ruUkCV7O1mzgVFO4/rev2cLRda9Tm9HrL70ZPut4rOHgY0FNrUu9BCbXA2sdQ+x0chA==

is-weakref@^1.0.2:
  version "1.0.2"
  resolved "https://registry.npmjs.org/is-weakref/-/is-weakref-1.0.2.tgz"
  integrity sha512-qctsuLZmIQ0+vSSMfoVvyFe2+GSEvnmZ2ezTup1SBse9+twCCeial6EEi3Nc2KFcf6+qz2FBPnjXsk8xhKSaPQ==
  dependencies:
    call-bind "^1.0.2"

is-weakset@^2.0.1:
  version "2.0.2"
  resolved "https://registry.npmjs.org/is-weakset/-/is-weakset-2.0.2.tgz"
  integrity sha512-t2yVvttHkQktwnNNmBQ98AhENLdPUTDTE21uPqAQ0ARwQfGeQKRVS0NNurH7bTf7RrvcVn1OOge45CnBeHCSmg==
  dependencies:
    call-bind "^1.0.2"
    get-intrinsic "^1.1.1"

isarray@^2.0.5:
  version "2.0.5"
  resolved "https://registry.npmjs.org/isarray/-/isarray-2.0.5.tgz"
  integrity sha512-xHjhDr3cNBK0BzdUJSPXZntQUx/mwMS5Rw4A7lPJ90XGAO6ISP/ePDNuo0vhqOZU+UD5JoodwCAAoZQd3FeAKw==

isarray@~1.0.0:
  version "1.0.0"
  resolved "https://registry.npmjs.org/isarray/-/isarray-1.0.0.tgz"
  integrity sha512-VLghIWNM6ELQzo7zwmcg0NmTVyWKYjvIeM83yjp0wRDTmUnrM678fQbcKBo6n2CJEF0szoG//ytg+TKla89ALQ==

isarray@0.0.1:
  version "0.0.1"
  resolved "https://registry.npmjs.org/isarray/-/isarray-0.0.1.tgz"
  integrity sha512-D2S+3GLxWH+uhrNEcoh/fnmYeP8E8/zHl644d/jdA0g2uyXvy3sb0qxotE+ne0LtccHknQzWwZEzhak7oJ0COQ==

isexe@^2.0.0:
  version "2.0.0"
  resolved "https://registry.npmjs.org/isexe/-/isexe-2.0.0.tgz"
  integrity sha512-RHxMLp9lnKHGHRng9QFhRCMbYAcVpn69smSGcq3f36xjgVVWThj4qqLbTLlq7Ssj8B+fIQ1EuCEGI2lKsyQeIw==

iterator.prototype@^1.1.2:
  version "1.1.2"
  resolved "https://registry.npmjs.org/iterator.prototype/-/iterator.prototype-1.1.2.tgz"
  integrity sha512-DR33HMMr8EzwuRL8Y9D3u2BMj8+RqSE850jfGu59kS7tbmPLzGkZmVSfyCFSDxuZiEY6Rzt3T2NA/qU+NwVj1w==
  dependencies:
    define-properties "^1.2.1"
    get-intrinsic "^1.2.1"
    has-symbols "^1.0.3"
    reflect.getprototypeof "^1.0.4"
    set-function-name "^2.0.1"

jackspeak@^3.1.2:
  version "3.4.3"
  resolved "https://registry.npmjs.org/jackspeak/-/jackspeak-3.4.3.tgz"
  integrity sha512-OGlZQpz2yfahA/Rd1Y8Cd9SIEsqvXkLVoSw/cgwhnhFMDbsQFeZYoJJ7bIZBS9BcamUW96asq/npPWugM+RQBw==
  dependencies:
    "@isaacs/cliui" "^8.0.2"
  optionalDependencies:
    "@pkgjs/parseargs" "^0.11.0"

jest-worker@^27.4.5:
  version "27.5.1"
  resolved "https://registry.npmjs.org/jest-worker/-/jest-worker-27.5.1.tgz"
  integrity sha512-7vuh85V5cdDofPyxn58nrPjBktZo0u9x1g8WtjQol+jZDaE+fhN+cIvTj11GndBnMnyfrUOG1sZQxCdjKh+DKg==
  dependencies:
    "@types/node" "*"
    merge-stream "^2.0.0"
    supports-color "^8.0.0"

jiti@^1.21.6:
  version "1.21.6"
  resolved "https://registry.npmjs.org/jiti/-/jiti-1.21.6.tgz"
  integrity sha512-2yTgeWTWzMWkHu6Jp9NKgePDaYHbntiwvYuuJLbbN9vl7DC9DvXKOB2BC3ZZ92D3cvV/aflH0osDfwpHepQ53w==

jquery@>=1.8.0:
  version "3.7.1"
  resolved "https://registry.npmjs.org/jquery/-/jquery-3.7.1.tgz"
  integrity sha512-m4avr8yL8kmFN8psrbFFFmB/If14iN5o9nw/NgnnM+kybDJpRsAynV2BsfpTYrTRysYUdADVD7CkUUizgkpLfg==

"js-tokens@^3.0.0 || ^4.0.0", js-tokens@^4.0.0:
  version "4.0.0"
  resolved "https://registry.npmjs.org/js-tokens/-/js-tokens-4.0.0.tgz"
  integrity sha512-RdJUflcE3cUzKiMqQgsCu06FPu9UdIJO0beYbPhHN4k6apgJtifcoCtT9bcxOpYBtpD2kCM6Sbzg4CausW/PKQ==

js-yaml@^4.1.0:
  version "4.1.0"
  resolved "https://registry.npmjs.org/js-yaml/-/js-yaml-4.1.0.tgz"
  integrity sha512-wpxZs9NoxZaJESJGIZTyDEaYpl0FKSA+FB9aJiyemKhMwkxQg63h4T1KJgUGHpTqPDNRcmmYLugrRjJlBtWvRA==
  dependencies:
    argparse "^2.0.1"

json-buffer@3.0.1:
  version "3.0.1"
  resolved "https://registry.npmjs.org/json-buffer/-/json-buffer-3.0.1.tgz"
  integrity sha512-4bV5BfR2mqfQTJm+V5tPPdf+ZpuhiIvTuAB5g8kcrXOZpTT/QwwVRWBywX1ozr6lEuPdbHxwaJlm9G6mI2sfSQ==

json-parse-even-better-errors@^2.3.0, json-parse-even-better-errors@^2.3.1:
  version "2.3.1"
  resolved "https://registry.npmjs.org/json-parse-even-better-errors/-/json-parse-even-better-errors-2.3.1.tgz"
  integrity sha512-xyFwyhro/JEof6Ghe2iz2NcXoj2sloNsWr/XsERDK/oiPCfaNhl5ONfp+jQdAZRQQ0IJWNzH9zIZF7li91kh2w==

json-schema-traverse@^0.4.1:
  version "0.4.1"
  resolved "https://registry.npmjs.org/json-schema-traverse/-/json-schema-traverse-0.4.1.tgz"
  integrity sha512-xbbCH5dCYU5T8LcEhhuh7HJ88HXuW3qsI3Y0zOZFKfZEHcpWiHU/Jxzk629Brsab/mMiHQti9wMP+845RPe3Vg==

json-stable-stringify-without-jsonify@^1.0.1:
  version "1.0.1"
  resolved "https://registry.npmjs.org/json-stable-stringify-without-jsonify/-/json-stable-stringify-without-jsonify-1.0.1.tgz"
  integrity sha512-Bdboy+l7tA3OGW6FjyFHWkP5LuByj1Tk33Ljyq0axyzdk9//JSi2u3fP1QSmd1KNwq6VOKYGlAu87CisVir6Pw==

json2mq@^0.2.0:
  version "0.2.0"
  resolved "https://registry.npmjs.org/json2mq/-/json2mq-0.2.0.tgz"
  integrity sha512-SzoRg7ux5DWTII9J2qkrZrqV1gt+rTaoufMxEzXbS26Uid0NwaJd123HcoB80TgubEppxxIGdNxCx50fEoEWQA==
  dependencies:
    string-convert "^0.2.0"

json5@^1.0.2:
  version "1.0.2"
  resolved "https://registry.npmjs.org/json5/-/json5-1.0.2.tgz"
  integrity sha512-g1MWMLBiz8FKi1e4w0UyVL3w+iJceWAFBAaBnnGKOpNa5f8TLktkbre1+s6oICydWAm+HRUGTmI+//xv2hvXYA==
  dependencies:
    minimist "^1.2.0"

json5@^2.1.2:
  version "2.2.3"
  resolved "https://registry.npmjs.org/json5/-/json5-2.2.3.tgz"
  integrity sha512-XmOWe7eyHYH14cLdVPoyg+GOH3rYX++KpzrylJwSW98t3Nk+U8XOl8FWKOgwtzdb8lXGf6zYwDUzeHMWfxasyg==

jsonwebtoken@^9.0.1:
  version "9.0.2"
  resolved "https://registry.npmjs.org/jsonwebtoken/-/jsonwebtoken-9.0.2.tgz"
  integrity sha512-PRp66vJ865SSqOlgqS8hujT5U4AOgMfhrwYIuIhfKaoSCZcirrmASQr8CX7cUg+RMih+hgznrjp99o+W4pJLHQ==
  dependencies:
    jws "^3.2.2"
    lodash.includes "^4.3.0"
    lodash.isboolean "^3.0.3"
    lodash.isinteger "^4.0.4"
    lodash.isnumber "^3.0.3"
    lodash.isplainobject "^4.0.6"
    lodash.isstring "^4.0.1"
    lodash.once "^4.0.0"
    ms "^2.1.1"
    semver "^7.5.4"

jspdf-autotable@^3.7.1:
  version "3.7.1"
  resolved "https://registry.npmjs.org/jspdf-autotable/-/jspdf-autotable-3.7.1.tgz"
  integrity sha512-5fgjqE8nIwUoNz5l/i/aD/uONKofE4yp/kJ097EKBllPVTPGnGV5OWHld30db3+CvNrgzrRl8gmTnKF6vag05g==

jspdf@^2.5.1:
  version "2.5.1"
  resolved "https://registry.npmjs.org/jspdf/-/jspdf-2.5.1.tgz"
  integrity sha512-hXObxz7ZqoyhxET78+XR34Xu2qFGrJJ2I2bE5w4SM8eFaFEkW2xcGRVUss360fYelwRSid/jT078kbNvmoW0QA==
  dependencies:
    "@babel/runtime" "^7.14.0"
    atob "^2.1.2"
    btoa "^1.2.1"
    fflate "^0.4.8"
  optionalDependencies:
    canvg "^3.0.6"
    core-js "^3.6.0"
    dompurify "^2.2.0"
    html2canvas "^1.0.0-rc.5"

jss-plugin-camel-case@^10.8.2:
  version "10.10.0"
  resolved "https://registry.npmjs.org/jss-plugin-camel-case/-/jss-plugin-camel-case-10.10.0.tgz"
  integrity sha512-z+HETfj5IYgFxh1wJnUAU8jByI48ED+v0fuTuhKrPR+pRBYS2EDwbusU8aFOpCdYhtRc9zhN+PJ7iNE8pAWyPw==
  dependencies:
    "@babel/runtime" "^7.3.1"
    hyphenate-style-name "^1.0.3"
    jss "10.10.0"

jss-plugin-default-unit@^10.8.2:
  version "10.10.0"
  resolved "https://registry.npmjs.org/jss-plugin-default-unit/-/jss-plugin-default-unit-10.10.0.tgz"
  integrity sha512-SvpajxIECi4JDUbGLefvNckmI+c2VWmP43qnEy/0eiwzRUsafg5DVSIWSzZe4d2vFX1u9nRDP46WCFV/PXVBGQ==
  dependencies:
    "@babel/runtime" "^7.3.1"
    jss "10.10.0"

jss-plugin-global@^10.8.2:
  version "10.10.0"
  resolved "https://registry.npmjs.org/jss-plugin-global/-/jss-plugin-global-10.10.0.tgz"
  integrity sha512-icXEYbMufiNuWfuazLeN+BNJO16Ge88OcXU5ZDC2vLqElmMybA31Wi7lZ3lf+vgufRocvPj8443irhYRgWxP+A==
  dependencies:
    "@babel/runtime" "^7.3.1"
    jss "10.10.0"

jss-plugin-nested@^10.8.2:
  version "10.10.0"
  resolved "https://registry.npmjs.org/jss-plugin-nested/-/jss-plugin-nested-10.10.0.tgz"
  integrity sha512-9R4JHxxGgiZhurDo3q7LdIiDEgtA1bTGzAbhSPyIOWb7ZubrjQe8acwhEQ6OEKydzpl8XHMtTnEwHXCARLYqYA==
  dependencies:
    "@babel/runtime" "^7.3.1"
    jss "10.10.0"
    tiny-warning "^1.0.2"

jss-plugin-props-sort@^10.8.2:
  version "10.10.0"
  resolved "https://registry.npmjs.org/jss-plugin-props-sort/-/jss-plugin-props-sort-10.10.0.tgz"
  integrity sha512-5VNJvQJbnq/vRfje6uZLe/FyaOpzP/IH1LP+0fr88QamVrGJa0hpRRyAa0ea4U/3LcorJfBFVyC4yN2QC73lJg==
  dependencies:
    "@babel/runtime" "^7.3.1"
    jss "10.10.0"

jss-plugin-rule-value-function@^10.8.2:
  version "10.10.0"
  resolved "https://registry.npmjs.org/jss-plugin-rule-value-function/-/jss-plugin-rule-value-function-10.10.0.tgz"
  integrity sha512-uEFJFgaCtkXeIPgki8ICw3Y7VMkL9GEan6SqmT9tqpwM+/t+hxfMUdU4wQ0MtOiMNWhwnckBV0IebrKcZM9C0g==
  dependencies:
    "@babel/runtime" "^7.3.1"
    jss "10.10.0"
    tiny-warning "^1.0.2"

jss-plugin-vendor-prefixer@^10.8.2:
  version "10.10.0"
  resolved "https://registry.npmjs.org/jss-plugin-vendor-prefixer/-/jss-plugin-vendor-prefixer-10.10.0.tgz"
  integrity sha512-UY/41WumgjW8r1qMCO8l1ARg7NHnfRVWRhZ2E2m0DMYsr2DD91qIXLyNhiX83hHswR7Wm4D+oDYNC1zWCJWtqg==
  dependencies:
    "@babel/runtime" "^7.3.1"
    css-vendor "^2.0.8"
    jss "10.10.0"

jss@^10.8.2, jss@10.10.0:
  version "10.10.0"
  resolved "https://registry.npmjs.org/jss/-/jss-10.10.0.tgz"
  integrity sha512-cqsOTS7jqPsPMjtKYDUpdFC0AbhYFLTcuGRqymgmdJIeQ8cH7+AgX7YSgQy79wXloZq2VvATYxUOUQEvS1V/Zw==
  dependencies:
    "@babel/runtime" "^7.3.1"
    csstype "^3.0.2"
    is-in-browser "^1.1.3"
    tiny-warning "^1.0.2"

"jsx-ast-utils@^2.4.1 || ^3.0.0", jsx-ast-utils@^3.3.3:
  version "3.3.5"
  resolved "https://registry.npmjs.org/jsx-ast-utils/-/jsx-ast-utils-3.3.5.tgz"
  integrity sha512-ZZow9HBI5O6EPgSJLUb8n2NKgmVWTwCvHGwFuJlMjvLFqlGG6pjirPhtdsseaLZjSibD8eegzmYpUZwoIlj2cQ==
  dependencies:
    array-includes "^3.1.6"
    array.prototype.flat "^1.3.1"
    object.assign "^4.1.4"
    object.values "^1.1.6"

jwa@^1.4.1:
  version "1.4.1"
  resolved "https://registry.npmjs.org/jwa/-/jwa-1.4.1.tgz"
  integrity sha512-qiLX/xhEEFKUAJ6FiBMbes3w9ATzyk5W7Hvzpa/SLYdxNtng+gcurvrI7TbACjIXlsJyr05/S1oUhZrc63evQA==
  dependencies:
    buffer-equal-constant-time "1.0.1"
    ecdsa-sig-formatter "1.0.11"
    safe-buffer "^5.0.1"

jws@^3.2.2:
  version "3.2.2"
  resolved "https://registry.npmjs.org/jws/-/jws-3.2.2.tgz"
  integrity sha512-YHlZCB6lMTllWDtSPHz/ZXTsi8S00usEV6v1tjq8tOUZzw7DpSDWVXjXDre6ed1w/pd495ODpHZYSdkRTsa0HA==
  dependencies:
    jwa "^1.4.1"
    safe-buffer "^5.0.1"

keyv@^4.5.3:
  version "4.5.4"
  resolved "https://registry.npmjs.org/keyv/-/keyv-4.5.4.tgz"
  integrity sha512-oxVHkHR/EJf2CNXnWxRLW6mg7JyCCUcG0DtEGmL2ctUo1PNTin1PUil+r/+4r5MpVgC/fn1kjsx7mjSujKqIpw==
  dependencies:
    json-buffer "3.0.1"

language-subtag-registry@~0.3.2:
  version "0.3.22"
  resolved "https://registry.npmjs.org/language-subtag-registry/-/language-subtag-registry-0.3.22.tgz"
  integrity sha512-tN0MCzyWnoz/4nHS6uxdlFWoUZT7ABptwKPQ52Ea7URk6vll88bWBVhodtnlfEuCcKWNGoc+uGbw1cwa9IKh/w==

language-tags@=1.0.5:
  version "1.0.5"
  resolved "https://registry.npmjs.org/language-tags/-/language-tags-1.0.5.tgz"
  integrity sha512-qJhlO9cGXi6hBGKoxEG/sKZDAHD5Hnu9Hs4WbOY3pCWXDhw0N8x1NenNzm2EnNLkLkk7J2SdxAkDSbb6ftT+UQ==
  dependencies:
    language-subtag-registry "~0.3.2"

levn@^0.4.1:
  version "0.4.1"
  resolved "https://registry.npmjs.org/levn/-/levn-0.4.1.tgz"
  integrity sha512-+bT2uH4E5LGE7h/n3evcS/sQlJXCpIp6ym8OWJ5eV6+67Dsql/LaaT7qJBAt2rzfoa/5QBGBhxDix1dMt2kQKQ==
  dependencies:
    prelude-ls "^1.2.1"
    type-check "~0.4.0"

lilconfig@^3.0.0, lilconfig@^3.1.3:
  version "3.1.3"
  resolved "https://registry.npmjs.org/lilconfig/-/lilconfig-3.1.3.tgz"
  integrity sha512-/vlFKAoH5Cgt3Ie+JLhRbwOsCQePABiU3tJ1egGvyQ+33R/vcwM2Zl2QR/LzjsBeItPt3oSVXapn+m4nQDvpzw==

lines-and-columns@^1.1.6:
  version "1.2.4"
  resolved "https://registry.npmjs.org/lines-and-columns/-/lines-and-columns-1.2.4.tgz"
  integrity sha512-7ylylesZQ/PV29jhEDl3Ufjo6ZX7gCqJr5F7PKrqc93v7fzSymt1BpwEU8nAUXs8qzzvqhbjhK5QZg6Mt/HkBg==

load-script@^1.0.0:
  version "1.0.0"
  resolved "https://registry.npmjs.org/load-script/-/load-script-1.0.0.tgz"
  integrity sha512-kPEjMFtZvwL9TaZo0uZ2ml+Ye9HUMmPwbYRJ324qF9tqMejwykJ5ggTyvzmrbBeapCAbk98BSbTeovHEEP1uCA==

loader-runner@^4.2.0:
  version "4.3.0"
  resolved "https://registry.npmjs.org/loader-runner/-/loader-runner-4.3.0.tgz"
  integrity sha512-3R/1M+yS3j5ou80Me59j7F9IMs4PXs3VqRrm0TU3AbKPxlmpoY1TNscJV/oGJXo8qCatFGTfDbY6W6ipGOYXfg==

loader-utils@^2.0.0:
  version "2.0.4"
  resolved "https://registry.npmjs.org/loader-utils/-/loader-utils-2.0.4.tgz"
  integrity sha512-xXqpXoINfFhgua9xiqD8fPFHgkoq1mmmpE92WlDbm9rNRd/EbRb+Gqf908T2DMfuHjjJlksiK2RbHVOdD/MqSw==
  dependencies:
    big.js "^5.2.2"
    emojis-list "^3.0.0"
    json5 "^2.1.2"

lodash-es@4.17.21:
  version "4.17.21"
  resolved "https://registry.npmjs.org/lodash-es/-/lodash-es-4.17.21.tgz"
  integrity sha512-mKnC+QJ9pWVzv+C4/U3rRsHapFfHvQFoFB92e52xeyGMcX6/OlIl78je1u8vePzYZSkkogMPJ2yjxxsb89cxyw==

lodash.debounce@^4.0.8:
  version "4.0.8"
  resolved "https://registry.npmjs.org/lodash.debounce/-/lodash.debounce-4.0.8.tgz"
  integrity sha512-FT1yDzDYEoYWhnSGnpE/4Kj1fLZkDFyqRb7fNt6FdYOSxlUWAtp42Eh6Wb0rGIv/m9Bgo7x4GhQbm5Ys4SG5ow==

lodash.foreach@^4.5.0:
  version "4.5.0"
  resolved "https://registry.npmjs.org/lodash.foreach/-/lodash.foreach-4.5.0.tgz"
  integrity sha512-aEXTF4d+m05rVOAUG3z4vZZ4xVexLKZGF0lIxuHZ1Hplpk/3B6Z1+/ICICYRLm7c41Z2xiejbkCkJoTlypoXhQ==

lodash.get@^4.4.2:
  version "4.4.2"
  resolved "https://registry.npmjs.org/lodash.get/-/lodash.get-4.4.2.tgz"
  integrity sha512-z+Uw/vLuy6gQe8cfaFWD7p0wVv8fJl3mbzXh33RS+0oW2wvUqiRXiQ69gLWSLpgB5/6sU+r6BlQR0MBILadqTQ==

lodash.includes@^4.3.0:
  version "4.3.0"
  resolved "https://registry.npmjs.org/lodash.includes/-/lodash.includes-4.3.0.tgz"
  integrity sha512-W3Bx6mdkRTGtlJISOvVD/lbqjTlPPUDTMnlXZFnVwi9NKJ6tiAk6LVdlhZMm17VZisqhKcgzpO5Wz91PCt5b0w==

lodash.isboolean@^3.0.3:
  version "3.0.3"
  resolved "https://registry.npmjs.org/lodash.isboolean/-/lodash.isboolean-3.0.3.tgz"
  integrity sha512-Bz5mupy2SVbPHURB98VAcw+aHh4vRV5IPNhILUCsOzRmsTmSQ17jIuqopAentWoehktxGd9e/hbIXq980/1QJg==

lodash.isinteger@^4.0.4:
  version "4.0.4"
  resolved "https://registry.npmjs.org/lodash.isinteger/-/lodash.isinteger-4.0.4.tgz"
  integrity sha512-DBwtEWN2caHQ9/imiNeEA5ys1JoRtRfY3d7V9wkqtbycnAmTvRRmbHKDV4a0EYc678/dia0jrte4tjYwVBaZUA==

lodash.isnumber@^3.0.3:
  version "3.0.3"
  resolved "https://registry.npmjs.org/lodash.isnumber/-/lodash.isnumber-3.0.3.tgz"
  integrity sha512-QYqzpfwO3/CWf3XP+Z+tkQsfaLL/EnUlXWVkIk5FUPc4sBdTehEqZONuyRt2P67PXAk+NXmTBcc97zw9t1FQrw==

lodash.isplainobject@^4.0.6:
  version "4.0.6"
  resolved "https://registry.npmjs.org/lodash.isplainobject/-/lodash.isplainobject-4.0.6.tgz"
  integrity sha512-oSXzaWypCMHkPC3NvBEaPHf0KsA5mvPrOPgQWDsbg8n7orZ290M0BmC/jgRZ4vcJ6DTAhjrsSYgdsW/F+MFOBA==

lodash.isstring@^4.0.1:
  version "4.0.1"
  resolved "https://registry.npmjs.org/lodash.isstring/-/lodash.isstring-4.0.1.tgz"
  integrity sha512-0wJxfxH1wgO3GrbuP+dTTk7op+6L41QCXbGINEmD+ny/G/eCqGzxyCsh7159S+mgDDcoarnBw6PC1PS5+wUGgw==

lodash.kebabcase@^4.1.1:
  version "4.1.1"
  resolved "https://registry.npmjs.org/lodash.kebabcase/-/lodash.kebabcase-4.1.1.tgz"
  integrity sha512-N8XRTIMMqqDgSy4VLKPnJ/+hpGZN+PHQiJnSenYqPaVV/NCqEogTnAdZLQiGKhxX+JCs8waWq2t1XHWKOmlY8g==

lodash.mapkeys@^4.6.0:
  version "4.6.0"
  resolved "https://registry.npmjs.org/lodash.mapkeys/-/lodash.mapkeys-4.6.0.tgz"
  integrity sha512-0Al+hxpYvONWtg+ZqHpa/GaVzxuN3V7Xeo2p+bY06EaK/n+Y9R7nBePPN2o1LxmL0TWQSwP8LYZ008/hc9JzhA==

lodash.merge@^4.6.2:
  version "4.6.2"
  resolved "https://registry.npmjs.org/lodash.merge/-/lodash.merge-4.6.2.tgz"
  integrity sha512-0KpjqXRVvrYyCsX1swR/XTK0va6VQkQM6MNo7PqW77ByjAhoARA8EfrP1N4+KlKj8YS0ZUCtRT/YUuhyYDujIQ==

lodash.omit@^4.5.0:
  version "4.5.0"
  resolved "https://registry.npmjs.org/lodash.omit/-/lodash.omit-4.5.0.tgz"
  integrity sha512-XeqSp49hNGmlkj2EJlfrQFIzQ6lXdNro9sddtQzcJY8QaoC2GO0DT7xaIokHeyM+mIT0mPMlPvkYzg2xCuHdZg==

lodash.once@^4.0.0:
  version "4.1.1"
  resolved "https://registry.npmjs.org/lodash.once/-/lodash.once-4.1.1.tgz"
  integrity sha512-Sb487aTOCr9drQVL8pIxOzVhafOjZN9UU54hiN8PU3uAiSV7lx1yYNpbNmex2PK6dSJoNTSJUUswT651yww3Mg==

lodash@^4.17.4:
  version "4.17.21"
  resolved "https://registry.npmjs.org/lodash/-/lodash-4.17.21.tgz"
  integrity sha512-v2kDEe57lecTulaDIuNTPy3Ry4gLGJ6Z1O3vE1krgXZNrsQ+LFTGHVxVjcXPs17LhbZVGedAJv8XZ1tvj5FvSg==

loose-envify@^1.0.0, loose-envify@^1.1.0, loose-envify@^1.4.0:
  version "1.4.0"
  resolved "https://registry.npmjs.org/loose-envify/-/loose-envify-1.4.0.tgz"
  integrity sha512-lyuxPGr/Wfhrlem2CL/UcnUc1zcqKAImBDzukY7Y5F/yQiNdko6+fRLevlw1HgMySw7f611UIY408EtxRSoK3Q==
  dependencies:
    js-tokens "^3.0.0 || ^4.0.0"

lru-cache@^10.2.0:
  version "10.4.3"
  resolved "https://registry.npmjs.org/lru-cache/-/lru-cache-10.4.3.tgz"
  integrity sha512-JNAzZcXrCt42VGLuYz0zfAzDfAvJWW6AfYlDBQyDV5DClI2m5sAmK+OIO7s59XfsRsWHp02jAJrRadPRGTt6SQ==

lru-cache@^6.0.0:
  version "6.0.0"
  resolved "https://registry.npmjs.org/lru-cache/-/lru-cache-6.0.0.tgz"
  integrity sha512-Jo6dJ04CmSjuznwJSS3pUeWmd/H0ffTlkXXgwZi+eq1UCmqQwCh+eLsYOYCwY991i2Fah4h1BEMCx4qThGbsiA==
  dependencies:
    yallist "^4.0.0"

make-dir@^3.1.0:
  version "3.1.0"
  resolved "https://registry.npmjs.org/make-dir/-/make-dir-3.1.0.tgz"
  integrity sha512-g3FeP20LNwhALb/6Cz6Dd4F2ngze0jz7tbzrD2wAV+o9FeNHe4rL+yK2md0J/fiSf1sa1ADhXqi5+oVwOM/eGw==
  dependencies:
    semver "^6.0.0"

merge-stream@^2.0.0:
  version "2.0.0"
  resolved "https://registry.npmjs.org/merge-stream/-/merge-stream-2.0.0.tgz"
  integrity sha512-abv/qOcuPfk3URPfDzmZU1LKmuw8kT+0nIHvKrKgFrwifol/doWcdA4ZqsWQ8ENrFKkd67Mfpo/LovbIUsbt3w==

merge2@^1.3.0, merge2@^1.4.1:
  version "1.4.1"
  resolved "https://registry.npmjs.org/merge2/-/merge2-1.4.1.tgz"
  integrity sha512-8q7VEgMJW4J8tcfVPy8g09NcQwZdbwFEqhe/WZkoIzjn/3TGDwtOCYtXGxA3O8tPzpczCCDgv+P2P5y00ZJOOg==

micromatch@^4.0.4, micromatch@^4.0.8:
  version "4.0.8"
  resolved "https://registry.npmjs.org/micromatch/-/micromatch-4.0.8.tgz"
  integrity sha512-PXwfBhYu0hBCPw8Dn0E+WDYb7af3dSLVWKi3HGv84IdF4TyFoC0ysxFd0Goxw7nSv4T/PzEJQxsYsEiFCKo2BA==
  dependencies:
    braces "^3.0.3"
    picomatch "^2.3.1"

mime-db@1.52.0:
  version "1.52.0"
  resolved "https://registry.npmjs.org/mime-db/-/mime-db-1.52.0.tgz"
  integrity sha512-sPU4uV7dYlvtWJxwwxHD0PuihVNiE7TyAbQ5SWxDCB9mUYvOgroQOwYQQOKPJ8CIbE+1ETVlOoK1UC2nU3gYvg==

mime-types@^2.1.12, mime-types@^2.1.27:
  version "2.1.35"
  resolved "https://registry.npmjs.org/mime-types/-/mime-types-2.1.35.tgz"
  integrity sha512-ZDY+bPm5zTTF+YpCrAU9nK0UgICYPT0QtT1NZWFv4s++TNkcgVaT0g6+4R2uI4MjQjzysHB1zxuWL50hzaeXiw==
  dependencies:
    mime-db "1.52.0"

minimatch@^3.0.4, minimatch@^3.1.1, minimatch@^3.1.2:
  version "3.1.2"
  resolved "https://registry.npmjs.org/minimatch/-/minimatch-3.1.2.tgz"
  integrity sha512-J7p63hRiAjw1NDEww1W7i37+ByIrOWO5XQQAzZ3VOcL0PNybwpfmV/N05zFAzwQ9USyEcX6t3UO+K5aqBQOIHw==
  dependencies:
    brace-expansion "^1.1.7"

minimatch@^9.0.4:
  version "9.0.5"
  resolved "https://registry.npmjs.org/minimatch/-/minimatch-9.0.5.tgz"
  integrity sha512-G6T0ZX48xgozx7587koeX9Ys2NYy6Gmv//P89sEte9V9whIapMNF4idKxnW2QtCcLiTWlb/wfCabAtAFWhhBow==
  dependencies:
    brace-expansion "^2.0.1"

minimist@^1.2.0, minimist@^1.2.6, minimist@~1.2.5:
  version "1.2.8"
  resolved "https://registry.npmjs.org/minimist/-/minimist-1.2.8.tgz"
  integrity sha512-2yyAR8qBkN3YuheJanUpWC5U3bb5osDywNB8RzDVlDwDHbocAJveqqj1u8+SVD7jkWT4yvsHCpWqqWqAxb0zCA==

minipass@^3.0.0:
  version "3.3.6"
  resolved "https://registry.npmjs.org/minipass/-/minipass-3.3.6.tgz"
  integrity sha512-DxiNidxSEK+tHG6zOIklvNOwm3hvCrbUrdtzY74U6HKTJxvIDfOUL5W5P2Ghd3DTkhhKPYGqeNUIh5qcM4YBfw==
  dependencies:
    yallist "^4.0.0"

"minipass@^5.0.0 || ^6.0.2 || ^7.0.0":
  version "7.1.2"
  resolved "https://registry.npmjs.org/minipass/-/minipass-7.1.2.tgz"
  integrity sha512-qOOzS1cBTWYF4BH8fVePDBOO9iptMnGUEZwNc/cMWnTV2nVLZ7VoNWEPHkYczZA0pdoA7dl6e7FL659nX9S2aw==

minipass@^5.0.0:
  version "5.0.0"
  resolved "https://registry.npmjs.org/minipass/-/minipass-5.0.0.tgz"
  integrity sha512-3FnjYuehv9k6ovOEbyOswadCDPX1piCfhV8ncmYtHOjuPwylVWsghTLo7rabjC3Rx5xD4HDx8Wm1xnMF7S5qFQ==

minipass@^7.1.2:
  version "7.1.2"
  resolved "https://registry.npmjs.org/minipass/-/minipass-7.1.2.tgz"
  integrity sha512-qOOzS1cBTWYF4BH8fVePDBOO9iptMnGUEZwNc/cMWnTV2nVLZ7VoNWEPHkYczZA0pdoA7dl6e7FL659nX9S2aw==

minizlib@^2.1.1:
  version "2.1.2"
  resolved "https://registry.npmjs.org/minizlib/-/minizlib-2.1.2.tgz"
  integrity sha512-bAxsR8BVfj60DWXHE3u30oHzfl4G7khkSuPW+qvpd7jFRHm7dLxOjUk1EHACJ/hxLY8phGJ0YhYHZo7jil7Qdg==
  dependencies:
    minipass "^3.0.0"
    yallist "^4.0.0"

mkdirp@^1.0.3:
  version "1.0.4"
  resolved "https://registry.npmjs.org/mkdirp/-/mkdirp-1.0.4.tgz"
  integrity sha512-vVqVZQyf3WLx2Shd0qJ9xuvqgAyKPLAiqITEtqW0oIUjzo3PePDd6fW9iFz30ef7Ysp/oiWqbhszeGWW2T6Gzw==

ms@^2.1.1:
  version "2.1.3"
  resolved "https://registry.npmjs.org/ms/-/ms-2.1.3.tgz"
  integrity sha512-6FlzubTLZG3J2a/NVCAleEhjzq5oxgHyaCU9yYXvcLsvoVaHJq/s5xXI6/XXP6tz7R9xAOtHnSO/tXtF3WRTlA==

ms@2.0.0:
  version "2.0.0"
  resolved "https://registry.npmjs.org/ms/-/ms-2.0.0.tgz"
  integrity sha512-Tpp60P6IUJDTuOq/5Z8cdskzJujfwqfOTkrwIwj7IRISpnkJnT6SyJ4PCPnGMoFjC9ddhal5KVIYtAt97ix05A==

ms@2.1.2:
  version "2.1.2"
  resolved "https://registry.npmjs.org/ms/-/ms-2.1.2.tgz"
  integrity sha512-sGkPx+VjMtmA6MX27oA4FBFELFCZZ4S4XqeGOXCv68tT+jb3vk/RyaKWP0PTKyWtmLSM0b+adUTEvbs1PEaH2w==

multipipe@^1.0.2:
  version "1.0.2"
  resolved "https://registry.npmjs.org/multipipe/-/multipipe-1.0.2.tgz"
  integrity sha512-6uiC9OvY71vzSGX8lZvSqscE7ft9nPupJ8fMjrCNRAUy2LREUW42UL+V/NTrogr6rFgRydUrCX4ZitfpSNkSCQ==
  dependencies:
    duplexer2 "^0.1.2"
    object-assign "^4.1.0"

mz@^2.7.0:
  version "2.7.0"
  resolved "https://registry.npmjs.org/mz/-/mz-2.7.0.tgz"
  integrity sha512-z81GNO7nnYMEhrGh9LeymoE4+Yr0Wn5McHIZMK5cfQCl+NDX08sCZgUc9/6MHni9IWuFLm1Z3HTCXu2z9fN62Q==
  dependencies:
    any-promise "^1.0.0"
    object-assign "^4.0.1"
    thenify-all "^1.0.0"

nanoid@^3.1.30:
  version "3.3.6"
  resolved "https://registry.npmjs.org/nanoid/-/nanoid-3.3.6.tgz"
  integrity sha512-BGcqMMJuToF7i1rt+2PWSNVnWIkGCU78jBG3RxO/bZlnZPK2Cmi2QaffxGO/2RvWi9sL+FAiRiXMgsyxQ1DIDA==

nanoid@^3.3.7:
  version "3.3.7"
  resolved "https://registry.npmjs.org/nanoid/-/nanoid-3.3.7.tgz"
  integrity sha512-eSRppjcPIatRIMC1U6UngP8XFcz8MQWGQdt1MTBQ7NaAmvXDfvNxbvWV3x2y6CdEUciCSsDHDQZbhYaB8QEo2g==

natural-compare@^1.4.0:
  version "1.4.0"
  resolved "https://registry.npmjs.org/natural-compare/-/natural-compare-1.4.0.tgz"
  integrity sha512-OWND8ei3VtNC9h7V60qff3SVobHr996CTwgxubgyQYEpg290h9J0buyECNNJexkFm5sOajh5G116RYA1c8ZMSw==

neo-async@^2.6.2:
  version "2.6.2"
  resolved "https://registry.npmjs.org/neo-async/-/neo-async-2.6.2.tgz"
  integrity sha512-Yd3UES5mWCSqR+qNT93S3UoYUkqAZ9lLg8a7g9rimsWmYGK8cVToA4/sF3RrshdyV3sAGMXVUmpMYOw+dLpOuw==

next-images@1.8.4:
  version "1.8.4"
  resolved "https://registry.npmjs.org/next-images/-/next-images-1.8.4.tgz"
  integrity sha512-E6JV+aMxeUCh8A+cwn1xgmlh/zINSW4JC/XLNbM+PWQd5LBdfB+m1IDCAfNnGOKMo96kzw+4LsKxnX/Kldw78Q==
  dependencies:
    file-loader "^6.2.0"
    url-loader "^4.1.0"

next-transpile-modules@9.0.0:
  version "9.0.0"
  resolved "https://registry.npmjs.org/next-transpile-modules/-/next-transpile-modules-9.0.0.tgz"
  integrity sha512-VCNFOazIAnXn1hvgYYSTYMnoWgKgwlYh4lm1pKbSfiB3kj5ZYLcKVhfh3jkPOg1cnd9DP+pte9yCUocdPEUBTQ==
  dependencies:
    enhanced-resolve "^5.7.0"
    escalade "^3.1.1"

next@>=10.2.0, next@12.1.6:
  version "12.1.6"
  resolved "https://registry.npmjs.org/next/-/next-12.1.6.tgz"
  integrity sha512-cebwKxL3/DhNKfg9tPZDQmbRKjueqykHHbgaoG4VBRH3AHQJ2HO0dbKFiS1hPhe1/qgc2d/hFeadsbPicmLD+A==
  dependencies:
    "@next/env" "12.1.6"
    caniuse-lite "^1.0.30001332"
    postcss "8.4.5"
    styled-jsx "5.0.2"
  optionalDependencies:
    "@next/swc-android-arm-eabi" "12.1.6"
    "@next/swc-android-arm64" "12.1.6"
    "@next/swc-darwin-arm64" "12.1.6"
    "@next/swc-darwin-x64" "12.1.6"
    "@next/swc-linux-arm-gnueabihf" "12.1.6"
    "@next/swc-linux-arm64-gnu" "12.1.6"
    "@next/swc-linux-arm64-musl" "12.1.6"
    "@next/swc-linux-x64-gnu" "12.1.6"
    "@next/swc-linux-x64-musl" "12.1.6"
    "@next/swc-win32-arm64-msvc" "12.1.6"
    "@next/swc-win32-ia32-msvc" "12.1.6"
    "@next/swc-win32-x64-msvc" "12.1.6"

node-addon-api@^5.0.0:
  version "5.1.0"
  resolved "https://registry.npmjs.org/node-addon-api/-/node-addon-api-5.1.0.tgz"
  integrity sha512-eh0GgfEkpnoWDq+VY8OyvYhFEzBk6jIYbRKdIlyTiAXIVJ8PyBaKb0rp7oDtoddbdoHWhq8wwr+XZ81F1rpNdA==

node-fetch@^2.6.7:
  version "2.7.0"
  resolved "https://registry.npmjs.org/node-fetch/-/node-fetch-2.7.0.tgz"
  integrity sha512-c4FRfUm/dbcWZ7U+1Wq0AwCyFL+3nt2bEw05wfxSz+DWpWsitgmSgYmy2dQdWyKC1694ELPqMs/YzUSNozLt8A==
  dependencies:
    whatwg-url "^5.0.0"

node-releases@^2.0.18:
  version "2.0.18"
  resolved "https://registry.npmjs.org/node-releases/-/node-releases-2.0.18.tgz"
  integrity sha512-d9VeXT4SJ7ZeOqGX6R5EM022wpL+eWPooLI+5UpWn2jCT1aosUQEhQP214x33Wkwx3JQMvIm+tIoVOdodFS40g==

nodemailer@^6.9.7:
  version "6.9.7"
  resolved "https://registry.npmjs.org/nodemailer/-/nodemailer-6.9.7.tgz"
  integrity sha512-rUtR77ksqex/eZRLmQ21LKVH5nAAsVicAtAYudK7JgwenEDZ0UIQ1adUGqErz7sMkWYxWTTU1aeP2Jga6WQyJw==

nopt@^5.0.0:
  version "5.0.0"
  resolved "https://registry.npmjs.org/nopt/-/nopt-5.0.0.tgz"
  integrity sha512-Tbj67rffqceeLpcRXrT7vKAN8CwfPeIBgM7E6iBkmKLV7bEMwpGgYLGv0jACUsECaa/vuxP0IjEont6umdMgtQ==
  dependencies:
    abbrev "1"

normalize-path@^3.0.0, normalize-path@~3.0.0:
  version "3.0.0"
  resolved "https://registry.npmjs.org/normalize-path/-/normalize-path-3.0.0.tgz"
  integrity sha512-6eZs5Ls3WtCisHWp9S2GUy8dqkpGi4BVSz3GaqiE6ezub0512ESztXUwUB6C6IKbQkY2Pnb/mD4WYojCRwcwLA==

normalize-range@^0.1.2:
  version "0.1.2"
  resolved "https://registry.npmjs.org/normalize-range/-/normalize-range-0.1.2.tgz"
  integrity sha512-bdok/XvKII3nUpklnV6P2hxtMNrCboOjAcyBuQnWEhO665FwrSNRxU+AqpsyvO6LgGYPspN+lu5CLtw4jPRKNA==

npmlog@^5.0.1:
  version "5.0.1"
  resolved "https://registry.npmjs.org/npmlog/-/npmlog-5.0.1.tgz"
  integrity sha512-AqZtDUWOMKs1G/8lwylVjrdYgqA4d9nu8hc+0gzRxlDb1I10+FHBGMXs6aiQHFdCUUlqH99MUMuLfzWDNDtfxw==
  dependencies:
    are-we-there-yet "^2.0.0"
    console-control-strings "^1.1.0"
    gauge "^3.0.0"
    set-blocking "^2.0.0"

nprogress@0.2.0:
  version "0.2.0"
  resolved "https://registry.npmjs.org/nprogress/-/nprogress-0.2.0.tgz"
  integrity sha512-I19aIingLgR1fmhftnbWWO3dXc0hSxqHQHQb3H8m+K3TnEn/iSeTZZOyvKXWqQESMwuUVnatlCnZdLBZZt2VSA==

numeral@2.0.6:
  version "2.0.6"
  resolved "https://registry.npmjs.org/numeral/-/numeral-2.0.6.tgz"
  integrity sha512-qaKRmtYPZ5qdw4jWJD6bxEf1FJEqllJrwxCLIm0sQU/A7v2/czigzOb+C2uSiFsa9lBUzeH7M1oK+Q+OLxL3kA==

object-assign@^4.0.1, object-assign@^4.1.0, object-assign@^4.1.1:
  version "4.1.1"
  resolved "https://registry.npmjs.org/object-assign/-/object-assign-4.1.1.tgz"
  integrity sha512-rJgTQnkUnH1sFw8yT6VSU3zD3sWmu6sZhIseY8VX+GRu3P6F7Fu+JNDoXfklElbLJSnc3FUQHVe4cU5hj+BcUg==

object-hash@^3.0.0:
  version "3.0.0"
  resolved "https://registry.npmjs.org/object-hash/-/object-hash-3.0.0.tgz"
  integrity sha512-RSn9F68PjH9HqtltsSnqYC1XXoWe9Bju5+213R98cNGttag9q9yAOTzdbsqvIa7aNm5WffBZFpWYr2aWrklWAw==

object-inspect@^1.13.1, object-inspect@^1.9.0:
  version "1.13.1"
  resolved "https://registry.npmjs.org/object-inspect/-/object-inspect-1.13.1.tgz"
  integrity sha512-5qoj1RUiKOMsCCNLV1CBiPYE10sziTsnmNxkAI/rZhiD63CF7IqdFGC/XzjWjpSgLf0LxXX3bDFIh0E18f6UhQ==

object-keys@^1.1.1:
  version "1.1.1"
  resolved "https://registry.npmjs.org/object-keys/-/object-keys-1.1.1.tgz"
  integrity sha512-NuAESUOUMrlIXOfHKzD6bpPu3tYt3xvjNdRIQ+FeT0lNb4K8WR70CaDxhuNguS2XG+GjkyMwOzsN5ZktImfhLA==

object-keys@~0.4.0:
  version "0.4.0"
  resolved "https://registry.npmjs.org/object-keys/-/object-keys-0.4.0.tgz"
  integrity sha512-ncrLw+X55z7bkl5PnUvHwFK9FcGuFYo9gtjws2XtSzL+aZ8tm830P60WJ0dSmFVaSalWieW5MD7kEdnXda9yJw==

object.assign@^4.1.4:
  version "4.1.4"
  resolved "https://registry.npmjs.org/object.assign/-/object.assign-4.1.4.tgz"
  integrity sha512-1mxKf0e58bvyjSCtKYY4sRe9itRk3PJpquJOjeIkz885CczcI4IvJJDLPS72oowuSh+pBxUFROpX+TU++hxhZQ==
  dependencies:
    call-bind "^1.0.2"
    define-properties "^1.1.4"
    has-symbols "^1.0.3"
    object-keys "^1.1.1"

object.entries@^1.1.6:
  version "1.1.7"
  resolved "https://registry.npmjs.org/object.entries/-/object.entries-1.1.7.tgz"
  integrity sha512-jCBs/0plmPsOnrKAfFQXRG2NFjlhZgjjcBLSmTnEhU8U6vVTsVe8ANeQJCHTl3gSsI4J+0emOoCgoKlmQPMgmA==
  dependencies:
    call-bind "^1.0.2"
    define-properties "^1.2.0"
    es-abstract "^1.22.1"

object.fromentries@^2.0.6, object.fromentries@^2.0.7:
  version "2.0.7"
  resolved "https://registry.npmjs.org/object.fromentries/-/object.fromentries-2.0.7.tgz"
  integrity sha512-UPbPHML6sL8PI/mOqPwsH4G6iyXcCGzLin8KvEPenOZN5lpCNBZZQ+V62vdjB1mQHrmqGQt5/OJzemUA+KJmEA==
  dependencies:
    call-bind "^1.0.2"
    define-properties "^1.2.0"
    es-abstract "^1.22.1"

object.groupby@^1.0.1:
  version "1.0.1"
  resolved "https://registry.npmjs.org/object.groupby/-/object.groupby-1.0.1.tgz"
  integrity sha512-HqaQtqLnp/8Bn4GL16cj+CUYbnpe1bh0TtEaWvybszDG4tgxCJuRpV8VGuvNaI1fAnI4lUJzDG55MXcOH4JZcQ==
  dependencies:
    call-bind "^1.0.2"
    define-properties "^1.2.0"
    es-abstract "^1.22.1"
    get-intrinsic "^1.2.1"

object.hasown@^1.1.2:
  version "1.1.3"
  resolved "https://registry.npmjs.org/object.hasown/-/object.hasown-1.1.3.tgz"
  integrity sha512-fFI4VcYpRHvSLXxP7yiZOMAd331cPfd2p7PFDVbgUsYOfCT3tICVqXWngbjr4m49OvsBwUBQ6O2uQoJvy3RexA==
  dependencies:
    define-properties "^1.2.0"
    es-abstract "^1.22.1"

object.values@^1.1.6, object.values@^1.1.7:
  version "1.1.7"
  resolved "https://registry.npmjs.org/object.values/-/object.values-1.1.7.tgz"
  integrity sha512-aU6xnDFYT3x17e/f0IiiwlGPTy2jzMySGfUB4fq6z7CV8l85CWHDk5ErhyhpfDHhrOMwGFhSQkhMGHaIotA6Ng==
  dependencies:
    call-bind "^1.0.2"
    define-properties "^1.2.0"
    es-abstract "^1.22.1"

once@^1.3.0:
  version "1.4.0"
  resolved "https://registry.npmjs.org/once/-/once-1.4.0.tgz"
  integrity sha512-lNaJgI+2Q5URQBkccEKHTQOPaXdUxnZZElQTZY0MFUAuaEqe1E+Nyvgdz/aIyNi6Z9MzO5dv1H8n58/GELp3+w==
  dependencies:
    wrappy "1"

optionator@^0.9.1:
  version "0.9.3"
  resolved "https://registry.npmjs.org/optionator/-/optionator-0.9.3.tgz"
  integrity sha512-JjCoypp+jKn1ttEFExxhetCKeJt9zhAgAve5FXHixTvFDW/5aEktX9bufBKLRRMdU7bNtpLfcGu94B3cdEJgjg==
  dependencies:
    "@aashutoshrathi/word-wrap" "^1.2.3"
    deep-is "^0.1.3"
    fast-levenshtein "^2.0.6"
    levn "^0.4.1"
    prelude-ls "^1.2.1"
    type-check "^0.4.0"

package-json-from-dist@^1.0.0:
  version "1.0.1"
  resolved "https://registry.npmjs.org/package-json-from-dist/-/package-json-from-dist-1.0.1.tgz"
  integrity sha512-UEZIS3/by4OC8vL3P2dTXRETpebLI2NiI5vIrjaD/5UtrkFX/tNbwjTSRAGC/+7CAo2pIcBaRgWmcBBHcsaCIw==

parent-module@^1.0.0:
  version "1.0.1"
  resolved "https://registry.npmjs.org/parent-module/-/parent-module-1.0.1.tgz"
  integrity sha512-GQ2EWRpQV8/o+Aw8YqtfZZPfNRWZYkbidE9k5rpl/hC3vtHHBfGm2Ifi6qWV+coDGkrUKZAxE3Lot5kcsRlh+g==
  dependencies:
    callsites "^3.0.0"

parse-json@^5.0.0:
  version "5.2.0"
  resolved "https://registry.npmjs.org/parse-json/-/parse-json-5.2.0.tgz"
  integrity sha512-ayCKvm/phCGxOkYRSCM82iDwct8/EonSEgCSxWxD7ve6jHggsFl4fZVQBPRNgQoKiuV/odhFrGzQXZwbifC8Rg==
  dependencies:
    "@babel/code-frame" "^7.0.0"
    error-ex "^1.3.1"
    json-parse-even-better-errors "^2.3.0"
    lines-and-columns "^1.1.6"

path-is-absolute@^1.0.0:
  version "1.0.1"
  resolved "https://registry.npmjs.org/path-is-absolute/-/path-is-absolute-1.0.1.tgz"
  integrity sha512-AVbw3UJ2e9bq64vSaS9Am0fje1Pa8pbGqTTsmXfaIiMpnr5DlDhfJOuLj9Sf95ZPVDAUerDfEk88MPmPe7UCQg==

path-key@^3.1.0:
  version "3.1.1"
  resolved "https://registry.npmjs.org/path-key/-/path-key-3.1.1.tgz"
  integrity sha512-ojmeN0qd+y0jszEtoY48r0Peq5dwMEkIlCOu6Q5f41lfkswXuKtYrhgoTpLnyIcHm24Uhqx+5Tqm2InSwLhE6Q==

path-parse@^1.0.7:
  version "1.0.7"
  resolved "https://registry.npmjs.org/path-parse/-/path-parse-1.0.7.tgz"
  integrity sha512-LDJzPVEEEPR+y48z93A0Ed0yXb8pAByGWo/k5YYdYgpY2/2EsOsksJrq7lOHxryrVOn1ejG6oAp8ahvOIQD8sw==

path-scurry@^1.11.1:
  version "1.11.1"
  resolved "https://registry.npmjs.org/path-scurry/-/path-scurry-1.11.1.tgz"
  integrity sha512-Xa4Nw17FS9ApQFJ9umLiJS4orGjm7ZzwUrwamcGQuHSzDyth9boKDaycYdDcZDuqYATXw4HFXgaqWTctW/v1HA==
  dependencies:
    lru-cache "^10.2.0"
    minipass "^5.0.0 || ^6.0.2 || ^7.0.0"

path-type@^4.0.0:
  version "4.0.0"
  resolved "https://registry.npmjs.org/path-type/-/path-type-4.0.0.tgz"
  integrity sha512-gDKb8aZMDeD/tZWs9P6+q0J9Mwkdl6xMV8TjnGP3qJVJ06bdMgkbBlLU8IdfOsIsFz2BW1rNVT3XuNEl8zPAvw==

performance-now@^2.1.0:
  version "2.1.0"
  resolved "https://registry.npmjs.org/performance-now/-/performance-now-2.1.0.tgz"
  integrity sha512-7EAHlyLHI56VEIdK57uwHdHKIaAGbnXPiw0yWbarQZOKaKpvUIgW0jWRVLiatnM+XXlSwsanIBH/hzGMJulMow==

picocolors@^1.0.0, picocolors@^1.1.0, picocolors@^1.1.1:
  version "1.1.1"
  resolved "https://registry.npmjs.org/picocolors/-/picocolors-1.1.1.tgz"
  integrity sha512-xceH2snhtb5M9liqDsmEw56le376mTZkEX/jEb/RxNFyegNul7eNslCXP9FDj/Lcu0X8KEyMceP2ntpaHrDEVA==

picomatch@^2.0.4, picomatch@^2.2.1, picomatch@^2.3.1:
  version "2.3.1"
  resolved "https://registry.npmjs.org/picomatch/-/picomatch-2.3.1.tgz"
  integrity sha512-JU3teHTNjmE2VCGFzuY8EXzCDVwEqB2a8fsIvwaStHhAWJEeVd1o1QD80CU6+ZdEXXSLbSsuLwJjkCBWqRQUVA==

pify@^2.3.0:
  version "2.3.0"
  resolved "https://registry.npmjs.org/pify/-/pify-2.3.0.tgz"
  integrity sha512-udgsAY+fTnvv7kI7aaxbqwWNb0AHiB0qBO89PZKPkoTmGOgdbrHDKD+0B2X4uTfJ/FT1R09r9gTsjUjNJotuog==

pirates@^4.0.1:
  version "4.0.6"
  resolved "https://registry.npmjs.org/pirates/-/pirates-4.0.6.tgz"
  integrity sha512-saLsH7WeYYPiD25LDuLRRY/i+6HaPYr6G1OUlN39otzkSTxKnubR9RTxS3/Kk50s1g2JTgFwWQDQyplC5/SHZg==

postcss-import@^15.1.0:
  version "15.1.0"
  resolved "https://registry.npmjs.org/postcss-import/-/postcss-import-15.1.0.tgz"
  integrity sha512-hpr+J05B2FVYUAXHeK1YyI267J/dDDhMU6B6civm8hSY1jYJnBXxzKDKDswzJmtLHryrjhnDjqqp/49t8FALew==
  dependencies:
    postcss-value-parser "^4.0.0"
    read-cache "^1.0.0"
    resolve "^1.1.7"

postcss-js@^4.0.1:
  version "4.0.1"
  resolved "https://registry.npmjs.org/postcss-js/-/postcss-js-4.0.1.tgz"
  integrity sha512-dDLF8pEO191hJMtlHFPRa8xsizHaM82MLfNkUHdUtVEV3tgTp5oj+8qbEqYM57SLfc74KSbw//4SeJma2LRVIw==
  dependencies:
    camelcase-css "^2.0.1"

postcss-load-config@^4.0.2:
  version "4.0.2"
  resolved "https://registry.npmjs.org/postcss-load-config/-/postcss-load-config-4.0.2.tgz"
  integrity sha512-bSVhyJGL00wMVoPUzAVAnbEoWyqRxkjv64tUl427SKnPrENtq6hJwUojroMz2VB+Q1edmi4IfrAPpami5VVgMQ==
  dependencies:
    lilconfig "^3.0.0"
    yaml "^2.3.4"

postcss-nested@^6.2.0:
  version "6.2.0"
  resolved "https://registry.npmjs.org/postcss-nested/-/postcss-nested-6.2.0.tgz"
  integrity sha512-HQbt28KulC5AJzG+cZtj9kvKB93CFCdLvog1WFLf1D+xmMvPGlBstkpTEZfK5+AN9hfJocyBFCNiqyS48bpgzQ==
  dependencies:
    postcss-selector-parser "^6.1.1"

postcss-selector-parser@^6.1.1, postcss-selector-parser@^6.1.2:
  version "6.1.2"
  resolved "https://registry.npmjs.org/postcss-selector-parser/-/postcss-selector-parser-6.1.2.tgz"
  integrity sha512-Q8qQfPiZ+THO/3ZrOrO0cJJKfpYCagtMUkXbnEfmgUjwXg6z/WBeOyS9APBBPCTSiDV+s4SwQGu8yFsiMRIudg==
  dependencies:
    cssesc "^3.0.0"
    util-deprecate "^1.0.2"

postcss-value-parser@^4.0.0, postcss-value-parser@^4.2.0:
  version "4.2.0"
  resolved "https://registry.npmjs.org/postcss-value-parser/-/postcss-value-parser-4.2.0.tgz"
  integrity sha512-1NNCs6uurfkVbeXG4S8JFT9t19m45ICnif8zWLd5oPSZ50QnwMfK+H3jv408d4jw/7Bttv5axS5IiHoLaVNHeQ==

postcss@^8.0.0, postcss@^8.1.0, postcss@^8.2.14, postcss@^8.4.21, postcss@^8.4.28, postcss@^8.4.47, postcss@>=8.0.9:
  version "8.4.49"
  resolved "https://registry.npmjs.org/postcss/-/postcss-8.4.49.tgz"
  integrity sha512-OCVPnIObs4N29kxTjzLfUryOkvZEq+pf8jTF0lg8E7uETuWHA+v7j3c/xJmiqpX450191LlmZfUKkXxkTry7nA==
  dependencies:
    nanoid "^3.3.7"
    picocolors "^1.1.1"
    source-map-js "^1.2.1"

postcss@8.4.5:
  version "8.4.5"
  resolved "https://registry.npmjs.org/postcss/-/postcss-8.4.5.tgz"
  integrity sha512-jBDboWM8qpaqwkMwItqTQTiFikhs/67OYVvblFFTM7MrZjt6yMKd6r2kgXizEbTTljacm4NldIlZnhbjr84QYg==
  dependencies:
    nanoid "^3.1.30"
    picocolors "^1.0.0"
    source-map-js "^1.0.1"

prefix-style@2.0.1:
  version "2.0.1"
  resolved "https://registry.npmjs.org/prefix-style/-/prefix-style-2.0.1.tgz"
  integrity sha512-gdr1MBNVT0drzTq95CbSNdsrBDoHGlb2aDJP/FoY+1e+jSDPOb1Cv554gH2MGiSr2WTcXi/zu+NaFzfcHQkfBQ==

prelude-ls@^1.2.1:
  version "1.2.1"
  resolved "https://registry.npmjs.org/prelude-ls/-/prelude-ls-1.2.1.tgz"
  integrity sha512-vkcDPrRZo1QZLbn5RLGPpg/WmIQ65qoWWhcGKf/b5eplkkarX0m9z8ppCat4mlOqUsWpyNuYgO3VRyrYHSzX5g==

prettier-linter-helpers@^1.0.0:
  version "1.0.0"
  resolved "https://registry.npmjs.org/prettier-linter-helpers/-/prettier-linter-helpers-1.0.0.tgz"
  integrity sha512-GbK2cP9nraSSUF9N2XwUwqfzlAFlMNYYl+ShE/V+H8a9uNl/oUqB1w2EL54Jh0OlyRSd8RfWYJ3coVS4TROP2w==
  dependencies:
    fast-diff "^1.1.2"

prettier@>=2.0.0, prettier@2.6.2:
  version "2.6.2"
  resolved "https://registry.npmjs.org/prettier/-/prettier-2.6.2.tgz"
  integrity sha512-PkUpF+qoXTqhOeWL9fu7As8LXsIUZ1WYaJiY/a7McAQzxjk82OF0tibkFXVCDImZtWxbvojFjerkiLb0/q8mew==

prisma@*, prisma@^5.1.1:
  version "5.5.2"
  resolved "https://registry.npmjs.org/prisma/-/prisma-5.5.2.tgz"
  integrity sha512-WQtG6fevOL053yoPl6dbHV+IWgKo25IRN4/pwAGqcWmg7CrtoCzvbDbN9fXUc7QS2KK0LimHIqLsaCOX/vHl8w==
  dependencies:
    "@prisma/engines" "5.5.2"

process-nextick-args@~2.0.0:
  version "2.0.1"
  resolved "https://registry.npmjs.org/process-nextick-args/-/process-nextick-args-2.0.1.tgz"
  integrity sha512-3ouUOpQhtgrbOa17J7+uxOTpITYWaGP7/AhoR3+A+/1e9skrzelGi/dXzEYyvbxubEF6Wn2ypscTKiKJFFn1ag==

prop-types@^15.5.10, prop-types@^15.5.7, prop-types@^15.6.2, prop-types@^15.7.2, prop-types@^15.8.1, prop-types@15.8.1:
  version "15.8.1"
  resolved "https://registry.npmjs.org/prop-types/-/prop-types-15.8.1.tgz"
  integrity sha512-oj87CgZICdulUohogVAR7AjlC0327U4el4L6eAvOqCeudMDVU0NThNaV+b9Df4dXgSP1gXMTnPdhfe/2qDH5cg==
  dependencies:
    loose-envify "^1.4.0"
    object-assign "^4.1.1"
    react-is "^16.13.1"

proxy-from-env@^1.1.0:
  version "1.1.0"
  resolved "https://registry.npmjs.org/proxy-from-env/-/proxy-from-env-1.1.0.tgz"
  integrity sha512-D+zkORCbA9f1tdWRK0RaCR3GPv50cMxcrz4X8k5LTSUD1Dkw47mKJEZQNunItRTkWwgtaUSo1RVFRIG9ZXiFYg==

punycode@^2.1.0:
  version "2.3.0"
  resolved "https://registry.npmjs.org/punycode/-/punycode-2.3.0.tgz"
  integrity sha512-rRV+zQD8tVFys26lAGR9WUuS4iUAngJScM+ZRSKtvl5tKeZ2t5bvdNFdNHBW9FWR4guGHlgmsZ1G7BSm2wTbuA==

queue-microtask@^1.2.2:
  version "1.2.3"
  resolved "https://registry.npmjs.org/queue-microtask/-/queue-microtask-1.2.3.tgz"
  integrity sha512-NuaNSa6flKT5JaSYQzJok04JzTL1CA6aGhv5rfLW3PgqA+M2ChpZQnAC8h8i4ZFkBS8X5RqkDBHA7r4hej3K9A==

raf@^3.1.0, raf@^3.4.1:
  version "3.4.1"
  resolved "https://registry.npmjs.org/raf/-/raf-3.4.1.tgz"
  integrity sha512-Sq4CW4QhwOHE8ucn6J34MqtZCeWFP2aQSmrlroYgqAV1PjStIhJXxYuTgUIfkEk7zTLjmIjLmU5q+fbD1NnOJA==
  dependencies:
    performance-now "^2.1.0"

randombytes@^2.1.0:
  version "2.1.0"
  resolved "https://registry.npmjs.org/randombytes/-/randombytes-2.1.0.tgz"
  integrity sha512-vYl3iOX+4CKUWuxGi9Ukhie6fsqXqS9FE2Zaic4tNFD2N2QQaXOMFbuKK4QmDHC0JO6B1Zp41J0LpT0oR68amQ==
  dependencies:
    safe-buffer "^5.1.0"

react-apexcharts@1.4.0:
  version "1.4.0"
  resolved "https://registry.npmjs.org/react-apexcharts/-/react-apexcharts-1.4.0.tgz"
  integrity sha512-DrcMV4aAMrUG+n6412yzyATWEyCDWlpPBBhVbpzBC4PDeuYU6iF84SmExbck+jx5MUm4U5PM3/T307Mc3kzc9Q==
  dependencies:
    prop-types "^15.5.7"

react-custom-scrollbars-2@4.4.0:
  version "4.4.0"
  resolved "https://registry.npmjs.org/react-custom-scrollbars-2/-/react-custom-scrollbars-2-4.4.0.tgz"
  integrity sha512-I+oxZ9rfHfvYm85jdH2lQqpzwNr/ZAdYB8htm6R/hwRGoIEK31jq+YE6MmFwBzuO7C5zcAtH5HN9vwZxnW61NQ==
  dependencies:
    dom-css "^2.0.0"
    prop-types "^15.5.10"
    raf "^3.1.0"

"react-dom@^0.14.0 || ^15.0.0 || ^16.0.0 || ^17.0.0", "react-dom@^0.14.0 || ^15.0.1 || ^16.0.0 || ^17.0.0 || ^18.0.0", "react-dom@^16.8.0 || ^17.0.0", "react-dom@^16.8.0 || ^17.0.0-rc.1 || ^18.0.0", "react-dom@^16.8.0 || ^17.0.0-rc.1 || ^18.0.0 || ^19.0.0-rc.1", "react-dom@^17.0.0 || ^18.0.0", react-dom@^17.0.2, "react-dom@^17.0.2 || ^18.0.0", "react-dom@^17.0.2 || ^18.0.0-0", react-dom@^18.0.0, "react-dom@^18.0.0 || ^17.0.1 || ^16.7.0", react-dom@^18.2.0, react-dom@>=16.6.0, react-dom@>=16.8.0, react-dom@>=18:
  version "18.2.0"
  resolved "https://registry.npmjs.org/react-dom/-/react-dom-18.2.0.tgz"
  integrity sha512-6IMTriUmvsjHUjNtEDudZfuDQUoWXVxKHhlEGSk81n4YFS+r/Kl99wXiwlVXtPBtJenozv2P+hxDsw9eA7Xo6g==
  dependencies:
    loose-envify "^1.1.0"
    scheduler "^0.23.0"

react-is@^16.13.1:
  version "16.13.1"
  resolved "https://registry.npmjs.org/react-is/-/react-is-16.13.1.tgz"
  integrity sha512-24e6ynE2H+OKt4kqsOvNd8kBpV65zoxbA4BVsEOB3ARVWQki/DHzaUoC5KuON/BiccDaCCTZBuOcfZs70kR8bQ==

react-is@^16.7.0:
  version "16.13.1"
  resolved "https://registry.npmjs.org/react-is/-/react-is-16.13.1.tgz"
  integrity sha512-24e6ynE2H+OKt4kqsOvNd8kBpV65zoxbA4BVsEOB3ARVWQki/DHzaUoC5KuON/BiccDaCCTZBuOcfZs70kR8bQ==

react-is@^17.0.2:
  version "17.0.2"
  resolved "https://registry.npmjs.org/react-is/-/react-is-17.0.2.tgz"
  integrity sha512-w2GsyukL62IJnlaff/nRegPQR94C/XXamvMWmSHRJ4y7Ts/4ocGRmTHvOs8PSE6pB3dWOrD/nueuU5sduBsQ4w==

react-is@^18.2.0:
  version "18.2.0"
  resolved "https://registry.npmjs.org/react-is/-/react-is-18.2.0.tgz"
  integrity sha512-xWGDIW6x921xtzPkhiULtthJHoJvBbF3q26fzloPCK0hsvxtPVelvftw3zjbHWSkR2km9Z+4uxbDDK/6Zw9B8w==

react-qr-reader@^3.0.0-beta-1:
  version "3.0.0-beta-1"
  resolved "https://registry.npmjs.org/react-qr-reader/-/react-qr-reader-3.0.0-beta-1.tgz"
  integrity sha512-5HeFH9x/BlziRYQYGK2AeWS9WiKYZtGGMs9DXy3bcySTX3C9UJL9EwcPnWw8vlf7JP4FcrAlr1SnZ5nsWLQGyw==
  dependencies:
    "@zxing/browser" "0.0.7"
    "@zxing/library" "^0.18.3"
    rollup "^2.67.2"

react-remove-scroll-bar@^2.3.4:
  version "2.3.4"
  resolved "https://registry.npmjs.org/react-remove-scroll-bar/-/react-remove-scroll-bar-2.3.4.tgz"
  integrity sha512-63C4YQBUt0m6ALadE9XV56hV8BgJWDmmTPY758iIJjfQKt2nYwoUrPk0LXRXcB/yIj82T1/Ixfdpdk68LwIB0A==
  dependencies:
    react-style-singleton "^2.2.1"
    tslib "^2.0.0"

react-remove-scroll@^2.5.6:
  version "2.5.7"
  resolved "https://registry.npmjs.org/react-remove-scroll/-/react-remove-scroll-2.5.7.tgz"
  integrity sha512-FnrTWO4L7/Bhhf3CYBNArEG/yROV0tKmTv7/3h9QCFvH6sndeFf1wPqOcbFVu5VAulS5dV1wGT3GZZ/1GawqiA==
  dependencies:
    react-remove-scroll-bar "^2.3.4"
    react-style-singleton "^2.2.1"
    tslib "^2.1.0"
    use-callback-ref "^1.3.0"
    use-sidecar "^1.1.2"

react-slick@^0.29.0:
  version "0.29.0"
  resolved "https://registry.npmjs.org/react-slick/-/react-slick-0.29.0.tgz"
  integrity sha512-TGdOKE+ZkJHHeC4aaoH85m8RnFyWqdqRfAGkhd6dirmATXMZWAxOpTLmw2Ll/jPTQ3eEG7ercFr/sbzdeYCJXA==
  dependencies:
    classnames "^2.2.5"
    enquire.js "^2.1.6"
    json2mq "^0.2.0"
    lodash.debounce "^4.0.8"
    resize-observer-polyfill "^1.5.0"

react-style-singleton@^2.2.1:
  version "2.2.1"
  resolved "https://registry.npmjs.org/react-style-singleton/-/react-style-singleton-2.2.1.tgz"
  integrity sha512-ZWj0fHEMyWkHzKYUr2Bs/4zU6XLmq9HsgBURm7g5pAVfyn49DgUiNgY2d4lXRlYSiCif9YBGpQleewkcqddc7g==
  dependencies:
    get-nonce "^1.0.0"
    invariant "^2.2.4"
    tslib "^2.0.0"

react-textarea-autosize@^8.5.3:
  version "8.5.5"
  resolved "https://registry.npmjs.org/react-textarea-autosize/-/react-textarea-autosize-8.5.5.tgz"
  integrity sha512-CVA94zmfp8m4bSHtWwmANaBR8EPsKy2aZ7KwqhoS4Ftib87F9Kvi7XQhOixypPLMc6kVYgOXvKFuuzZDpHGRPg==
  dependencies:
    "@babel/runtime" "^7.20.13"
    use-composed-ref "^1.3.0"
    use-latest "^1.2.1"

react-transition-group@^4.4.2:
  version "4.4.5"
  resolved "https://registry.npmjs.org/react-transition-group/-/react-transition-group-4.4.5.tgz"
  integrity sha512-pZcd1MCJoiKiBR2NRxeCRg13uCXbydPnmB4EOeRrY7480qNWO8IIgQG6zlDkm6uRMsURXPuKq0GWtiM59a5Q6g==
  dependencies:
    "@babel/runtime" "^7.5.5"
    dom-helpers "^5.0.1"
    loose-envify "^1.4.0"
    prop-types "^15.6.2"

react-youtube@^10.1.0:
  version "10.1.0"
  resolved "https://registry.npmjs.org/react-youtube/-/react-youtube-10.1.0.tgz"
  integrity sha512-ZfGtcVpk0SSZtWCSTYOQKhfx5/1cfyEW1JN/mugGNfAxT3rmVJeMbGpA9+e78yG21ls5nc/5uZJETE3cm3knBg==
  dependencies:
    fast-deep-equal "3.1.3"
    prop-types "15.8.1"
    youtube-player "5.5.2"

"react@^0.14.0 || ^15.0.0 || ^16.0.0 || ^17.0.0", "react@^0.14.0 || ^15.0.1 || ^16.0.0 || ^17.0.0 || ^18.0.0", "react@^16.13.1 || ^17.0.0 || ^18.0.0", "react@^16.8.0 || ^17.0.0", "react@^16.8.0 || ^17.0.0 || ^18.0.0", "react@^16.8.0 || ^17.0.0-rc.1 || ^18.0.0", "react@^16.8.0 || ^17.0.0-rc.1 || ^18.0.0 || ^19.0.0-rc.1", react@^17.0.0, "react@^17.0.0 || ^18.0.0", react@^17.0.2, "react@^17.0.2 || ^18.0.0", "react@^17.0.2 || ^18.0.0-0", react@^18.0.0, "react@^18.0.0 || ^17.0.1 || ^16.7.0", react@^18.2.0, "react@>= 16.8.0 || 17.x.x || ^18.0.0-0", react@>=0.13, react@>=0.14.1, react@>=16.6.0, react@>=16.8, react@>=16.8.0, react@>=18:
  version "18.2.0"
  resolved "https://registry.npmjs.org/react/-/react-18.2.0.tgz"
  integrity sha512-/3IjMdb2L9QbBdWiW5e3P2/npwMBaU9mHCSCUzNln0ZCYbcfTsGbTJrU/kGemdH2IWmB2ioZ+zkxtmq6g09fGQ==
  dependencies:
    loose-envify "^1.1.0"

read-cache@^1.0.0:
  version "1.0.0"
  resolved "https://registry.npmjs.org/read-cache/-/read-cache-1.0.0.tgz"
  integrity sha512-Owdv/Ft7IjOgm/i0xvNDZ1LrRANRfew4b2prF3OWMQLxLfu3bS8FVhCsrSCMK4lR56Y9ya+AThoTpDCTxCmpRA==
  dependencies:
    pify "^2.3.0"

readable-stream@^2.0.2:
  version "2.3.8"
  resolved "https://registry.npmjs.org/readable-stream/-/readable-stream-2.3.8.tgz"
  integrity sha512-8p0AUk4XODgIewSi0l8Epjs+EVnWiK7NoDIEGU0HhE7+ZyY8D1IMY7odu5lRrFXGg71L15KG8QrPmum45RTtdA==
  dependencies:
    core-util-is "~1.0.0"
    inherits "~2.0.3"
    isarray "~1.0.0"
    process-nextick-args "~2.0.0"
    safe-buffer "~5.1.1"
    string_decoder "~1.1.1"
    util-deprecate "~1.0.1"

readable-stream@^3.6.0:
  version "3.6.2"
  resolved "https://registry.npmjs.org/readable-stream/-/readable-stream-3.6.2.tgz"
  integrity sha512-9u/sniCrY3D5WdsERHzHE4G2YCXqoG5FTHUiCC4SIbr6XcLZBY05ya9EKjYek9O5xOAwjGq+1JdGBAS7Q9ScoA==
  dependencies:
    inherits "^2.0.3"
    string_decoder "^1.1.1"
    util-deprecate "^1.0.1"

readable-stream@~1.0.17, readable-stream@~1.0.27-1:
  version "1.0.34"
  resolved "https://registry.npmjs.org/readable-stream/-/readable-stream-1.0.34.tgz"
  integrity sha512-ok1qVCJuRkNmvebYikljxJA/UEsKwLl2nI1OmaqAu4/UE+h0wKCHok4XkL/gvi39OacXvw59RJUOFUkDib2rHg==
  dependencies:
    core-util-is "~1.0.0"
    inherits "~2.0.1"
    isarray "0.0.1"
    string_decoder "~0.10.x"

readdirp@~3.6.0:
  version "3.6.0"
  resolved "https://registry.npmjs.org/readdirp/-/readdirp-3.6.0.tgz"
  integrity sha512-hOS089on8RduqdbhvQ5Z37A0ESjsqz6qnRcffsMU3495FuTdqSm+7bhJ29JvIOsBDEEnan5DPu9t3To9VRlMzA==
  dependencies:
    picomatch "^2.2.1"

reflect.getprototypeof@^1.0.4:
  version "1.0.4"
  resolved "https://registry.npmjs.org/reflect.getprototypeof/-/reflect.getprototypeof-1.0.4.tgz"
  integrity sha512-ECkTw8TmJwW60lOTR+ZkODISW6RQ8+2CL3COqtiJKLd6MmB45hN51HprHFziKLGkAuTGQhBb91V8cy+KHlaCjw==
  dependencies:
    call-bind "^1.0.2"
    define-properties "^1.2.0"
    es-abstract "^1.22.1"
    get-intrinsic "^1.2.1"
    globalthis "^1.0.3"
    which-builtin-type "^1.1.3"

regenerator-runtime@^0.13.7:
  version "0.13.11"
  resolved "https://registry.npmjs.org/regenerator-runtime/-/regenerator-runtime-0.13.11.tgz"
  integrity sha512-kY1AZVr2Ra+t+piVaJ4gxaFaReZVH40AKNo7UCX6W+dEwBo/2oZJzqfuN1qLq1oL45o56cPaTXELwrTh8Fpggg==

regenerator-runtime@^0.14.0:
  version "0.14.0"
  resolved "https://registry.npmjs.org/regenerator-runtime/-/regenerator-runtime-0.14.0.tgz"
  integrity sha512-srw17NI0TUWHuGa5CFGGmhfNIeja30WMBfbslPNhf6JrqQlLN5gcrvig1oqPxiVaXb0oW0XRKtH6Nngs5lKCIA==

regexp.prototype.flags@^1.5.0, regexp.prototype.flags@^1.5.1:
  version "1.5.1"
  resolved "https://registry.npmjs.org/regexp.prototype.flags/-/regexp.prototype.flags-1.5.1.tgz"
  integrity sha512-sy6TXMN+hnP/wMy+ISxg3krXx7BAtWVO4UouuCN/ziM9UEne0euamVNafDfvC83bRNr95y0V5iijeDQFUNpvrg==
  dependencies:
    call-bind "^1.0.2"
    define-properties "^1.2.0"
    set-function-name "^2.0.0"

regexpp@^3.2.0:
  version "3.2.0"
  resolved "https://registry.npmjs.org/regexpp/-/regexpp-3.2.0.tgz"
  integrity sha512-pq2bWo9mVD43nbts2wGv17XLiNLya+GklZ8kaDLV2Z08gDCsGpnKn9BFMepvWuHCbyVvY7J5o5+BVvoQbmlJLg==

resize-observer-polyfill@^1.5.0:
  version "1.5.1"
  resolved "https://registry.npmjs.org/resize-observer-polyfill/-/resize-observer-polyfill-1.5.1.tgz"
  integrity sha512-LwZrotdHOo12nQuZlHEmtuXdqGoOD0OhaxopaNFxWzInpEgaLWoVuAMbTzixuosCx2nEG58ngzW3vxdWoxIgdg==

resolve-from@^4.0.0:
  version "4.0.0"
  resolved "https://registry.npmjs.org/resolve-from/-/resolve-from-4.0.0.tgz"
  integrity sha512-pb/MYmXstAkysRFx8piNI1tGFNQIFA3vkE3Gq4EuA1dF6gHp/+vgZqsCGJapvy8N3Q+4o7FwvquPJcnZ7RYy4g==

resolve@^1.1.7, resolve@^1.19.0, resolve@^1.22.0, resolve@^1.22.4, resolve@^1.22.8:
  version "1.22.8"
  resolved "https://registry.npmjs.org/resolve/-/resolve-1.22.8.tgz"
  integrity sha512-oKWePCxqpd6FlLvGV1VU0x7bkPmmCNolxzjMf4NczoDnQcIWrAF+cPtZn5i6n+RfD2d9i0tzpKnG6Yk168yIyw==
  dependencies:
    is-core-module "^2.13.0"
    path-parse "^1.0.7"
    supports-preserve-symlinks-flag "^1.0.0"

resolve@^2.0.0-next.4:
  version "2.0.0-next.5"
  resolved "https://registry.npmjs.org/resolve/-/resolve-2.0.0-next.5.tgz"
  integrity sha512-U7WjGVG9sH8tvjW5SmGbQuui75FiyjAX72HX15DwBBwF9dNiQZRQAg9nnPhYy+TUnE0+VcrttuvNI8oSxZcocA==
  dependencies:
    is-core-module "^2.13.0"
    path-parse "^1.0.7"
    supports-preserve-symlinks-flag "^1.0.0"

reusify@^1.0.4:
  version "1.0.4"
  resolved "https://registry.npmjs.org/reusify/-/reusify-1.0.4.tgz"
  integrity sha512-U9nH88a3fc/ekCF1l0/UP1IosiuIjyTh7hBvXVMHYgVcfGvt897Xguj2UOLDeI5BG2m7/uwyaLVT6fbtCwTyzw==

rgbcolor@^1.0.1:
  version "1.0.1"
  resolved "https://registry.npmjs.org/rgbcolor/-/rgbcolor-1.0.1.tgz"
  integrity sha512-9aZLIrhRaD97sgVhtJOW6ckOEh6/GnvQtdVNfdZ6s67+3/XwLS9lBcQYzEEhYVeUowN7pRzMLsyGhK2i/xvWbw==

rifm@^0.12.1:
  version "0.12.1"
  resolved "https://registry.npmjs.org/rifm/-/rifm-0.12.1.tgz"
  integrity sha512-OGA1Bitg/dSJtI/c4dh90svzaUPt228kzFsUkJbtA2c964IqEAwWXeL9ZJi86xWv3j5SMqRvGULl7bA6cK0Bvg==

rimraf@^3.0.2:
  version "3.0.2"
  resolved "https://registry.npmjs.org/rimraf/-/rimraf-3.0.2.tgz"
  integrity sha512-JZkJMZkAGFFPP2YqXZXPbMlMBgsxzE8ILs4lMIX/2o0L9UBw9O/Y3o6wFw/i9YLapcUJWwqbi3kdxIPdC62TIA==
  dependencies:
    glob "^7.1.3"

rollup@^2.67.2:
  version "2.79.1"
  resolved "https://registry.npmjs.org/rollup/-/rollup-2.79.1.tgz"
  integrity sha512-uKxbd0IhMZOhjAiD5oAFp7BqvkA4Dv47qpOCtaNvng4HBwdbWtdOh8f5nZNuk2rp51PMGk3bzfWu5oayNEuYnw==
  optionalDependencies:
    fsevents "~2.3.2"

run-parallel@^1.1.9:
  version "1.2.0"
  resolved "https://registry.npmjs.org/run-parallel/-/run-parallel-1.2.0.tgz"
  integrity sha512-5l4VyZR86LZ/lDxZTR6jqL8AFE2S0IFLMP26AbjsLVADxHdhB/c0GUsH+y39UfCi3dzz8OlQuPmnaJOMoDHQBA==
  dependencies:
    queue-microtask "^1.2.2"

safe-array-concat@^1.0.1:
  version "1.0.1"
  resolved "https://registry.npmjs.org/safe-array-concat/-/safe-array-concat-1.0.1.tgz"
  integrity sha512-6XbUAseYE2KtOuGueyeobCySj9L4+66Tn6KQMOPQJrAJEowYKW/YR/MGJZl7FdydUdaFu4LYyDZjxf4/Nmo23Q==
  dependencies:
    call-bind "^1.0.2"
    get-intrinsic "^1.2.1"
    has-symbols "^1.0.3"
    isarray "^2.0.5"

safe-buffer@^5.0.1, safe-buffer@^5.1.0, safe-buffer@~5.2.0:
  version "5.2.1"
  resolved "https://registry.npmjs.org/safe-buffer/-/safe-buffer-5.2.1.tgz"
  integrity sha512-rp3So07KcdmmKbGvgaNxQSJr7bGVSVk5S9Eq1F+ppbRo70+YeaDxkw5Dd8NPN+GD6bjnYm2VuPuCXmpuYvmCXQ==

safe-buffer@~5.1.0, safe-buffer@~5.1.1:
  version "5.1.2"
  resolved "https://registry.npmjs.org/safe-buffer/-/safe-buffer-5.1.2.tgz"
  integrity sha512-Gd2UZBJDkXlY7GbJxfsE8/nvKkUEU1G38c1siN6QP6a9PT9MmHB8GnpscSmMJSoF8LOIrt8ud/wPtojys4G6+g==

safe-regex-test@^1.0.0:
  version "1.0.0"
  resolved "https://registry.npmjs.org/safe-regex-test/-/safe-regex-test-1.0.0.tgz"
  integrity sha512-JBUUzyOgEwXQY1NuPtvcj/qcBDbDmEvWufhlnXZIm75DEHp+afM1r1ujJpJsV/gSM4t59tpDyPi1sd6ZaPFfsA==
  dependencies:
    call-bind "^1.0.2"
    get-intrinsic "^1.1.3"
    is-regex "^1.1.4"

scheduler@^0.23.0:
  version "0.23.0"
  resolved "https://registry.npmjs.org/scheduler/-/scheduler-0.23.0.tgz"
  integrity sha512-CtuThmgHNg7zIZWAXi3AsyIzA3n4xx7aNyjwC2VJldO2LMVDhFK+63xGqq6CsJH4rTAt6/M+N4GhZiDYPx9eUw==
  dependencies:
    loose-envify "^1.1.0"

schema-utils@^3.0.0, schema-utils@^3.1.1, schema-utils@^3.2.0:
  version "3.3.0"
  resolved "https://registry.npmjs.org/schema-utils/-/schema-utils-3.3.0.tgz"
  integrity sha512-pN/yOAvcC+5rQ5nERGuwrjLlYvLTbCibnZ1I7B1LaiAz9BRBlE9GMgE/eqV30P7aJQUf7Ddimy/RsbYO/GrVGg==
  dependencies:
    "@types/json-schema" "^7.0.8"
    ajv "^6.12.5"
    ajv-keywords "^3.5.2"

scroll-into-view-if-needed@^2.2.20:
  version "2.2.31"
  resolved "https://registry.npmjs.org/scroll-into-view-if-needed/-/scroll-into-view-if-needed-2.2.31.tgz"
  integrity sha512-dGCXy99wZQivjmjIqihaBQNjryrz5rueJY7eHfTdyWEiR4ttYpsajb14rn9s5d4DY4EcY6+4+U/maARBXJedkA==
  dependencies:
    compute-scroll-into-view "^1.0.20"

scroll-into-view-if-needed@3.0.10:
  version "3.0.10"
  resolved "https://registry.npmjs.org/scroll-into-view-if-needed/-/scroll-into-view-if-needed-3.0.10.tgz"
  integrity sha512-t44QCeDKAPf1mtQH3fYpWz8IM/DyvHLjs8wUvvwMYxk5moOqCzrMSxK6HQVD0QVmVjXFavoFIPRVrMuJPKAvtg==
  dependencies:
    compute-scroll-into-view "^3.0.2"

semver@^6.0.0, semver@^6.3.0, semver@^6.3.1:
  version "6.3.1"
  resolved "https://registry.npmjs.org/semver/-/semver-6.3.1.tgz"
  integrity sha512-BR7VvDCVHO+q2xBEWskxS6DJE1qRnb7DxzUrogb71CWoSficBxYsiAGd+Kl0mmq/MprG9yArRkyrQxTO6XjMzA==

semver@^7.3.5:
  version "7.5.4"
  resolved "https://registry.npmjs.org/semver/-/semver-7.5.4.tgz"
  integrity sha512-1bCSESV6Pv+i21Hvpxp3Dx+pSD8lIPt8uVjRrxAUt/nbswYc+tK6Y2btiULjd4+fnq15PX+nqQDC7Oft7WkwcA==
  dependencies:
    lru-cache "^6.0.0"

semver@^7.3.7:
  version "7.5.4"
  resolved "https://registry.npmjs.org/semver/-/semver-7.5.4.tgz"
  integrity sha512-1bCSESV6Pv+i21Hvpxp3Dx+pSD8lIPt8uVjRrxAUt/nbswYc+tK6Y2btiULjd4+fnq15PX+nqQDC7Oft7WkwcA==
  dependencies:
    lru-cache "^6.0.0"

semver@^7.5.4:
  version "7.5.4"
  resolved "https://registry.npmjs.org/semver/-/semver-7.5.4.tgz"
  integrity sha512-1bCSESV6Pv+i21Hvpxp3Dx+pSD8lIPt8uVjRrxAUt/nbswYc+tK6Y2btiULjd4+fnq15PX+nqQDC7Oft7WkwcA==
  dependencies:
    lru-cache "^6.0.0"

serialize-javascript@^6.0.1:
  version "6.0.2"
  resolved "https://registry.npmjs.org/serialize-javascript/-/serialize-javascript-6.0.2.tgz"
  integrity sha512-Saa1xPByTTq2gdeFZYLLo+RFE35NHZkAbqZeWNd3BpzppeVisAqpDjcp8dyf6uIvEqJRd46jemmyA4iFIeVk8g==
  dependencies:
    randombytes "^2.1.0"

set-blocking@^2.0.0:
  version "2.0.0"
  resolved "https://registry.npmjs.org/set-blocking/-/set-blocking-2.0.0.tgz"
  integrity sha512-KiKBS8AnWGEyLzofFfmvKwpdPzqiy16LvQfK3yv/fVH7Bj13/wl3JSR1J+rfgRE9q7xUJK4qvgS8raSOeLUehw==

set-function-length@^1.1.1:
  version "1.1.1"
  resolved "https://registry.npmjs.org/set-function-length/-/set-function-length-1.1.1.tgz"
  integrity sha512-VoaqjbBJKiWtg4yRcKBQ7g7wnGnLV3M8oLvVWwOk2PdYY6PEFegR1vezXR0tw6fZGF9csVakIRjrJiy2veSBFQ==
  dependencies:
    define-data-property "^1.1.1"
    get-intrinsic "^1.2.1"
    gopd "^1.0.1"
    has-property-descriptors "^1.0.0"

set-function-name@^2.0.0, set-function-name@^2.0.1:
  version "2.0.1"
  resolved "https://registry.npmjs.org/set-function-name/-/set-function-name-2.0.1.tgz"
  integrity sha512-tMNCiqYVkXIZgc2Hnoy2IvC/f8ezc5koaRFkCjrpWzGpCd3qbZXPzVy9MAZzK1ch/X0jvSkojys3oqJN0qCmdA==
  dependencies:
    define-data-property "^1.0.1"
    functions-have-names "^1.2.3"
    has-property-descriptors "^1.0.0"

shebang-command@^2.0.0:
  version "2.0.0"
  resolved "https://registry.npmjs.org/shebang-command/-/shebang-command-2.0.0.tgz"
  integrity sha512-kHxr2zZpYtdmrN1qDjrrX/Z1rR1kG8Dx+gkpK1G4eXmvXswmcE1hTWBWYUzlraYw1/yZp6YuDY77YtvbN0dmDA==
  dependencies:
    shebang-regex "^3.0.0"

shebang-regex@^3.0.0:
  version "3.0.0"
  resolved "https://registry.npmjs.org/shebang-regex/-/shebang-regex-3.0.0.tgz"
  integrity sha512-7++dFhtcx3353uBaq8DDR4NuxBetBzC7ZQOhmTQInHEd6bSrXdiEyzCvG07Z44UYdLShWUyXt5M/yhz8ekcb1A==

side-channel@^1.0.4:
  version "1.0.4"
  resolved "https://registry.npmjs.org/side-channel/-/side-channel-1.0.4.tgz"
  integrity sha512-q5XPytqFEIKHkGdiMIrY10mvLRvnQh42/+GoBlFW3b2LXLE2xxJpZFdm94we0BaoV3RwJyGqg5wS7epxTv0Zvw==
  dependencies:
    call-bind "^1.0.0"
    get-intrinsic "^1.0.2"
    object-inspect "^1.9.0"

signal-exit@^3.0.0:
  version "3.0.7"
  resolved "https://registry.npmjs.org/signal-exit/-/signal-exit-3.0.7.tgz"
  integrity sha512-wnD2ZE+l+SPC/uoS0vXeE9L1+0wuaMqKlfz9AMUo38JsyLSBWSFcHR1Rri62LZc12vLr1gb3jl7iwQhgwpAbGQ==

signal-exit@^4.0.1:
  version "4.1.0"
  resolved "https://registry.npmjs.org/signal-exit/-/signal-exit-4.1.0.tgz"
  integrity sha512-bzyZ1e88w9O1iNJbKnOlvYTrWPDl46O1bG0D3XInv+9tkPrxrN8jUUTiFlDkkmKWgn1M6CfIA13SuGqOa9Korw==

simple-swizzle@^0.2.2:
  version "0.2.2"
  resolved "https://registry.npmjs.org/simple-swizzle/-/simple-swizzle-0.2.2.tgz"
  integrity sha512-JA//kQgZtbuY83m+xT+tXJkmJncGMTFT+C+g2h2R9uxkYIrE2yy9sgmcLhCnw57/WSD+Eh3J97FPEDFnbXnDUg==
  dependencies:
    is-arrayish "^0.3.1"

sister@^3.0.0:
  version "3.0.2"
  resolved "https://registry.npmjs.org/sister/-/sister-3.0.2.tgz"
  integrity sha512-p19rtTs+NksBRKW9qn0UhZ8/TUI9BPw9lmtHny+Y3TinWlOa9jWh9xB0AtPSdmOy49NJJJSSe0Ey4C7h0TrcYA==

slash@^3.0.0:
  version "3.0.0"
  resolved "https://registry.npmjs.org/slash/-/slash-3.0.0.tgz"
  integrity sha512-g9Q1haeby36OSStwb4ntCGGGaKsaVSjQ68fBxoQcutl5fS1vuY18H3wSt3jFyFtrkx+Kz0V1G85A4MyAdDMi2Q==

slate-react@^0.98.3:
  version "0.98.4"
  resolved "https://registry.npmjs.org/slate-react/-/slate-react-0.98.4.tgz"
  integrity sha512-8Of3v9hFuX8rIRc86LuuBhU9t8ps+9ARKL4yyhCrKQYZ93Ep/LFA3GvPGvtf3zYuVadZ8tkhRH8tbHOGNAndLw==
  dependencies:
    "@juggle/resize-observer" "^3.4.0"
    "@types/is-hotkey" "^0.1.1"
    "@types/lodash" "^4.14.149"
    direction "^1.0.3"
    is-hotkey "^0.1.6"
    is-plain-object "^5.0.0"
    lodash "^4.17.4"
    scroll-into-view-if-needed "^2.2.20"
    tiny-invariant "1.0.6"

slate@^0.94.1, slate@>=0.65.3:
  version "0.94.1"
  resolved "https://registry.npmjs.org/slate/-/slate-0.94.1.tgz"
  integrity sha512-GH/yizXr1ceBoZ9P9uebIaHe3dC/g6Plpf9nlUwnvoyf6V1UOYrRwkabtOCd3ZfIGxomY4P7lfgLr7FPH8/BKA==
  dependencies:
    immer "^9.0.6"
    is-plain-object "^5.0.0"
    tiny-warning "^1.0.3"

slick-carousel@^1.8.1:
  version "1.8.1"
  resolved "https://registry.npmjs.org/slick-carousel/-/slick-carousel-1.8.1.tgz"
  integrity sha512-XB9Ftrf2EEKfzoQXt3Nitrt/IPbT+f1fgqBdoxO3W/+JYvtEOW6EgxnWfr9GH6nmULv7Y2tPmEX3koxThVmebA==

source-map-js@^1.0.1, source-map-js@^1.2.1:
  version "1.2.1"
  resolved "https://registry.npmjs.org/source-map-js/-/source-map-js-1.2.1.tgz"
  integrity sha512-UXWMKhLOwVKb728IUtQPXxfYU+usdybtUrK/8uGE8CQMvrhOpwvzDBwj0QhSL7MQc7vIsISBG8VQ8+IDQxpfQA==

source-map-support@~0.5.20:
  version "0.5.21"
  resolved "https://registry.npmjs.org/source-map-support/-/source-map-support-0.5.21.tgz"
  integrity sha512-uBHU3L3czsIyYXKX88fdrGovxdSCoTGDRZ6SYXtSRxLZUzHg5P/66Ht6uoUlHu9EZod+inXhKo3qQgwXUT/y1w==
  dependencies:
    buffer-from "^1.0.0"
    source-map "^0.6.0"

source-map@^0.5.7:
  version "0.5.7"
  resolved "https://registry.npmjs.org/source-map/-/source-map-0.5.7.tgz"
  integrity sha512-LbrmJOMUSdEVxIKvdcJzQC+nQhe8FUZQTXQy6+I75skNgn3OoQ0DZA8YnFa7gp8tqtL3KPf1kmo0R5DoApeSGQ==

source-map@^0.6.0:
  version "0.6.1"
  resolved "https://registry.npmjs.org/source-map/-/source-map-0.6.1.tgz"
  integrity sha512-UjgapumWlbMhkBgzT7Ykc5YXUT46F0iKu8SGXq0bcwP5dz/h0Plj6enJqjz1Zbq2l5WaqYnrVbwWOWMyF3F47g==

stackblur-canvas@^2.0.0:
  version "2.6.0"
  resolved "https://registry.npmjs.org/stackblur-canvas/-/stackblur-canvas-2.6.0.tgz"
  integrity sha512-8S1aIA+UoF6erJYnglGPug6MaHYGo1Ot7h5fuXx4fUPvcvQfcdw2o/ppCse63+eZf8PPidSu4v1JnmEVtEDnpg==

string_decoder@^1.1.1:
  version "1.3.0"
  resolved "https://registry.npmjs.org/string_decoder/-/string_decoder-1.3.0.tgz"
  integrity sha512-hkRX8U1WjJFd8LsDJ2yQ/wWWxaopEsABU1XfkM8A+j0+85JAGppt16cr1Whg6KIbb4okU6Mql6BOj+uup/wKeA==
  dependencies:
    safe-buffer "~5.2.0"

string_decoder@~0.10.x:
  version "0.10.31"
  resolved "https://registry.npmjs.org/string_decoder/-/string_decoder-0.10.31.tgz"
  integrity sha512-ev2QzSzWPYmy9GuqfIVildA4OdcGLeFZQrq5ys6RtiuF+RQQiZWr8TZNyAcuVXyQRYfEO+MsoB/1BuQVhOJuoQ==

string_decoder@~1.1.1:
  version "1.1.1"
  resolved "https://registry.npmjs.org/string_decoder/-/string_decoder-1.1.1.tgz"
  integrity sha512-n/ShnvDi6FHbbVfviro+WojiFzv+s8MPMHBczVePfUpDJLwoLT0ht1l4YwBCbi8pJAveEEdnkHyPyTP/mzRfwg==
  dependencies:
    safe-buffer "~5.1.0"

string-convert@^0.2.0:
  version "0.2.1"
  resolved "https://registry.npmjs.org/string-convert/-/string-convert-0.2.1.tgz"
  integrity sha512-u/1tdPl4yQnPBjnVrmdLo9gtuLvELKsAoRapekWggdiQNvvvum+jYF329d84NAa660KQw7pB2n36KrIKVoXa3A==

"string-width-cjs@npm:string-width@^4.2.0":
  version "4.2.3"
  resolved "https://registry.npmjs.org/string-width/-/string-width-4.2.3.tgz"
  integrity sha512-wKyQRQpjJ0sIp62ErSZdGsjMJWsap5oRNihHhu6G7JVO/9jIB6UyevL+tXuOqrng8j/cxKTWyWUwvSTriiZz/g==
  dependencies:
    emoji-regex "^8.0.0"
    is-fullwidth-code-point "^3.0.0"
    strip-ansi "^6.0.1"

"string-width@^1.0.2 || 2 || 3 || 4", string-width@^4.1.0, string-width@^4.2.3:
  version "4.2.3"
  resolved "https://registry.npmjs.org/string-width/-/string-width-4.2.3.tgz"
  integrity sha512-wKyQRQpjJ0sIp62ErSZdGsjMJWsap5oRNihHhu6G7JVO/9jIB6UyevL+tXuOqrng8j/cxKTWyWUwvSTriiZz/g==
  dependencies:
    emoji-regex "^8.0.0"
    is-fullwidth-code-point "^3.0.0"
    strip-ansi "^6.0.1"

string-width@^5.0.1:
  version "5.1.2"
  resolved "https://registry.npmjs.org/string-width/-/string-width-5.1.2.tgz"
  integrity sha512-HnLOCR3vjcY8beoNLtcjZ5/nxn2afmME6lhrDrebokqMap+XbeW8n9TXpPDOqdGK5qcI3oT0GKTW6wC7EMiVqA==
  dependencies:
    eastasianwidth "^0.2.0"
    emoji-regex "^9.2.2"
    strip-ansi "^7.0.1"

string-width@^5.1.2:
  version "5.1.2"
  resolved "https://registry.npmjs.org/string-width/-/string-width-5.1.2.tgz"
  integrity sha512-HnLOCR3vjcY8beoNLtcjZ5/nxn2afmME6lhrDrebokqMap+XbeW8n9TXpPDOqdGK5qcI3oT0GKTW6wC7EMiVqA==
  dependencies:
    eastasianwidth "^0.2.0"
    emoji-regex "^9.2.2"
    strip-ansi "^7.0.1"

string.prototype.matchall@^4.0.8:
  version "4.0.10"
  resolved "https://registry.npmjs.org/string.prototype.matchall/-/string.prototype.matchall-4.0.10.tgz"
  integrity sha512-rGXbGmOEosIQi6Qva94HUjgPs9vKW+dkG7Y8Q5O2OYkWL6wFaTRZO8zM4mhP94uX55wgyrXzfS2aGtGzUL7EJQ==
  dependencies:
    call-bind "^1.0.2"
    define-properties "^1.2.0"
    es-abstract "^1.22.1"
    get-intrinsic "^1.2.1"
    has-symbols "^1.0.3"
    internal-slot "^1.0.5"
    regexp.prototype.flags "^1.5.0"
    set-function-name "^2.0.0"
    side-channel "^1.0.4"

string.prototype.trim@^1.2.8:
  version "1.2.8"
  resolved "https://registry.npmjs.org/string.prototype.trim/-/string.prototype.trim-1.2.8.tgz"
  integrity sha512-lfjY4HcixfQXOfaqCvcBuOIapyaroTXhbkfJN3gcB1OtyupngWK4sEET9Knd0cXd28kTUqu/kHoV4HKSJdnjiQ==
  dependencies:
    call-bind "^1.0.2"
    define-properties "^1.2.0"
    es-abstract "^1.22.1"

string.prototype.trimend@^1.0.7:
  version "1.0.7"
  resolved "https://registry.npmjs.org/string.prototype.trimend/-/string.prototype.trimend-1.0.7.tgz"
  integrity sha512-Ni79DqeB72ZFq1uH/L6zJ+DKZTkOtPIHovb3YZHQViE+HDouuU4mBrLOLDn5Dde3RF8qw5qVETEjhu9locMLvA==
  dependencies:
    call-bind "^1.0.2"
    define-properties "^1.2.0"
    es-abstract "^1.22.1"

string.prototype.trimstart@^1.0.7:
  version "1.0.7"
  resolved "https://registry.npmjs.org/string.prototype.trimstart/-/string.prototype.trimstart-1.0.7.tgz"
  integrity sha512-NGhtDFu3jCEm7B4Fy0DpLewdJQOZcQ0rGbwQ/+stjnrp2i+rlKeCvos9hOIeCmqwratM47OBxY7uFZzjxHXmrg==
  dependencies:
    call-bind "^1.0.2"
    define-properties "^1.2.0"
    es-abstract "^1.22.1"

"strip-ansi-cjs@npm:strip-ansi@^6.0.1":
  version "6.0.1"
  resolved "https://registry.npmjs.org/strip-ansi/-/strip-ansi-6.0.1.tgz"
  integrity sha512-Y38VPSHcqkFrCpFnQ9vuSXmquuv5oXOKpGeT6aGrr3o3Gc9AlVa6JBfUSOCnbxGGZF+/0ooI7KrPuUSztUdU5A==
  dependencies:
    ansi-regex "^5.0.1"

strip-ansi@^6.0.0, strip-ansi@^6.0.1:
  version "6.0.1"
  resolved "https://registry.npmjs.org/strip-ansi/-/strip-ansi-6.0.1.tgz"
  integrity sha512-Y38VPSHcqkFrCpFnQ9vuSXmquuv5oXOKpGeT6aGrr3o3Gc9AlVa6JBfUSOCnbxGGZF+/0ooI7KrPuUSztUdU5A==
  dependencies:
    ansi-regex "^5.0.1"

strip-ansi@^7.0.1:
  version "7.1.0"
  resolved "https://registry.npmjs.org/strip-ansi/-/strip-ansi-7.1.0.tgz"
  integrity sha512-iq6eVVI64nQQTRYq2KtEg2d2uU7LElhTJwsH4YzIHZshxlgZms/wIc4VoDQTlG/IvVIrBKG06CrZnp0qv7hkcQ==
  dependencies:
    ansi-regex "^6.0.1"

strip-bom@^3.0.0:
  version "3.0.0"
  resolved "https://registry.npmjs.org/strip-bom/-/strip-bom-3.0.0.tgz"
  integrity sha512-vavAMRXOgBVNF6nyEEmL3DBK19iRpDcoIwW+swQ+CbGiu7lju6t+JklA1MHweoWtadgt4ISVUsXLyDq34ddcwA==

strip-json-comments@^3.1.0, strip-json-comments@^3.1.1:
  version "3.1.1"
  resolved "https://registry.npmjs.org/strip-json-comments/-/strip-json-comments-3.1.1.tgz"
  integrity sha512-6fPc+R4ihwqP6N/aIv2f1gMH8lOVtWQHoqC4yK6oSDVVocumAsfCqjkXnqiYMhmMwS/mEHLp7Vehlt3ql6lEig==

styled-jsx@5.0.2:
  version "5.0.2"
  resolved "https://registry.npmjs.org/styled-jsx/-/styled-jsx-5.0.2.tgz"
  integrity sha512-LqPQrbBh3egD57NBcHET4qcgshPks+yblyhPlH2GY8oaDgKs8SK4C3dBh3oSJjgzJ3G5t1SYEZGHkP+QEpX9EQ==

stylis@4.0.13:
  version "4.0.13"
  resolved "https://registry.npmjs.org/stylis/-/stylis-4.0.13.tgz"
  integrity sha512-xGPXiFVl4YED9Jh7Euv2V220mriG9u4B2TA6Ybjc1catrstKD2PpIdU3U0RKpkVBC2EhmL/F0sPCr9vrFTNRag==

stylis@4.2.0:
  version "4.2.0"
  resolved "https://registry.npmjs.org/stylis/-/stylis-4.2.0.tgz"
  integrity sha512-Orov6g6BB1sDfYgzWfTHDOxamtX1bE/zo104Dh9e6fqJ3PooipYyfJ0pUmrZO2wAvO8YbEyeFrkV91XTsGMSrw==

sucrase@^3.35.0:
  version "3.35.0"
  resolved "https://registry.npmjs.org/sucrase/-/sucrase-3.35.0.tgz"
  integrity sha512-8EbVDiu9iN/nESwxeSxDKe0dunta1GOlHufmSSXxMD2z2/tMZpDMpvXQGsc+ajGo8y2uYUmixaSRUc/QPoQ0GA==
  dependencies:
    "@jridgewell/gen-mapping" "^0.3.2"
    commander "^4.0.0"
    glob "^10.3.10"
    lines-and-columns "^1.1.6"
    mz "^2.7.0"
    pirates "^4.0.1"
    ts-interface-checker "^0.1.9"

supports-color@^5.3.0:
  version "5.5.0"
  resolved "https://registry.npmjs.org/supports-color/-/supports-color-5.5.0.tgz"
  integrity sha512-QjVjwdXIt408MIiAqCX4oUKsgU2EqAGzs2Ppkm4aQYbjm+ZEWEcW4SfFNTr4uMNZma0ey4f5lgLrkB0aX0QMow==
  dependencies:
    has-flag "^3.0.0"

supports-color@^7.1.0:
  version "7.2.0"
  resolved "https://registry.npmjs.org/supports-color/-/supports-color-7.2.0.tgz"
  integrity sha512-qpCAvRl9stuOHveKsn7HncJRvv501qIacKzQlO/+Lwxc9+0q2wLyv4Dfvt80/DPn2pqOBsJdDiogXGR9+OvwRw==
  dependencies:
    has-flag "^4.0.0"

supports-color@^8.0.0:
  version "8.1.1"
  resolved "https://registry.npmjs.org/supports-color/-/supports-color-8.1.1.tgz"
  integrity sha512-MpUEN2OodtUzxvKQl72cUF7RQ5EiHsGvSsVG0ia9c5RbWGL2CI4C7EpPS8UTBIplnlzZiNuV56w+FuNxy3ty2Q==
  dependencies:
    has-flag "^4.0.0"

supports-preserve-symlinks-flag@^1.0.0:
  version "1.0.0"
  resolved "https://registry.npmjs.org/supports-preserve-symlinks-flag/-/supports-preserve-symlinks-flag-1.0.0.tgz"
  integrity sha512-ot0WnXS9fgdkgIcePe6RHNk1WA8+muPa6cSjeR3V8K27q9BB1rTE3R1p7Hv0z1ZyAc8s6Vvv8DIyWf681MAt0w==

svg-pathdata@^6.0.3:
  version "6.0.3"
  resolved "https://registry.npmjs.org/svg-pathdata/-/svg-pathdata-6.0.3.tgz"
  integrity sha512-qsjeeq5YjBZ5eMdFuUa4ZosMLxgr5RZ+F+Y1OrDhuOCEInRMA3x74XdBtggJcj9kOeInz0WE+LgCPDkZFlBYJw==

svg.draggable.js@^2.2.2:
  version "2.2.2"
  resolved "https://registry.npmjs.org/svg.draggable.js/-/svg.draggable.js-2.2.2.tgz"
  integrity sha512-JzNHBc2fLQMzYCZ90KZHN2ohXL0BQJGQimK1kGk6AvSeibuKcIdDX9Kr0dT9+UJ5O8nYA0RB839Lhvk4CY4MZw==
  dependencies:
    svg.js "^2.0.1"

svg.easing.js@^2.0.0:
  version "2.0.0"
  resolved "https://registry.npmjs.org/svg.easing.js/-/svg.easing.js-2.0.0.tgz"
  integrity sha512-//ctPdJMGy22YoYGV+3HEfHbm6/69LJUTAqI2/5qBvaNHZ9uUFVC82B0Pl299HzgH13rKrBgi4+XyXXyVWWthA==
  dependencies:
    svg.js ">=2.3.x"

svg.filter.js@^2.0.2:
  version "2.0.2"
  resolved "https://registry.npmjs.org/svg.filter.js/-/svg.filter.js-2.0.2.tgz"
  integrity sha512-xkGBwU+dKBzqg5PtilaTb0EYPqPfJ9Q6saVldX+5vCRy31P6TlRCP3U9NxH3HEufkKkpNgdTLBJnmhDHeTqAkw==
  dependencies:
    svg.js "^2.2.5"

svg.js@^2.0.1, svg.js@^2.2.5, svg.js@^2.4.0, svg.js@^2.6.5, svg.js@>=2.3.x:
  version "2.7.1"
  resolved "https://registry.npmjs.org/svg.js/-/svg.js-2.7.1.tgz"
  integrity sha512-ycbxpizEQktk3FYvn/8BH+6/EuWXg7ZpQREJvgacqn46gIddG24tNNe4Son6omdXCnSOaApnpZw6MPCBA1dODA==

svg.pathmorphing.js@^0.1.3:
  version "0.1.3"
  resolved "https://registry.npmjs.org/svg.pathmorphing.js/-/svg.pathmorphing.js-0.1.3.tgz"
  integrity sha512-49HWI9X4XQR/JG1qXkSDV8xViuTLIWm/B/7YuQELV5KMOPtXjiwH4XPJvr/ghEDibmLQ9Oc22dpWpG0vUDDNww==
  dependencies:
    svg.js "^2.4.0"

svg.resize.js@^1.4.3:
  version "1.4.3"
  resolved "https://registry.npmjs.org/svg.resize.js/-/svg.resize.js-1.4.3.tgz"
  integrity sha512-9k5sXJuPKp+mVzXNvxz7U0uC9oVMQrrf7cFsETznzUDDm0x8+77dtZkWdMfRlmbkEEYvUn9btKuZ3n41oNA+uw==
  dependencies:
    svg.js "^2.6.5"
    svg.select.js "^2.1.2"

svg.select.js@^2.1.2:
  version "2.1.2"
  resolved "https://registry.npmjs.org/svg.select.js/-/svg.select.js-2.1.2.tgz"
  integrity sha512-tH6ABEyJsAOVAhwcCjF8mw4crjXSI1aa7j2VQR8ZuJ37H2MBUbyeqYr5nEO7sSN3cy9AR9DUwNg0t/962HlDbQ==
  dependencies:
    svg.js "^2.2.5"

svg.select.js@^3.0.1:
  version "3.0.1"
  resolved "https://registry.npmjs.org/svg.select.js/-/svg.select.js-3.0.1.tgz"
  integrity sha512-h5IS/hKkuVCbKSieR9uQCj9w+zLHoPh+ce19bBYyqF53g6mnPB8sAtIbe1s9dh2S2fCmYX2xel1Ln3PJBbK4kw==
  dependencies:
    svg.js "^2.6.5"

sweetalert2@^11.7.27:
  version "11.7.32"
  resolved "https://registry.npmjs.org/sweetalert2/-/sweetalert2-11.7.32.tgz"
  integrity sha512-44tNNe2oLe7T94mT6dus4hc9G7qg6jZU/K5qZzpNS6e5HGPrSF6Kie6oZ7B5puIJydB34V2h/8f5EhIFivYo4A==

tailwind-merge@^1.14.0:
  version "1.14.0"
  resolved "https://registry.npmjs.org/tailwind-merge/-/tailwind-merge-1.14.0.tgz"
  integrity sha512-3mFKyCo/MBcgyOTlrY8T7odzZFx+w+qKSMAmdFzRvqBfLlSigU6TZnlFHK0lkMwj9Bj8OYU+9yW9lmGuS0QEnQ==

tailwind-variants@^0.1.20:
  version "0.1.20"
  resolved "https://registry.npmjs.org/tailwind-variants/-/tailwind-variants-0.1.20.tgz"
  integrity sha512-AMh7x313t/V+eTySKB0Dal08RHY7ggYK0MSn/ad8wKWOrDUIzyiWNayRUm2PIJ4VRkvRnfNuyRuKbLV3EN+ewQ==
  dependencies:
    tailwind-merge "^1.14.0"

tailwindcss@*, tailwindcss@^3.4.16, tailwindcss@>=3.4.0:
  version "3.4.16"
  resolved "https://registry.npmjs.org/tailwindcss/-/tailwindcss-3.4.16.tgz"
  integrity sha512-TI4Cyx7gDiZ6r44ewaJmt0o6BrMCT5aK5e0rmJ/G9Xq3w7CX/5VXl/zIPEJZFUK5VEqwByyhqNPycPlvcK4ZNw==
  dependencies:
    "@alloc/quick-lru" "^5.2.0"
    arg "^5.0.2"
    chokidar "^3.6.0"
    didyoumean "^1.2.2"
    dlv "^1.1.3"
    fast-glob "^3.3.2"
    glob-parent "^6.0.2"
    is-glob "^4.0.3"
    jiti "^1.21.6"
    lilconfig "^3.1.3"
    micromatch "^4.0.8"
    normalize-path "^3.0.0"
    object-hash "^3.0.0"
    picocolors "^1.1.1"
    postcss "^8.4.47"
    postcss-import "^15.1.0"
    postcss-js "^4.0.1"
    postcss-load-config "^4.0.2"
    postcss-nested "^6.2.0"
    postcss-selector-parser "^6.1.2"
    resolve "^1.22.8"
    sucrase "^3.35.0"

tapable@^2.1.1, tapable@^2.2.0:
  version "2.2.1"
  resolved "https://registry.npmjs.org/tapable/-/tapable-2.2.1.tgz"
  integrity sha512-GNzQvQTOIP6RyTfE2Qxb8ZVlNmw0n88vp1szwWRimP02mnTsx3Wtn5qRdqY9w2XduFNUgvOwhNnQsjwCp+kqaQ==

tar@^6.1.11:
  version "6.2.0"
  resolved "https://registry.npmjs.org/tar/-/tar-6.2.0.tgz"
  integrity sha512-/Wo7DcT0u5HUV486xg675HtjNd3BXZ6xDbzsCUZPt5iw8bTQ63bP0Raut3mvro9u+CUyq7YQd8Cx55fsZXxqLQ==
  dependencies:
    chownr "^2.0.0"
    fs-minipass "^2.0.0"
    minipass "^5.0.0"
    minizlib "^2.1.1"
    mkdirp "^1.0.3"
    yallist "^4.0.0"

terser-webpack-plugin@^5.3.10:
  version "5.3.10"
  resolved "https://registry.npmjs.org/terser-webpack-plugin/-/terser-webpack-plugin-5.3.10.tgz"
  integrity sha512-BKFPWlPDndPs+NGGCr1U59t0XScL5317Y0UReNrHaw9/FwhPENlq6bfgs+4yPfyP51vqC1bQ4rp1EfXW5ZSH9w==
  dependencies:
    "@jridgewell/trace-mapping" "^0.3.20"
    jest-worker "^27.4.5"
    schema-utils "^3.1.1"
    serialize-javascript "^6.0.1"
    terser "^5.26.0"

terser@^5.26.0:
  version "5.36.0"
  resolved "https://registry.npmjs.org/terser/-/terser-5.36.0.tgz"
  integrity sha512-IYV9eNMuFAV4THUspIRXkLakHnV6XO7FEdtKjf/mDyrnqUg9LnlOn6/RwRvM9SZjR4GUq8Nk8zj67FzVARr74w==
  dependencies:
    "@jridgewell/source-map" "^0.3.3"
    acorn "^8.8.2"
    commander "^2.20.0"
    source-map-support "~0.5.20"

text-segmentation@^1.0.3:
  version "1.0.3"
  resolved "https://registry.npmjs.org/text-segmentation/-/text-segmentation-1.0.3.tgz"
  integrity sha512-iOiPUo/BGnZ6+54OsWxZidGCsdU8YbE4PSpdPinp7DeMtUJNJBoJ/ouUSTJjHkh1KntHaltHl/gDs2FC4i5+Nw==
  dependencies:
    utrie "^1.0.2"

text-table@^0.2.0:
  version "0.2.0"
  resolved "https://registry.npmjs.org/text-table/-/text-table-0.2.0.tgz"
  integrity sha512-N+8UisAXDGk8PFXP4HAzVR9nbfmVJ3zYLAWiTIoqC5v5isinhr+r5uaO8+7r3BMfuNIufIsA7RdpVgacC2cSpw==

thenify-all@^1.0.0:
  version "1.6.0"
  resolved "https://registry.npmjs.org/thenify-all/-/thenify-all-1.6.0.tgz"
  integrity sha512-RNxQH/qI8/t3thXJDwcstUO4zeqo64+Uy/+sNVRBx4Xn2OX+OZ9oP+iJnNFqplFra2ZUVeKCSa2oVWi3T4uVmA==
  dependencies:
    thenify ">= 3.1.0 < 4"

"thenify@>= 3.1.0 < 4":
  version "3.3.1"
  resolved "https://registry.npmjs.org/thenify/-/thenify-3.3.1.tgz"
  integrity sha512-RVZSIV5IG10Hk3enotrhvz0T9em6cyHBLkH/YAZuKqd8hRkKhSfCGIcP2KUY0EPxndzANBmNllzWPwak+bheSw==
  dependencies:
    any-promise "^1.0.0"

through@^2.3.8:
  version "2.3.8"
  resolved "https://registry.npmjs.org/through/-/through-2.3.8.tgz"
  integrity sha512-w89qg7PI8wAdvX60bMDP+bFoD5Dvhm9oLheFp5O4a2QF0cSBGsBX4qZmadPMvVqlLJBBci+WqGGOAPvcDeNSVg==

through2@~0.4.1:
  version "0.4.2"
  resolved "https://registry.npmjs.org/through2/-/through2-0.4.2.tgz"
  integrity sha512-45Llu+EwHKtAZYTPPVn3XZHBgakWMN3rokhEv5hu596XP+cNgplMg+Gj+1nmAvj+L0K7+N49zBKx5rah5u0QIQ==
  dependencies:
    readable-stream "~1.0.17"
    xtend "~2.1.1"

tiny-invariant@1.0.6:
  version "1.0.6"
  resolved "https://registry.npmjs.org/tiny-invariant/-/tiny-invariant-1.0.6.tgz"
  integrity sha512-FOyLWWVjG+aC0UqG76V53yAWdXfH8bO6FNmyZOuUrzDzK8DI3/JRY25UD7+g49JWM1LXwymsKERB+DzI0dTEQA==

tiny-warning@^1.0.2, tiny-warning@^1.0.3:
  version "1.0.3"
  resolved "https://registry.npmjs.org/tiny-warning/-/tiny-warning-1.0.3.tgz"
  integrity sha512-lBN9zLN/oAf68o3zNXYrdCt1kP8WsiGW8Oo2ka41b2IM5JL/S1CTyX1rW0mb/zSuJun0ZUrDxx4sqvYS2FWzPA==

tinymce@^6.3.1, tinymce@^6.7.0:
  version "6.7.2"
  resolved "https://registry.npmjs.org/tinymce/-/tinymce-6.7.2.tgz"
  integrity sha512-6h/02jHmXyghekFzmzccZxUUEFtlPEKHxOd+gd49bjno3ybavZInPIaDd/pp2GeEwsFm20oGgJCL7UiebXm9dw==

to-camel-case@1.0.0:
  version "1.0.0"
  resolved "https://registry.npmjs.org/to-camel-case/-/to-camel-case-1.0.0.tgz"
  integrity sha512-nD8pQi5H34kyu1QDMFjzEIYqk0xa9Alt6ZfrdEMuHCFOfTLhDG5pgTu/aAM9Wt9lXILwlXmWP43b8sav0GNE8Q==
  dependencies:
    to-space-case "^1.0.0"

to-fast-properties@^2.0.0:
  version "2.0.0"
  resolved "https://registry.npmjs.org/to-fast-properties/-/to-fast-properties-2.0.0.tgz"
  integrity sha512-/OaKK0xYrs3DmxRYqL/yDc+FxFUVYhDlXMhRmv3z915w2HF1tnN1omB354j8VUGO/hbRzyD6Y3sA7v7GS/ceog==

to-no-case@^1.0.0:
  version "1.0.2"
  resolved "https://registry.npmjs.org/to-no-case/-/to-no-case-1.0.2.tgz"
  integrity sha512-Z3g735FxuZY8rodxV4gH7LxClE4H0hTIyHNIHdk+vpQxjLm0cwnKXq/OFVZ76SOQmto7txVcwSCwkU5kqp+FKg==

to-regex-range@^5.0.1:
  version "5.0.1"
  resolved "https://registry.npmjs.org/to-regex-range/-/to-regex-range-5.0.1.tgz"
  integrity sha512-65P7iz6X5yEr1cwcgvQxbbIw7Uk3gOy5dIdtZ4rDveLqhrdJP+Li/Hx6tyK0NEb+2GCyneCMJiGqrADCSNk8sQ==
  dependencies:
    is-number "^7.0.0"

to-space-case@^1.0.0:
  version "1.0.0"
  resolved "https://registry.npmjs.org/to-space-case/-/to-space-case-1.0.0.tgz"
  integrity sha512-rLdvwXZ39VOn1IxGL3V6ZstoTbwLRckQmn/U8ZDLuWwIXNpuZDhQ3AiRUlhTbOXFVE9C+dR51wM0CBDhk31VcA==
  dependencies:
    to-no-case "^1.0.0"

tr46@~0.0.3:
  version "0.0.3"
  resolved "https://registry.npmjs.org/tr46/-/tr46-0.0.3.tgz"
  integrity sha512-N3WMsuqV66lT30CrXNbEjx4GEwlow3v6rr4mCcv6prnfwhS01rkgyFdjPNBYd9br7LpXV1+Emh01fHnq2Gdgrw==

ts-custom-error@^3.0.0:
  version "3.3.1"
  resolved "https://registry.npmjs.org/ts-custom-error/-/ts-custom-error-3.3.1.tgz"
  integrity sha512-5OX1tzOjxWEgsr/YEUWSuPrQ00deKLh6D7OTWcvNHm12/7QPyRh8SYpyWvA4IZv8H/+GQWQEh/kwo95Q9OVW1A==

ts-interface-checker@^0.1.9:
  version "0.1.13"
  resolved "https://registry.npmjs.org/ts-interface-checker/-/ts-interface-checker-0.1.13.tgz"
  integrity sha512-Y/arvbn+rrz3JCKl9C4kVNfTfSm2/mEp5FSz5EsZSANGPSlQrpRI5M4PKF+mJnE52jOO90PnPSc3Ur3bTQw0gA==

tsconfig-paths@^3.14.1, tsconfig-paths@^3.14.2:
  version "3.14.2"
  resolved "https://registry.npmjs.org/tsconfig-paths/-/tsconfig-paths-3.14.2.tgz"
  integrity sha512-o/9iXgCYc5L/JxCHPe3Hvh8Q/2xm5Z+p18PESBU6Ff33695QnCHBEjcytY2q19ua7Mbl/DavtBOLq+oG0RCL+g==
  dependencies:
    "@types/json5" "^0.0.29"
    json5 "^1.0.2"
    minimist "^1.2.6"
    strip-bom "^3.0.0"

tslib@^1.8.1:
  version "1.14.1"
  resolved "https://registry.npmjs.org/tslib/-/tslib-1.14.1.tgz"
  integrity sha512-Xni35NKzjgMrwevysHTCArtLDpPvye8zV/0E4EyYn43P7/7qvQwPh9BGkHewbMulVntbigmcT7rdX3BNo9wRJg==

tslib@^2.0.0, tslib@^2.1.0, tslib@^2.4.0, tslib@2:
  version "2.6.2"
  resolved "https://registry.npmjs.org/tslib/-/tslib-2.6.2.tgz"
  integrity sha512-AEYxH93jGFPn/a2iVAwW87VuUIkR1FVUKB77NwMF7nBTDkDrrT/Hpt/IrCJ0QXhW27jTBDcf5ZY7w6RiqTMw2Q==

tsutils@^3.21.0:
  version "3.21.0"
  resolved "https://registry.npmjs.org/tsutils/-/tsutils-3.21.0.tgz"
  integrity sha512-mHKK3iUXL+3UF6xL5k0PEhKRUBKPBCv/+RkEOpjRWxxx27KKRBmmA60A9pgOUvMi8GKhRMPEmjBRPzs2W7O1OA==
  dependencies:
    tslib "^1.8.1"

type-check@^0.4.0, type-check@~0.4.0:
  version "0.4.0"
  resolved "https://registry.npmjs.org/type-check/-/type-check-0.4.0.tgz"
  integrity sha512-XleUoc9uwGXqjWwXaUTZAmzMcFZ5858QA2vvx1Ur5xIcixXIP+8LnFDgRplU30us6teqdlskFfu+ae4K79Ooew==
  dependencies:
    prelude-ls "^1.2.1"

type-fest@^0.20.2:
  version "0.20.2"
  resolved "https://registry.npmjs.org/type-fest/-/type-fest-0.20.2.tgz"
  integrity sha512-Ne+eE4r0/iWnpAxD852z3A+N0Bt5RN//NjJwRd2VFHEmrywxf5vsZlh4R6lixl6B+wz/8d+maTSAkN1FIkI3LQ==

typed-array-buffer@^1.0.0:
  version "1.0.0"
  resolved "https://registry.npmjs.org/typed-array-buffer/-/typed-array-buffer-1.0.0.tgz"
  integrity sha512-Y8KTSIglk9OZEr8zywiIHG/kmQ7KWyjseXs1CbSo8vC42w7hg2HgYTxSWwP0+is7bWDc1H+Fo026CpHFwm8tkw==
  dependencies:
    call-bind "^1.0.2"
    get-intrinsic "^1.2.1"
    is-typed-array "^1.1.10"

typed-array-byte-length@^1.0.0:
  version "1.0.0"
  resolved "https://registry.npmjs.org/typed-array-byte-length/-/typed-array-byte-length-1.0.0.tgz"
  integrity sha512-Or/+kvLxNpeQ9DtSydonMxCx+9ZXOswtwJn17SNLvhptaXYDJvkFFP5zbfU/uLmvnBJlI4yrnXRxpdWH/M5tNA==
  dependencies:
    call-bind "^1.0.2"
    for-each "^0.3.3"
    has-proto "^1.0.1"
    is-typed-array "^1.1.10"

typed-array-byte-offset@^1.0.0:
  version "1.0.0"
  resolved "https://registry.npmjs.org/typed-array-byte-offset/-/typed-array-byte-offset-1.0.0.tgz"
  integrity sha512-RD97prjEt9EL8YgAgpOkf3O4IF9lhJFr9g0htQkm0rchFp/Vx7LW5Q8fSXXub7BXAODyUQohRMyOc3faCPd0hg==
  dependencies:
    available-typed-arrays "^1.0.5"
    call-bind "^1.0.2"
    for-each "^0.3.3"
    has-proto "^1.0.1"
    is-typed-array "^1.1.10"

typed-array-length@^1.0.4:
  version "1.0.4"
  resolved "https://registry.npmjs.org/typed-array-length/-/typed-array-length-1.0.4.tgz"
  integrity sha512-KjZypGq+I/H7HI5HlOoGHkWUUGq+Q0TPhQurLbyrVrvnKTBgzLhIJ7j6J/XTQOi0d1RjyZ0wdas8bKs2p0x3Ng==
  dependencies:
    call-bind "^1.0.2"
    for-each "^0.3.3"
    is-typed-array "^1.1.9"

typescript@^5.2.2, "typescript@>=2.8.0 || >= 3.2.0-dev || >= 3.3.0-dev || >= 3.4.0-dev || >= 3.5.0-dev || >= 3.6.0-dev || >= 3.6.0-beta || >= 3.7.0-dev || >= 3.7.0-beta", typescript@>=3.3.1:
  version "5.2.2"
  resolved "https://registry.npmjs.org/typescript/-/typescript-5.2.2.tgz"
  integrity sha512-mI4WrpHsbCIcwT9cF4FZvr80QUeKvsUsUvKDoR+X/7XHQH98xYD8YHZg7ANtz2GtZt/CBq2QJ0thkGJMHfqc1w==

unbox-primitive@^1.0.2:
  version "1.0.2"
  resolved "https://registry.npmjs.org/unbox-primitive/-/unbox-primitive-1.0.2.tgz"
  integrity sha512-61pPlCD9h51VoreyJ0BReideM3MDKMKnh6+V9L08331ipq6Q8OFXZYiqP6n/tbHx4s5I9uRhcye6BrbkizkBDw==
  dependencies:
    call-bind "^1.0.2"
    has-bigints "^1.0.2"
    has-symbols "^1.0.3"
    which-boxed-primitive "^1.0.2"

update-browserslist-db@^1.1.1:
  version "1.1.1"
  resolved "https://registry.npmjs.org/update-browserslist-db/-/update-browserslist-db-1.1.1.tgz"
  integrity sha512-R8UzCaa9Az+38REPiJ1tXlImTJXlVfgHZsglwBD/k6nj76ctsH1E3q4doGrukiLQd3sGQYu56r5+lo5r94l29A==
  dependencies:
    escalade "^3.2.0"
    picocolors "^1.1.0"

uri-js@^4.2.2:
  version "4.4.1"
  resolved "https://registry.npmjs.org/uri-js/-/uri-js-4.4.1.tgz"
  integrity sha512-7rKUyy33Q1yc98pQ1DAmLtwX109F7TIfWlW1Ydo8Wl1ii1SeHieeh0HHfPeL2fMXK6z0s8ecKs9frCuLJvndBg==
  dependencies:
    punycode "^2.1.0"

url-loader@^4.1.0:
  version "4.1.1"
  resolved "https://registry.npmjs.org/url-loader/-/url-loader-4.1.1.tgz"
  integrity sha512-3BTV812+AVHHOJQO8O5MkWgZ5aosP7GnROJwvzLS9hWDj00lZ6Z0wNak423Lp9PBZN05N+Jk/N5Si8jRAlGyWA==
  dependencies:
    loader-utils "^2.0.0"
    mime-types "^2.1.27"
    schema-utils "^3.0.0"

use-callback-ref@^1.3.0:
  version "1.3.0"
  resolved "https://registry.npmjs.org/use-callback-ref/-/use-callback-ref-1.3.0.tgz"
  integrity sha512-3FT9PRuRdbB9HfXhEq35u4oZkvpJ5kuYbpqhCfmiZyReuRgpnhDlbr2ZEnnuS0RrJAPn6l23xjFg9kpDM+Ms7w==
  dependencies:
    tslib "^2.0.0"

use-composed-ref@^1.3.0:
  version "1.3.0"
  resolved "https://registry.npmjs.org/use-composed-ref/-/use-composed-ref-1.3.0.tgz"
  integrity sha512-GLMG0Jc/jiKov/3Ulid1wbv3r54K9HlMW29IWcDFPEqFkSO2nS0MuefWgMJpeHQ9YJeXDL3ZUF+P3jdXlZX/cQ==

use-isomorphic-layout-effect@^1.1.1:
  version "1.1.2"
  resolved "https://registry.npmjs.org/use-isomorphic-layout-effect/-/use-isomorphic-layout-effect-1.1.2.tgz"
  integrity sha512-49L8yCO3iGT/ZF9QttjwLF/ZD9Iwto5LnH5LmEdk/6cFmXddqi2ulF0edxTwjj+7mqvpVVGQWvbXZdn32wRSHA==

use-latest@^1.2.1:
  version "1.2.1"
  resolved "https://registry.npmjs.org/use-latest/-/use-latest-1.2.1.tgz"
  integrity sha512-xA+AVm/Wlg3e2P/JiItTziwS7FK92LWrDB0p+hgXloIMuVCeJJ8v6f0eeHyPZaJrM+usM1FkFfbNCrJGs8A/zw==
  dependencies:
    use-isomorphic-layout-effect "^1.1.1"

use-sidecar@^1.1.2:
  version "1.1.2"
  resolved "https://registry.npmjs.org/use-sidecar/-/use-sidecar-1.1.2.tgz"
  integrity sha512-epTbsLuzZ7lPClpz2TyryBfztm7m+28DlEv2ZCQ3MDr5ssiwyOwGH/e5F9CkfWjJ1t4clvI58yF822/GUkjjhw==
  dependencies:
    detect-node-es "^1.1.0"
    tslib "^2.0.0"

util-deprecate@^1.0.1, util-deprecate@^1.0.2, util-deprecate@~1.0.1:
  version "1.0.2"
  resolved "https://registry.npmjs.org/util-deprecate/-/util-deprecate-1.0.2.tgz"
  integrity sha512-EPD5q1uXyFxJpCrLnCc1nHnq3gOa6DZBocAIiI2TaSCA7VCJ1UJDMagCzIkXNsUYfD1daK//LTEQ8xiIbrHtcw==

utrie@^1.0.2:
  version "1.0.2"
  resolved "https://registry.npmjs.org/utrie/-/utrie-1.0.2.tgz"
  integrity sha512-1MLa5ouZiOmQzUbjbu9VmjLzn1QLXBhwpUa7kdLUQK+KQ5KA9I1vk5U4YHe/X2Ch7PYnJfWuWT+VbuxbGwljhw==
  dependencies:
    base64-arraybuffer "^1.0.2"

v8-compile-cache@^2.0.3:
  version "2.4.0"
  resolved "https://registry.npmjs.org/v8-compile-cache/-/v8-compile-cache-2.4.0.tgz"
  integrity sha512-ocyWc3bAHBB/guyqJQVI5o4BZkPhznPYUG2ea80Gond/BgNWpap8TOmLSeeQG7bnh2KMISxskdADG59j7zruhw==

vanilla-colorful@0.7.2:
  version "0.7.2"
  resolved "https://registry.npmjs.org/vanilla-colorful/-/vanilla-colorful-0.7.2.tgz"
  integrity sha512-z2YZusTFC6KnLERx1cgoIRX2CjPRP0W75N+3CC6gbvdX5Ch47rZkEMGO2Xnf+IEmi3RiFLxS18gayMA27iU7Kg==

watchpack@^2.4.1:
  version "2.4.2"
  resolved "https://registry.npmjs.org/watchpack/-/watchpack-2.4.2.tgz"
  integrity sha512-TnbFSbcOCcDgjZ4piURLCbJ3nJhznVh9kw6F6iokjiFPl8ONxe9A6nMDVXDiNbrSfLILs6vB07F7wLBrwPYzJw==
  dependencies:
    glob-to-regexp "^0.4.1"
    graceful-fs "^4.1.2"

webidl-conversions@^3.0.0:
  version "3.0.1"
  resolved "https://registry.npmjs.org/webidl-conversions/-/webidl-conversions-3.0.1.tgz"
  integrity sha512-2JAn3z8AR6rjK8Sm8orRC0h/bcl/DqL7tRPdGZ4I1CjdF+EaMLmYxBHyXuKL849eucPFhvBoxMsflfOb8kxaeQ==

webpack-sources@^3.2.3:
  version "3.2.3"
  resolved "https://registry.npmjs.org/webpack-sources/-/webpack-sources-3.2.3.tgz"
  integrity sha512-/DyMEOrDgLKKIG0fmvtz+4dUX/3Ghozwgm6iPp8KRhvn+eQf9+Q7GWxVNMk3+uCPWfdXYC4ExGBckIXdFEfH1w==

"webpack@^4.0.0 || ^5.0.0", webpack@^5.1.0:
  version "5.96.1"
  resolved "https://registry.npmjs.org/webpack/-/webpack-5.96.1.tgz"
  integrity sha512-l2LlBSvVZGhL4ZrPwyr8+37AunkcYj5qh8o6u2/2rzoPc8gxFJkLj1WxNgooi9pnoc06jh0BjuXnamM4qlujZA==
  dependencies:
    "@types/eslint-scope" "^3.7.7"
    "@types/estree" "^1.0.6"
    "@webassemblyjs/ast" "^1.12.1"
    "@webassemblyjs/wasm-edit" "^1.12.1"
    "@webassemblyjs/wasm-parser" "^1.12.1"
    acorn "^8.14.0"
    browserslist "^4.24.0"
    chrome-trace-event "^1.0.2"
    enhanced-resolve "^5.17.1"
    es-module-lexer "^1.2.1"
    eslint-scope "5.1.1"
    events "^3.2.0"
    glob-to-regexp "^0.4.1"
    graceful-fs "^4.2.11"
    json-parse-even-better-errors "^2.3.1"
    loader-runner "^4.2.0"
    mime-types "^2.1.27"
    neo-async "^2.6.2"
    schema-utils "^3.2.0"
    tapable "^2.1.1"
    terser-webpack-plugin "^5.3.10"
    watchpack "^2.4.1"
    webpack-sources "^3.2.3"

whatwg-url@^5.0.0:
  version "5.0.0"
  resolved "https://registry.npmjs.org/whatwg-url/-/whatwg-url-5.0.0.tgz"
  integrity sha512-saE57nupxk6v3HY35+jzBwYa0rKSy0XR8JSxZPwgLr7ys0IBzhGviA1/TUGJLmSVqs8pb9AnvICXEuOHLprYTw==
  dependencies:
    tr46 "~0.0.3"
    webidl-conversions "^3.0.0"

which-boxed-primitive@^1.0.2:
  version "1.0.2"
  resolved "https://registry.npmjs.org/which-boxed-primitive/-/which-boxed-primitive-1.0.2.tgz"
  integrity sha512-bwZdv0AKLpplFY2KZRX6TvyuN7ojjr7lwkg6ml0roIy9YeuSr7JS372qlNW18UQYzgYK9ziGcerWqZOmEn9VNg==
  dependencies:
    is-bigint "^1.0.1"
    is-boolean-object "^1.1.0"
    is-number-object "^1.0.4"
    is-string "^1.0.5"
    is-symbol "^1.0.3"

which-builtin-type@^1.1.3:
  version "1.1.3"
  resolved "https://registry.npmjs.org/which-builtin-type/-/which-builtin-type-1.1.3.tgz"
  integrity sha512-YmjsSMDBYsM1CaFiayOVT06+KJeXf0o5M/CAd4o1lTadFAtacTUM49zoYxr/oroopFDfhvN6iEcBxUyc3gvKmw==
  dependencies:
    function.prototype.name "^1.1.5"
    has-tostringtag "^1.0.0"
    is-async-function "^2.0.0"
    is-date-object "^1.0.5"
    is-finalizationregistry "^1.0.2"
    is-generator-function "^1.0.10"
    is-regex "^1.1.4"
    is-weakref "^1.0.2"
    isarray "^2.0.5"
    which-boxed-primitive "^1.0.2"
    which-collection "^1.0.1"
    which-typed-array "^1.1.9"

which-collection@^1.0.1:
  version "1.0.1"
  resolved "https://registry.npmjs.org/which-collection/-/which-collection-1.0.1.tgz"
  integrity sha512-W8xeTUwaln8i3K/cY1nGXzdnVZlidBcagyNFtBdD5kxnb4TvGKR7FfSIS3mYpwWS1QUCutfKz8IY8RjftB0+1A==
  dependencies:
    is-map "^2.0.1"
    is-set "^2.0.1"
    is-weakmap "^2.0.1"
    is-weakset "^2.0.1"

which-typed-array@^1.1.11, which-typed-array@^1.1.13, which-typed-array@^1.1.9:
  version "1.1.13"
  resolved "https://registry.npmjs.org/which-typed-array/-/which-typed-array-1.1.13.tgz"
  integrity sha512-P5Nra0qjSncduVPEAr7xhoF5guty49ArDTwzJ/yNuPIbZppyRxFQsRCWrocxIY+CnMVG+qfbU2FmDKyvSGClow==
  dependencies:
    available-typed-arrays "^1.0.5"
    call-bind "^1.0.4"
    for-each "^0.3.3"
    gopd "^1.0.1"
    has-tostringtag "^1.0.0"

which@^2.0.1:
  version "2.0.2"
  resolved "https://registry.npmjs.org/which/-/which-2.0.2.tgz"
  integrity sha512-BLI3Tl1TW3Pvl70l3yq3Y64i+awpwXqsGBYWkkqMtnbXgrMD+yj7rhW0kuEDxzJaYXGjEW5ogapKNMEKNMjibA==
  dependencies:
    isexe "^2.0.0"

wide-align@^1.1.2:
  version "1.1.5"
  resolved "https://registry.npmjs.org/wide-align/-/wide-align-1.1.5.tgz"
  integrity sha512-eDMORYaPNZ4sQIuuYPDHdQvf4gyCF9rEEV/yPxGfwPkRodwEgiMUUXTx/dex+Me0wxx53S+NgUHaP7y3MGlDmg==
  dependencies:
    string-width "^1.0.2 || 2 || 3 || 4"

"wrap-ansi-cjs@npm:wrap-ansi@^7.0.0":
  version "7.0.0"
  resolved "https://registry.npmjs.org/wrap-ansi/-/wrap-ansi-7.0.0.tgz"
  integrity sha512-YVGIj2kamLSTxw6NsZjoBxfSwsn0ycdesmc4p+Q21c5zPuZ1pl+NfxVdxPtdHvmNVOQ6XSYG4AUtyt/Fi7D16Q==
  dependencies:
    ansi-styles "^4.0.0"
    string-width "^4.1.0"
    strip-ansi "^6.0.0"

wrap-ansi@^8.1.0:
  version "8.1.0"
  resolved "https://registry.npmjs.org/wrap-ansi/-/wrap-ansi-8.1.0.tgz"
  integrity sha512-si7QWI6zUMq56bESFvagtmzMdGOtoxfR+Sez11Mobfc7tm+VkUckk9bW2UeffTGVUbOksxmSw0AA2gs8g71NCQ==
  dependencies:
    ansi-styles "^6.1.0"
    string-width "^5.0.1"
    strip-ansi "^7.0.1"

wrappy@1:
  version "1.0.2"
  resolved "https://registry.npmjs.org/wrappy/-/wrappy-1.0.2.tgz"
  integrity sha512-l4Sp/DRseor9wL6EvV2+TuQn63dMkPjZ/sp9XkghTEbV9KlPS1xUsZ3u7/IQO4wxtcFB4bgpQPRcR3QCvezPcQ==

xtend@~2.1.1:
  version "2.1.2"
  resolved "https://registry.npmjs.org/xtend/-/xtend-2.1.2.tgz"
  integrity sha512-vMNKzr2rHP9Dp/e1NQFnLQlwlhp9L/LfvnsVdHxN1f+uggyVI3i08uD14GPvCToPkdsRfyPqIyYGmIk58V98ZQ==
  dependencies:
    object-keys "~0.4.0"

yallist@^4.0.0:
  version "4.0.0"
  resolved "https://registry.npmjs.org/yallist/-/yallist-4.0.0.tgz"
  integrity sha512-3wdGidZyq5PB084XLES5TpOSRA3wjXAlIWMhum2kRcv/41Sn2emQ0dycQW4uZXLejwKvg6EsvbdlVL+FYEct7A==

yaml@^1.10.0:
  version "1.10.2"
  resolved "https://registry.npmjs.org/yaml/-/yaml-1.10.2.tgz"
  integrity sha512-r3vXyErRCYJ7wg28yvBY5VSoAF8ZvlcW9/BwUzEtUsjvX/DKs24dIkuwjtuprwJJHsbyUbLApepYTR1BN4uHrg==

yaml@^2.3.4:
  version "2.3.4"
  resolved "https://registry.npmjs.org/yaml/-/yaml-2.3.4.tgz"
  integrity sha512-8aAvwVUSHpfEqTQ4w/KMlf3HcRdt50E5ODIQJBw1fQ5RL34xabzxtUlzTXVqc4rkZsPbvrXKWnABCD7kWSmocA==

youtube-player@5.5.2:
  version "5.5.2"
  resolved "https://registry.npmjs.org/youtube-player/-/youtube-player-5.5.2.tgz"
  integrity sha512-ZGtsemSpXnDky2AUYWgxjaopgB+shFHgXVpiJFeNB5nWEugpW1KWYDaHKuLqh2b67r24GtP6HoSW5swvf0fFIQ==
  dependencies:
    debug "^2.6.6"
    load-script "^1.0.0"
    sister "^3.0.0"
