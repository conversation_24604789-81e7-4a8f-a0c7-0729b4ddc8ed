import { PrismaClient } from '@prisma/client';

const prisma = new PrismaClient();

export default async function handler(req, res) {
  if (req.method !== 'GET') {
    return res.status(405).json({ message: 'Method not allowed' });
  }

  const { id } = req.query;

  try {
    const userAddresses = await prisma.userAddress.findMany({
      where: {
        userId: parseInt(id)
      },
      include: {
        city: true,
        district: true,
        neighborhood: true
      },
      orderBy: {
          createdAt: 'desc'
      },
    });

    // Close the Prisma client connection
    await prisma.$disconnect();

    res.status(200).json({ results: userAddresses });
  } catch (error) {
    console.error(error);
    // Close the Prisma client connection
    await prisma.$disconnect();
    res.status(500).json({ message: 'Internal server error' });
  }
}
