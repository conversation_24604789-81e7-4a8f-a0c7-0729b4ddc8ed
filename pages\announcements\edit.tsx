import React, { useState, useEffect } from 'react';
import Head from 'next/head';
import { authMiddleware } from '../../middleware';
import SidebarLayout from '@/layouts/SidebarLayout';
import PageTitleWrapper from '@/components/PageTitleWrapper';
import { Typography, Container, Grid, CircularProgress } from '@mui/material';
import { Image, Card, CardBody, CardHeader, Button, Input, Textarea, Divider } from '@nextui-org/react';
import { Select, SelectItem } from "@nextui-org/select";
import Footer from '@/components/Footer';
import { useRouter } from 'next/router';
import Swal from 'sweetalert2';
import ArrowBackIcon from '@mui/icons-material/ArrowBack';
import Link from 'next/link';

const EditAnnouncementPage = () => {
    const router = useRouter();
    const { id } = router.query;

    const [item, setItem]: any = useState({});
    const [detailItem, setDetailItem]: any = useState([]);
    const [vendors, setVendors] = useState([]);
    const [loading, setLoading] = useState(true);
    const [currentTimestamp, setCurrentTimestamp] = useState(new Date().getTime());
    const [imageLoading, setImageLoading] = useState(false);

    const loadList = async () => {
        const url = `/api/announcements?id=${id}`;

        try {
            const res = await fetch(url);

            if (res.status === 200) {
                const json = await res.json();
                setItem(json.results[0]);
            } else {
                Swal.fire({
                    icon: 'error',
                    title: 'Hata',
                    text: 'Bir sorun meydana geldi!',
                });
            }
        } catch (error) {
            console.error(error);
        } finally {
            setLoading(false);
        }
    };

    const getDetail = async (detail: number) => {
        let url = "";
        detail == 1 ? (url = `/api/concerts`) : detail == 2 ? (url = `/api/daily-activities`) : detail == 3 ? (url = `/api/shop-products`) : (url = `/api/hollysnap`);

        try {
            const res = await fetch(url);

            if (res.status === 200) {
                const json = await res.json();
                setDetailItem(json.results);
            } else {
                Swal.fire({
                    icon: 'error',
                    title: 'Hata',
                    text: 'Bir sorun meydana geldi!',
                });
            }
        } catch (error) {
            console.error(error);
        } finally {
            setLoading(false);
        }
    };

    const loadVendors = async () => {
        const url = `/api/vendors`;

        try {
            const res = await fetch(url);

            if (res.status === 200) {
                const json = await res.json();
                setVendors(json.results);
            } else {
                Swal.fire({
                    icon: 'error',
                    title: 'Hata',
                    text: 'Bir sorun meydana geldi!',
                });
            }
        } catch (error) {
            console.error(error);
        } finally {
            setLoading(false);
        }
    };

    useEffect(() => {
        loadList();
        loadVendors();
    }, []);

    useEffect(() => {
        getDetail(item.detail);
    }, [item.detail]);

    const handleImageChange = async (event) => {
        const file = event.target.files[0];

        if (file) {
            if (!file.type.startsWith('image/')) {
                Swal.fire({
                    icon: 'error',
                    title: 'Hata',
                    text: 'Seçilen format desteklenmiyor!',
                });
                return;
            }
            setImageLoading(true);
            const formData = new FormData();
            formData.append('image', file);
            formData.append('fileName', `announcements/${item.id}`);

            try {
                const response = await fetch(`https://api.hollystone.com.tr/api/functions/upload`, {
                    method: 'POST',
                    body: formData,
                });

                if (response.ok) {
                    const data = await response.json();

                    if (data.type == "success") {
                        Swal.fire({
                            icon: 'success',
                            title: 'Başarılı',
                            text: 'Resim güncellendi!',
                        });
                        setCurrentTimestamp(new Date().getTime());
                    } else {
                        Swal.fire({
                            icon: 'error',
                            title: 'Hata',
                            text: data.error,
                        });
                    }
                } else {
                    Swal.fire({
                        icon: 'error',
                        title: 'Hata',
                        text: 'Bir sorun meydana geldi!',
                    });
                }
            } catch (error) {
                Swal.fire({
                    icon: 'error',
                    title: 'Hata',
                    text: 'Bir sorun meydana geldi!',
                });
            }
            setImageLoading(false);
        }
    };

    const handleDelete = async () => {
        const shouldDelete = await Swal.fire({
            title: 'Dikkat!',
            text: 'Bu kaydı silmek istediğine gerçekten emin misin?',
            icon: 'warning',
            showCancelButton: true,
            confirmButtonText: 'Evet, sil!',
            cancelButtonText: 'Vazgeç',
        });

        if (shouldDelete.isConfirmed) {
            try {
                const response = await fetch(`/api/announcements/delete`, {
                    method: 'DELETE',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({ id: id }),
                });

                if (response.ok) {
                    Swal.fire({
                        icon: 'success',
                        title: 'Başarılı',
                        text: 'Kayıt silindi!',
                    });
                    router.push('/announcements');
                } else {
                    Swal.fire({
                        icon: 'error',
                        title: 'Hata',
                        text: 'Bir sorun meydana geldi!',
                    });
                }
            } catch (error) {
                Swal.fire({
                    icon: 'error',
                    title: 'Hata',
                    text: 'Bir sorun meydana geldi!',
                });
            }
        }
    };

    const handleUpdate = async (event) => {
        event.preventDefault();
        try {
            const response = await fetch(`/api/announcements/update`, {
                method: 'PUT',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify(item),
            });

            if (response.ok) {
                await loadList();
                Swal.fire({
                    icon: 'success',
                    title: 'Başarılı',
                    text: 'Kayıt güncellendi!',
                });
            } else {
                Swal.fire({
                    icon: 'error',
                    title: 'Hata',
                    text: 'Bir sorun meydana geldi!',
                });
            }
        } catch (error) {
            Swal.fire({
                icon: 'error',
                title: 'Hata',
                text: 'Bir sorun meydana geldi!',
            });
        }
    }

    const renderContent = () => {
        if (loading) {
            return <p>Loading...</p>;
        }

        return (
            <>
                <Head>
                    <title>Duyurular - Düzenle</title>
                </Head>
                <PageTitleWrapper>
                    <Grid container alignItems="center">
                        <Grid item>
                            <Typography variant="h3" component="h3" gutterBottom>
                                Duyuru Düzenle
                            </Typography>
                        </Grid>
                    </Grid>
                </PageTitleWrapper>
                <Container maxWidth="lg">
                    <Grid
                        container
                        direction="row"
                        justifyContent="center"
                        alignItems="stretch"
                        spacing={4}
                        style={{
                            marginBottom: 20
                        }}
                    >
                        <Grid item xs={12}>
                            <Card
                                isBlurred
                                className="border-none bg-white dark:bg-default-100/50 max-w-[610px]"
                                shadow="sm"
                            >
                                <form onSubmit={handleUpdate}>
                                    <CardHeader className="flex justify-between gap-3">
                                        <div>
                                            <Link href="/announcements">
                                                <Button type="button" color="default" startContent={<ArrowBackIcon />}>
                                                    Duyurular
                                                </Button>
                                            </Link>
                                        </div>
                                        <div>Duyuru Bilgileri</div>
                                        <div className="flex-end">
                                            <Button type="button" onClick={handleDelete} color="danger" className="mr-2">
                                                Sil
                                            </Button>
                                            <Button type="submit" color="primary">
                                                Güncelle
                                            </Button>
                                        </div>
                                    </CardHeader>

                                    <Divider />

                                    <CardBody>
                                        <div className="flex">
                                            <div className="relative w-1/2">
                                                <label className="block cursor-pointer">
                                                    <Image
                                                        width={400}
                                                        height={400}
                                                        radius="none"
                                                        loading="eager"
                                                        style={{
                                                            height: "100%"
                                                        }}
                                                        src={`https://api.hollystone.com.tr/resources/images/${item.image}?t=${currentTimestamp}`}
                                                        fallbackSrc="https://via.placeholder.com/400x400.png?text="
                                                    />
                                                    {imageLoading ? (
                                                        <CircularProgress
                                                            size={48}
                                                            style={{
                                                                position: 'absolute',
                                                                top: '40%',
                                                                left: '40%',
                                                                transform: 'translate(-40%, -40%)',
                                                                zIndex: 999,
                                                            }}
                                                        />
                                                    ) : null}
                                                    <Input
                                                        type="file"
                                                        label="Resim"
                                                        name="image"
                                                        className="hidden"
                                                        onChange={handleImageChange}
                                                    />
                                                </label>
                                            </div>

                                            <div className="p-4 w-full">
                                                <Input
                                                    type="text"
                                                    label="Başlık"
                                                    name="header"
                                                    value={item.header}
                                                    className="mb-2"
                                                    onChange={(e: any) => setItem({ ...item, header: e.target.value })}
                                                />
                                                <div className="w-full mb-2 flex flex-row">
                                                    <Select
                                                        label="Konu"
                                                        placeholder="Bir seçim yapın..."
                                                        className="mr-2"
                                                        defaultSelectedKeys={[item.detail?.toString()]}
                                                        onChange={(e: any) => {
                                                            setItem({ ...item, detail: parseInt(e.target.value) });
                                                        }}
                                                    >
                                                        <SelectItem key="1" value="1">Konser</SelectItem>
                                                        <SelectItem key="2" value="2">Günlük Etkinlik</SelectItem>
                                                        <SelectItem key="3" value="3">Holly Shop</SelectItem>
                                                        <SelectItem key="4" value="4">Holly Snap</SelectItem>
                                                    </Select>
                                                    <Select
                                                        label="Hedef"
                                                        placeholder="Bir seçim yapın..."
                                                        className="mr-2"
                                                        defaultSelectedKeys={[item.detailId?.toString()]}
                                                        onChange={(e: any) => setItem({ ...item, detailId: parseInt(e.target.value) })}
                                                    >
                                                        {
                                                            detailItem.map((innerItem: any, index: number) => {
                                                                return (
                                                                    <SelectItem key={innerItem.id} value={index}>
                                                                        {item.detail == 4 ? innerItem.activityName : innerItem.name}
                                                                    </SelectItem>
                                                                )
                                                            })
                                                        }
                                                    </Select>
                                                    <Select
                                                        label="Bayi"
                                                        placeholder="Bir seçim yapın..."
                                                        defaultSelectedKeys={[item.vendorId?.toString()]}
                                                        onChange={(e: any) => setItem({ ...item, vendorId: parseInt(e.target.value) })}
                                                    >
                                                        {vendors.map((vendor, index) => {
                                                            return (
                                                                <SelectItem key={vendor.id} value={index}>
                                                                    {vendor.name}
                                                                </SelectItem>
                                                            )
                                                        })}
                                                    </Select>
                                                </div>
                                                <Textarea
                                                    label="İçerik"
                                                    name="content"
                                                    value={item.content}
                                                    className="mb-2"
                                                    onChange={(e: any) => setItem({ ...item, content: e.target.value })}
                                                />
                                            </div>
                                        </div>
                                    </CardBody>
                                </form>

                            </Card>
                        </Grid>
                    </Grid>
                </Container>
                <Footer />
            </>
        );
    };

    return renderContent();
};

EditAnnouncementPage.getLayout = (page: React.ReactNode) => <SidebarLayout>{page}</SidebarLayout>;

export const getServerSideProps = authMiddleware(async (_context: any) => {
    return {
        props: {},
    };
});

export default EditAnnouncementPage;
