import React, { useEffect, useState } from 'react';
import Head from 'next/head';
import { useRouter } from 'next/router';
import { authMiddleware } from 'middleware';
import Swal from 'sweetalert2';
import { Container, Grid, Typography, CircularProgress, Backdrop } from '@mui/material';
import { Select, SelectItem } from "@nextui-org/select";
import { Card, CardBody, CardHeader, Button, Input, Divider } from '@nextui-org/react';
import SidebarLayout from '@/layouts/SidebarLayout';
import PageTitleWrapper from '@/components/PageTitleWrapper';
import Footer from '@/components/Footer';
import ArrowBackIcon from '@mui/icons-material/ArrowBack';
import Link from 'next/link';

const NewTicketPage = () => {
    const router = useRouter();
    const { id } = router.query;
    const [item, setItem]: any = useState({});
    const [tickets, setTickets]: any = useState([]);
    const [loading, setLoading] = useState(false);

    const loadTickets = async () => {
        const url = `/api/concert-tickets?id=${id}`;

        try {
            const res = await fetch(url);

            if (res.status === 200) {
                const json = await res.json();
                setTickets(json.results);
            } else {
                Swal.fire({
                    icon: 'error',
                    title: 'Hata',
                    text: 'Bir sorun meydana geldi!',
                });
            }
        } catch (error) {
            console.error(error);
        } finally {
            setLoading(false);
        }
    };

    useEffect(() => {
        loadTickets();
    }, []);

    const handleInsert = async (event: React.FormEvent) => {
        event.preventDefault();
        if (!item.firstName || !item.lastName || !item.dateOfBirth  || !item.phoneNumber || !item.email || item.ticketId == null) {
            Swal.fire({
                icon: 'error',
                title: 'Hata',
                text: 'Lütfen alanları doldurun!',
            });
            return;
        }
        item.concertId = id;
        try {
            setLoading(true);
            const response = await fetch(`/api/upcoming-tickets/insert`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify(item),
            });

            if (response.ok) {
                Swal.fire({
                    icon: 'success',
                    title: 'Başarılı',
                    text: 'Kayıt başarılı!',
                }).then(() => {
                    router.push(`/upcoming-tickets/list?id=${id}`);
                });
            } else {
                Swal.fire({
                    icon: 'error',
                    title: 'Hata',
                    text: 'Bir sorun meydana geldi!',
                });
            }
        } catch (error) {
            console.error('An error occurred:', error);
        } finally {
            setLoading(false);
        }
    };

    return (
        <>
            <Head>
                <title>Yeni Bilet - Oluştur</title>
            </Head>
            <PageTitleWrapper>
                <Grid container alignItems="center">
                    <Grid item>
                        <Typography variant="h3" component="h3" gutterBottom>
                            Yeni Bilet Oluştur
                        </Typography>
                    </Grid>
                </Grid>
            </PageTitleWrapper>
            <Container maxWidth="lg">
                <Grid
                    container
                    direction="row"
                    justifyContent="center"
                    alignItems="stretch"
                    spacing={4}
                >
                    <Grid item xs={12}>
                        <Card
                            isBlurred
                            className="border-none bg-white dark:bg-default-100/50 max-w-[610px]"
                            shadow="sm"
                        >
                            <form onSubmit={handleInsert}>
                                <CardHeader className="flex justify-between gap-3">
                                    <div>
                                        <Link href={`/upcoming-tickets/list?id=${id}`}>
                                            <Button type="button" color="default" startContent={<ArrowBackIcon />}>
                                                Biletler
                                            </Button>
                                        </Link>
                                    </div>
                                    <div></div>
                                    <div className="flex-end">
                                        <Button type="submit" color="primary">
                                            Kaydet
                                        </Button>
                                    </div>
                                </CardHeader>

                                <Divider />

                                <CardBody>
                                    <div className="flex">
                                        <div className="p-4 w-full">
                                            <div className="w-full mb-2 flex flex-row">
                                                <Input
                                                    type="text"
                                                    label="Ad"
                                                    name="firstName"
                                                    value={item.firstName}
                                                    className="mr-2"
                                                    onChange={(e: any) => setItem({ ...item, firstName: e.target.value })}
                                                />
                                                <Input
                                                    type="text"
                                                    label="Soyad"
                                                    name="lastName"
                                                    value={item.lastName}
                                                    className="mr-2"
                                                    onChange={(e: any) => setItem({ ...item, lastName: e.target.value })}
                                                />
                                                <Input
                                                    type="date"
                                                    label="Doğum Tarihi"
                                                    placeholder="Doğum Tarihi"
                                                    name="dateOfBirth"
                                                    value={item?.dateOfBirth?.toISOString()?.split('T')[0]}
                                                    onChange={(e: any) => setItem({ ...item, dateOfBirth: new Date(e.target.value) })}
                                                />
                                            </div>
                                            <div className="w-full mb-2 flex flex-row">
                                                <Input
                                                    type="text"
                                                    label="Telefon Numarası"
                                                    name="phoneNumber"
                                                    value={item.phoneNumber}
                                                    className="mr-2"
                                                    onChange={(e: any) => setItem({ ...item, phoneNumber: e.target.value })}
                                                />
                                                <Input
                                                    type="text"
                                                    label="E-mail"
                                                    name="email"
                                                    value={item.email}
                                                    className="mr-2"
                                                    onChange={(e: any) => setItem({ ...item, email: e.target.value })}
                                                />
                                            </div>
                                            <Select
                                                label="Bilet"
                                                placeholder="Bir seçim yapın..."
                                                className="mr-2"
                                                defaultSelectedKeys={[item.ticketId?.toString()]}
                                                onChange={(e: any) => {
                                                    setItem({ ...item, ticketId: parseInt(e.target.value) });
                                                }}
                                            >
                                                {
                                                    tickets.map((innerItem: any, index: number) => {
                                                        return (
                                                            <SelectItem key={innerItem.id} value={index}>{innerItem.title}</SelectItem>
                                                        )
                                                    })
                                                }
                                            </Select>
                                        </div>
                                    </div>
                                </CardBody>
                            </form>
                        </Card>
                    </Grid>
                </Grid>
            </Container>
            <Footer />
            <Backdrop open={loading} style={{ zIndex: 9999 }}>
                <CircularProgress color="primary" />
            </Backdrop>
        </>
    );
};

NewTicketPage.getLayout = (page: React.ReactNode) => <SidebarLayout>{page}</SidebarLayout>;

export const getServerSideProps = authMiddleware(async (_context: any) => {
    return {
        props: {},
    };
});

export default NewTicketPage;
