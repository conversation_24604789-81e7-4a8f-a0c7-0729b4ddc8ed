import { PrismaClient } from '@prisma/client';

const prisma = new PrismaClient();

export default async function handler(req, res) {
  if (req.method !== 'PUT') {
    return res.status(405).json({ message: 'Method not allowed' });
  }

  const { id, name, image, sponsorImage, type, bigPrize, percentage, number, remaining } = req.body;

  try {
    // Önce mevcut kaydı alalım
    const existingPrize = await prisma.wheelPrizes.findUnique({
      where: {
        id: parseInt(id),
      },
    });

    if (!existingPrize) {
      return res.status(404).json({ message: 'Ödül bulunamadı' });
    }

    // Güncellenecek verileri hazırlayalım
    const updateData = {
      name,
      image,
      sponsorImage,
      type,
      bigPrize,
      percentage: parseFloat(percentage),
      // Eğer number değeri varsa kullan, yoksa mevcut değeri koru
      number: number !== undefined ? (number ? parseInt(number) : 0) : existingPrize.number,
      // Eğer remaining değeri varsa kullan, yoksa mevcut değeri koru
      remaining: remaining !== undefined ? (remaining ? parseInt(remaining) : 0) : existingPrize.remaining,
    };

    const updatedPrize = await prisma.wheelPrizes.update({
      where: {
        id: parseInt(id),
      },
      data: updateData,
    });

    res.status(200).json({ message: 'Ödül güncellendi', updatedItem: updatedPrize });
  } catch (error) {
    console.error(error);
    res.status(500).json({ message: 'Internal server error', error });
  }
}
