import { PrismaClient } from '@prisma/client';

const prisma = new PrismaClient();

export default async function handler(req, res) {
    if (req.method === 'PUT') {
        const { id, name, rank, status } = req.body;

        try {
            const updatedCategory = await prisma.shopCategory.update({
                where: {
                    id: parseInt(id),
                },
                data: {
                    name: name,
                    rank: rank,
                    status: status,
                },
            });

            res.status(200).json({ message: '<PERSON><PERSON>i güncellendi!', updatedItem: updatedCategory });
        } catch (error) {
            console.error(error);
            res.status(500).json({ message: 'Internal server error', error });
        }
    } else {
        res.status(405).json({ message: 'Method not allowed' });
    }
}
