import React, { useEffect, useState } from "react";
import Head from "next/head";
import { useRouter } from "next/router";
import Swal from "sweetalert2";
import SidebarLayout from "@/layouts/SidebarLayout";

const NewDailyActivityPage = () => {
    const router = useRouter();
    const [item, setItem]: any = useState({});
    const [selectedImage, setSelectedImage] = useState(null);
    const [stages, setStages] = useState([]);
    const [vendors, setVendors] = useState([]);

    const daysOfWeek = {
        1: "Pazartesi",
        2: "<PERSON><PERSON>",
        3: "Ç<PERSON><PERSON>am<PERSON>",
        4: "Perşembe",
        5: "<PERSON>uma",
        6: "<PERSON><PERSON><PERSON><PERSON>",
        7: "Pazar",
    };

    const loadStages = async () => {
        const url = `/api/stages`;
        try {
            const res = await fetch(url);
            if (res.status === 200) {
                const json = await res.json();
                setStages(json.results);
            } else {
                Swal.fire({
                    icon: "error",
                    title: "<PERSON><PERSON>",
                    text: "Bir sorun meydana geldi!",
                });
            }
        } catch (error) {
            console.error(error);
        }
    };

    const loadVendors = async () => {
        const url = `/api/vendors`;
        try {
            const res = await fetch(url);
            if (res.status === 200) {
                const json = await res.json();
                setVendors(json.results);
            } else {
                Swal.fire({
                    icon: "error",
                    title: "Hata",
                    text: "Bir sorun meydana geldi!",
                });
            }
        } catch (error) {
            console.error(error);
        }
    };

    useEffect(() => {
        loadStages();
        loadVendors();
    }, []);

    const handleImageChange = (event: React.ChangeEvent<HTMLInputElement>) => {
        const file = event.target.files?.[0];
        if (file) {
            if (file.type.startsWith("image/")) {
                setSelectedImage(URL.createObjectURL(file));
                setItem({ ...item, image: file });
            } else {
                Swal.fire({
                    icon: "error",
                    title: "Hata",
                    text: "Seçilen format desteklenmiyor!",
                });
                return;
            }
        }
    };

    console.log(item)

    const handleInsert = async (event: React.FormEvent) => {
    event.preventDefault();

    if (!item.name || !item.description || !item.image || !item.gateDate) {
        Swal.fire({
            icon: "error",
            title: "Hata",
            text: "Lütfen tüm alanları doldurun ve bir resim seçin!",
        });
        return;
    }
    const formattedGateDate = new Date(item.gateDate).toISOString();
    try {
        const payload = {
            ...item,
            gateDate: formattedGateDate,
        };
        // İlk olarak veritabanı kaydını yapıyoruz
        const response = await fetch(`/api/daily-activities/insert`, {
            method: "POST",
            headers: {
                "Content-Type": "application/json",
            },
            body: JSON.stringify(payload),
        });

        if (!response.ok) {
            const error = await response.json();
            Swal.fire({
                icon: "error",
                title: "Hata",
                text: error.message || "Bir sorun meydana geldi!",
            });
            return;
        }

        const data = await response.json();
        const id = data.createdItem.id;

        // Resim yükleme işlemi
        const formData = new FormData();
        formData.append("image", item.image);
        formData.append("fileName", `daily_activities/${id}`);

        const imageUploadResponse = await fetch(
            `https://api.hollystone.com.tr/api/functions/upload`,
            {
                method: "POST",
                headers: {
                    Authorization: `Bearer ${localStorage.getItem("token")}`,
                },
                body: formData,
            }
        );

        if (imageUploadResponse.ok) {
            const uploadData = await imageUploadResponse.json();

            if (uploadData.type === "success") {
                Swal.fire({
                    icon: "success",
                    title: "Başarılı",
                    text: "Kayıt ve resim yükleme başarılı!",
                }).then(() => {
                    router.push("/daily-activities");
                });
            } else {
                Swal.fire({
                    icon: "error",
                    title: "Hata",
                    text: uploadData.error || "Resim yükleme başarısız!",
                });
            }
        } else {
            Swal.fire({
                icon: "error",
                title: "Hata",
                text: "Resim yükleme işlemi başarısız oldu!",
            });
        }
    } catch (error) {
        console.error("Bir hata oluştu:", error);
        Swal.fire({
            icon: "error",
            title: "Hata",
            text: "Bir sorun meydana geldi!",
        });
    }
};


    return (
        <div className="bg-gray-100 min-h-screen">
            <Head>
                <title>Günlük Etkinlik - Oluştur</title>
            </Head>
            <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
                <h1 className="text-3xl font-bold text-gray-800 mt-6 mb-4">Günlük Etkinlik Oluştur</h1>
                <form onSubmit={handleInsert} className="bg-white shadow-md rounded-lg p-8">
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
                        {/* Başlık */}
                        <div>
                            <label
                                className="block text-sm font-medium text-gray-700 mb-2"
                                htmlFor="title"
                            >
                                Başlık
                            </label>
                            <input
                                id="title"
                                type="text"
                                value={item.name || ""}
                                onChange={(e) => setItem({ ...item, name: e.target.value })}
                                className="w-full px-4 py-2 border border-gray-300 rounded-lg shadow-sm text-gray-700 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500 transition duration-300"
                                placeholder="Etkinlik Başlığı"
                            />
                        </div>
        
                        {/* Haftanın Günü */}
                        <div>
                            <label
                                className="block text-sm font-medium text-gray-700 mb-2"
                                htmlFor="dayOfWeek"
                            >
                                Haftanın Günü
                            </label>
                            <select
                                id="dayOfWeek"
                                value={item.dayOfWeek || ""}
                                onChange={(e) => setItem({ ...item, dayOfWeek: parseInt(e.target.value) })}
                                className="w-full px-4 py-2 border border-gray-300 rounded-lg shadow-sm text-gray-700 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500 transition duration-300"
                            >
                                <option value="">Seçin...</option>
                                {Object.keys(daysOfWeek).map((key) => (
                                    <option key={key} value={key}>
                                        {daysOfWeek[key]}
                                    </option>
                                ))}
                            </select>
                        </div>
                        
                        {/* Kapı Açılış */}
                        <div>
                            <label
                                className="block text-sm font-medium text-gray-700 mb-2"
                                htmlFor="gateDate"
                            >
                                Kapı Açılış Tarihi ve Saati
                            </label>
                            <input
                                id="gateDate"
                                type="datetime-local"
                                value={item.gateDate || ""}
                                onChange={(e) => setItem({ ...item, gateDate: e.target.value })}
                                className="w-full px-4 py-2 border border-gray-300 rounded-lg shadow-sm text-gray-700 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500 transition duration-300"
                            />
                        </div>                
        
                        {/* Sahne */}
                        <div>
                            <label
                                className="block text-sm font-medium text-gray-700 mb-2"
                                htmlFor="stage"
                            >
                                Sahne
                            </label>
                            <select
                                id="stage"
                                value={item.stageId || ""}
                                onChange={(e) => setItem({ ...item, stageId: parseInt(e.target.value) })}
                                className="w-full px-4 py-2 border border-gray-300 rounded-lg shadow-sm text-gray-700 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500 transition duration-300"
                            >
                                <option value="">Seçin...</option>
                                {stages.map((stage) => (
                                    <option key={stage.id} value={stage.id}>
                                        {stage.name}
                                    </option>
                                ))}
                            </select>
                        </div>
        
                        {/* Bayi */}
                        <div>
                            <label
                                className="block text-sm font-medium text-gray-700 mb-2"
                                htmlFor="vendor"
                            >
                                Bayi
                            </label>
                            <select
                                id="vendor"
                                value={item.vendorId || ""}
                                onChange={(e) => setItem({ ...item, vendorId: parseInt(e.target.value) })}
                                className="w-full px-4 py-2 border border-gray-300 rounded-lg shadow-sm text-gray-700 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500 transition duration-300"
                            >
                                <option value="">Seçin...</option>
                                {vendors.map((vendor) => (
                                    <option key={vendor.id} value={vendor.id}>
                                        {vendor.name}
                                    </option>
                                ))}
                            </select>
                        </div>
        
                        {/* Resim */}
                        <div>
                            <label
                                className="block text-sm font-medium text-gray-700 mb-2"
                                htmlFor="image"
                            >
                                Resim
                            </label>
                            <input
                                id="image"
                                type="file"
                                onChange={handleImageChange}
                                className="w-full px-4 py-2 border border-gray-300 rounded-lg shadow-sm text-gray-700 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500 transition duration-300"
                            />
                            {selectedImage && (
                                <img
                                    src={selectedImage}
                                    alt="Seçilen Resim"
                                    className="mt-4 rounded-lg w-40 h-40 object-cover"
                                />
                            )}
                        </div>
                    </div>
        
                    {/* Açıklama */}
                    <div className="mt-6">
                        <label
                            className="block text-sm font-medium text-gray-700 mb-2"
                            htmlFor="description"
                        >
                            Açıklama
                        </label>
                        <textarea
                            id="description"
                            value={item.description || ""}
                            onChange={(e) => setItem({ ...item, description: e.target.value })}
                            className="w-full px-4 py-2 border border-gray-300 rounded-lg shadow-sm text-gray-700 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500 transition duration-300"
                            rows={4}
                            placeholder="Etkinlik açıklamasını buraya yazın..."
                        ></textarea>
                    </div>
        
                    {/* Butonlar */}
                    <div className="mt-6 flex justify-end space-x-4">
                        <button
                            type="button"
                            onClick={() => router.push("/daily-activities")}
                            className="px-4 py-2 bg-gray-300 text-gray-800 rounded-lg shadow hover:bg-gray-400 transition duration-300"
                        >
                            İptal
                        </button>
                        <button
                            type="submit"
                            className="px-4 py-2 bg-indigo-600 text-white rounded-lg shadow hover:bg-indigo-700 transition duration-300"
                        >
                            Oluştur
                        </button>
                    </div>
                </form>
            </div>
        </div>
    );
};

NewDailyActivityPage.getLayout = (page: React.ReactNode) => (
    <SidebarLayout>{page}</SidebarLayout>
);

export default NewDailyActivityPage;
