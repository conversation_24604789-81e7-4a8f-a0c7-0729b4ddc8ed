import { PrismaClient } from '@prisma/client';

const prisma = new PrismaClient();

export default async function handler(req, res) {
    if (req.method !== 'GET') {
        return res.status(405).json({ message: 'Method not allowed' });
    }

    const { id, amount, vendorId, adminId } = req.query;

    try {
        const user: any = await prisma.user.findFirst({
            where: {
                id: parseInt(id),
            }
        });

        const settings: any = await prisma.setting.findFirst({
            where: {
                id: 1
            }
        })

        // Close the Prisma client connection
        await prisma.$disconnect();

        if (!user || !settings) {
            return res.status(404).json({ message: 'Kullanıcı bulunamadı' });
        }

        const balance = user.hollyPoints - amount;
        if (balance < 0) {
            return res.status(400).json({ message: 'Yet<PERSON>iz bakiye' });
        }

        await prisma.user.update({
            where: {
                id: parseInt(id),
            },
            data: {
                hollyPoints: balance,
            }
        })

        await prisma.hollyPointsSpendHistory.create({
            data: {
                userId: parseInt(id),
                vendorId: parseInt(vendorId),
                hollyPoints: parseInt(amount),
                spendTo: 3
            }
        })

        await prisma.cashierAction.create({
            data: {
                userId: parseInt(id),
                adminId: parseInt(adminId),
                type: "hollyPoint",
                amount: parseInt(amount)
            }
        })

        res.status(200).json({ result: true, message: `${amount} Holly Puan (${settings?.hollyPointsValue * amount} ₺) kullanıldı!` });
    } catch (error) {
        console.error(error);
        // Close the Prisma client connection
        await prisma.$disconnect();
        res.status(500).json({ message: 'Internal server error' });
    }
}
