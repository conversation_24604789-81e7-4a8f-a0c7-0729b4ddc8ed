import { Box, Container, Link, Typography, styled } from '@mui/material';

const FooterWrapper = styled(Container)(
  ({ theme }) => `
        margin-top: ${theme.spacing(4)};
`
);

function Footer() {
  return (
    <FooterWrapper className="footer-wrapper">
      <Box
        pb={4}
        display={{ xs: 'block', md: 'flex' }}
        alignItems="center"
        textAlign={{ xs: 'center', md: 'left' }}
        justifyContent="space-between"
      >
        <Box>
          <Typography variant="subtitle1">
            &copy; 2022 - Holly Stone Admin Panel
          </Typography>
        </Box>
        <Typography
          sx={{
            pt: { xs: 2, md: 0 }
          }}
          variant="subtitle1"
        >
          <Link
            href="https://www.instagram.com/yilgin_yazilim/"
            target="_blank"
            rel="noopener noreferrer"
          >
            HOLLY STONE BAR İŞLETMECİLİĞİ LTD. ŞTİ.
          </Link>
          {' '}tarafından geliştirilmiştir.
        </Typography>
      </Box>
    </FooterWrapper>
  );
}

export default Footer;
