// This is your Prisma schema file,
// learn more about it in the docs: https://pris.ly/d/prisma-schema

generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "mysql"
  url      = env("DATABASE_URL")
}

model Setting {
  id               Int      @id @default(autoincrement())
  hollyPointsValue String
  commission       Int
  createdAt        DateTime @default(now())
  updatedAt        DateTime @default(now()) @updatedAt

  @@map("settings")
}

model AppVersion {
  id            Int      @id @default(autoincrement()) // Benzersiz kimlik
  platform      String   // Platform bilgisi (ör: 'ios', 'android')
  version       String   // Sürüm numarası (ör: '1.0.0')
  updateMessage String   // Güncelleme mesajı
  updateUrl     String   // Güncelleme URL'si
  createdAt     DateTime @default(now()) // Kayıt oluşturulma tarihi
}


model Transaction {
  id              Int       @id @default(autoincrement())
  walletId        Int
  type            String
  kasaonay        Int
  amount          Float
  description     String    @db.VarChar(800)
  createdAt       DateTime  @default(now())
  cashback        Float
  usedHollyPoints Int?
  userId          Int

  user            User    @relation(fields: [userId], references: [id])
  wallet          Wallet    @relation(fields: [walletId], references: [id])
  
  @@map("transactions")
}

model Wallet {
  id              Int       @id @default(autoincrement())
  userId          Int
  user            User      @relation(fields: [userId], references: [id])
  balance         Float
  createdAt       DateTime  @default(now())
  cashback        Float
  Transaction     Transaction[]
  
  @@map("wallets")
}


model TurkeyCity {
  id                 Int                  @id @default(autoincrement())
  title              String
  key                Int                  @unique
  UserAddress        UserAddress[]
  UserBillingAddress UserBillingAddress[]

  @@map("turkey_cities")
}

model TurkeyDistrict {
  id                 Int                  @id @default(autoincrement())
  title              String
  key                Int                  @unique
  cityKey            Int
  UserAddress        UserAddress[]
  UserBillingAddress UserBillingAddress[]

  @@map("turkey_districts")
}

model TurkeyNeighborhood {
  id                 Int                  @id @default(autoincrement())
  title              String
  key                Int                  @unique
  districtKey        Int
  UserAddress        UserAddress[]
  UserBillingAddress UserBillingAddress[]

  @@map("turkey_neighborhoods")
}

model City {
  id                 Int                  @id @default(autoincrement())
  title              String
  rank               Int
  abbreviation       String
  isDeleted          Boolean
  createdAt          DateTime             @default(now())
  updatedAt          DateTime             @default(now()) @updatedAt
  Performer          Performer[]
  JobApplication     JobApplication[]
  ContactApplication ContactApplication[]
  Vendor             Vendor[]

  @@map("cities")
}

model Announcement {
  id        Int      @id @default(autoincrement())
  vendor    Vendor   @relation(fields: [vendorId], references: [id], onDelete: Cascade)
  vendorId  Int
  header    String?
  content   String?
  image     String
  detail    String?
  detailId  String?
  type      Int?
  isDeleted Boolean
  createdAt DateTime @default(now())
  updatedAt DateTime @default(now()) @updatedAt

  @@map("announcements")
}

model Stage {
  id            Int             @id @default(autoincrement())
  name          String
  isDeleted     Boolean
  createdAt     DateTime        @default(now())
  updatedAt     DateTime        @default(now()) @updatedAt
  DailyActivity DailyActivity[]
  Concert       Concert[]

  @@map("stages")
}

model User {
  id                        Int                         @id @default(autoincrement())
  cityId                    Int
  firstName                 String
  lastName                  String
  phoneNumber               String                      @unique
  email                     String                      @unique
  password                  String
  dateOfBirth               DateTime
  referralCode              String
  image                     String
  hollyPoints               Int
  status                    Boolean
  isDeleted                 Boolean
  cover                     String?
  chatdate                  DateTime?
  push_id                   String?
  createdAt                 DateTime                    @default(now())
  updatedAt                 DateTime                    @default(now()) @updatedAt
  ShopOrder                 ShopOrder[]
  UserTicket                UserTicket[]
  HollyPointsEarningHistory HollyPointsEarningHistory[]
  HollyPointsSpendHistory   HollyPointsSpendHistory[]
  PrizeWheelSpinHistory     PrizeWheelSpinHistory[]
  ShamanPrize               ShamanPrize[]
  CashierAction             CashierAction[]
  GiftCard                  GiftCard[]
  Wallet                    Wallet[]
  Transaction               Transaction[]
  Notifications             Notification[]

  @@map("users")
}

model Notification {
  id         Int      @id @default(autoincrement())
  userId     Int?     // Kullanıcı ID'si (nullable)
  user       User?    @relation(fields: [userId], references: [id], onDelete: Cascade)
  title      String   // Bildirim başlığı
  content    String   // Bildirim içeriği
  type       String   // Bildirim tipi
  innerType  String?  // İç tür (nullable)
  target     String?  // Hedef (nullable)
  createdAt  DateTime @default(now())
  updatedAt  DateTime @default(now()) @updatedAt

  @@map("notifications")
}

model HollyPointsEarningHistory {
  id          Int      @id @default(autoincrement())
  user        User     @relation(fields: [userId], references: [id], onDelete: Cascade)
  userId      Int
  vendor      Vendor   @relation(fields: [vendorId], references: [id], onDelete: Cascade)
  vendorId    Int
  hollyPoints Int
  earnFrom    Int
  createdAt   DateTime @default(now())
  updatedAt   DateTime @default(now()) @updatedAt

  @@map("holly_points_earning_history")
}

model HollyPointsSpendHistory {
  id          Int      @id @default(autoincrement())
  user        User     @relation(fields: [userId], references: [id], onDelete: Cascade)
  userId      Int
  vendor      Vendor   @relation(fields: [vendorId], references: [id], onDelete: Cascade)
  vendorId    Int
  hollyPoints Int
  spendTo     Int
  createdAt   DateTime @default(now())
  updatedAt   DateTime @default(now()) @updatedAt

  @@map("holly_points_spend_history")
}

model GiftCard {
  id        Int      @id @default(autoincrement())
  cityId    Int
  user      User     @relation(fields: [userId], references: [id], onDelete: Cascade)
  userId    Int
  type      Int
  code      String
  value     Float
  status    Boolean
  createdAt DateTime @default(now())
  updatedAt DateTime @default(now()) @updatedAt

  @@map("gift_cards")
}

model UserAddress {
  id             Int                @id @default(autoincrement())
  userId         Int
  title          String
  city           TurkeyCity         @relation(fields: [cityId], references: [key], onDelete: Cascade)
  cityId         Int
  district       TurkeyDistrict     @relation(fields: [districtId], references: [key], onDelete: Cascade)
  districtId     Int
  neighborhood   TurkeyNeighborhood @relation(fields: [neighborhoodId], references: [key], onDelete: Cascade)
  neighborhoodId Int
  postalCode     Int
  fullAddress    String
  isDeleted      Boolean
  createdAt      DateTime           @default(now())
  updatedAt      DateTime           @default(now()) @updatedAt
  ShopOrder      ShopOrder[]

  @@map("user_addresses")
}

model UserBillingAddress {
  id             Int                @id @default(autoincrement())
  userId         Int
  title          String
  city           TurkeyCity         @relation(fields: [cityId], references: [key], onDelete: Cascade)
  cityId         Int
  district       TurkeyDistrict     @relation(fields: [districtId], references: [key], onDelete: Cascade)
  districtId     Int
  neighborhood   TurkeyNeighborhood @relation(fields: [neighborhoodId], references: [key], onDelete: Cascade)
  neighborhoodId Int
  postalCode     Int
  fullAddress    String
  isDeleted      Boolean
  createdAt      DateTime           @default(now())
  updatedAt      DateTime           @default(now()) @updatedAt
  ShopOrder      ShopOrder[]

  @@map("user_billing_addresses")
}

model CashierAction {
  id           Int         @id @default(autoincrement())
  admin        Admin       @relation(fields: [adminId], references: [id], onDelete: Cascade)
  adminId      Int
  user         User        @relation(fields: [userId], references: [id], onDelete: Cascade)
  userId       Int
  type         CashierEnum
  giftCardCode String?
  amount       Float
  createdAt    DateTime    @default(now())
  updatedAt    DateTime    @updatedAt

  @@map("cashier_actions")
}

enum CashierEnum {
  hollyPoint
  giftCard
}

model Admin {
  id             Int             @id @default(autoincrement())
  vendor         Vendor          @relation(fields: [vendorId], references: [id], onDelete: Cascade)
  vendorId       Int
  username       String
  password       String
  email          String
  phone          String
  identityNumber String
  authorityLevel Int
  isDeleted      Boolean
  createdAt      DateTime        @default(now())
  updatedAt      DateTime        @default(now()) @updatedAt
  CashierAction  CashierAction[]

  @@map("admins")
}

model VendorAnnouncement {
  id        Int      @id @default(autoincrement())
  vendor    Vendor   @relation(fields: [vendorId], references: [id], onDelete: Cascade)
  vendorId  Int
  header    String
  topic     String
  content   String
  isRead    Boolean
  isDeleted Boolean
  createdAt DateTime @default(now())
  updatedAt DateTime @default(now()) @updatedAt

  @@map("vendor_announcements")
}

model Vendor {
  id                        Int                         @id @default(autoincrement())
  city                      City                        @relation(fields: [cityId], references: [id], onDelete: Cascade)
  cityId                    Int
  invoiceToken              String?
  name                      String
  email                     String
  phone                     String
  taxNumber                 String
  taxOffice                 String
  bankAccountName           String
  iban                      String
  zipCode                   String
  address                   String
  latitude                  Float?                    
  longitude                 Float?                    
  modules                   String?
  isDeleted                 Boolean
  active                    Boolean                    @default(true) // Aktif/deaktif durumu
  createdAt                 DateTime                    @default(now())
  updatedAt                 DateTime                    @default(now()) @updatedAt
  Admin                     Admin[]
  Concert                   Concert[]
  DailyActivity             DailyActivity[]
  Announcement              Announcement[]
  HollyPointsEarningHistory HollyPointsEarningHistory[]
  HollyPointsSpendHistory   HollyPointsSpendHistory[]
  VendorAnnouncement        VendorAnnouncement[]

  @@map("vendors")
}

model Concert {
  id                Int                 @id @default(autoincrement())
  stage             Stage               @relation(fields: [stageId], references: [id], onDelete: Cascade)
  stageId           Int
  vendor            Vendor              @relation(fields: [vendorId], references: [id], onDelete: Cascade)
  vendorId          Int
  name              String
  isDeleted         Boolean
  date              DateTime
  gateDate          DateTime
  description       String              @db.VarChar(800)
  image             String?             
  redirect          String?
  createdAt         DateTime            @default(now())
  updatedAt         DateTime            @default(now()) @updatedAt
  ConcertTicket     ConcertTicket[]
  RefundApplication RefundApplication[]

  @@map("concerts")
}

model ConcertTicket {
  id          Int          @id @default(autoincrement())
  concert     Concert      @relation(fields: [concertId], references: [id], onDelete: Cascade)
  concertId   Int
  title       String
  type        Boolean
  price       Float
  hollyPoints Int?
  quota       Int?
  isDeleted   Boolean
  createdAt   DateTime     @default(now())
  updatedAt   DateTime     @default(now()) @updatedAt
  UserTicket  UserTicket[]

  @@map("concert_tickets")
}

model UserTicket {
  id              Int               @id @default(autoincrement())
  ticket          ConcertTicket     @relation(fields: [ticketId], references: [id], onDelete: Cascade)
  ticketId        Int
  user            User              @relation(fields: [userId], references: [id], onDelete: Cascade)
  userId          Int
  bondedTo        Int?
  paidPrice       Float
  qrCode          String
  status          Int
  payment         Boolean
  createdAt       DateTime          @default(now())
  updatedAt       DateTime          @default(now()) @updatedAt
  PaymentTransfer PaymentTransfer[]

  @@map("user_tickets")
}

model Invoice {
  id                 Int      @id @default(autoincrement())
  orderId            Int
  orderType          Boolean
  orderCode          String
  orderDate          DateTime
  vendorId           Int
  billingName        String
  billingAddress     String
  billingTown        String
  billingCity        String
  billingMobilePhone String
  identityNumber     String
  email              String
  taxExcludingPrice  String
  taxIncludingPrice  String
  orderDetails       String @db.VarChar(800)
  efatura            Boolean
  taxNumber          Int?
  taxOffice          String?
  orderStatusId      Int
  createdAt          DateTime @default(now())
  updatedAt          DateTime @default(now()) @updatedAt

  @@map("invoices")
}

model PaymentTransfer {
  id         Int        @id @default(autoincrement())
  userTicket UserTicket @relation(fields: [ticketId], references: [id], onDelete: Cascade)
  ticketId   Int
  createdAt  DateTime   @default(now())
  updatedAt  DateTime   @default(now()) @updatedAt

  @@map("payment_transfer")
}

model DailyActivity {
  id          Int      @id @default(autoincrement())
  stage       Stage    @relation(fields: [stageId], references: [id], onDelete: Cascade)
  stageId     Int
  vendor      Vendor   @relation(fields: [vendorId], references: [id], onDelete: Cascade)
  vendorId    Int
  dayOfWeek   Int
  name        String
  isDeleted   Boolean
  gateDate    DateTime
  date        DateTime
  description String   @db.VarChar(800)
  image       String
  createdAt   DateTime @default(now())
  updatedAt   DateTime @default(now()) @updatedAt

  @@map("daily_activities")
}

model HollySnap {
  id           Int      @id @default(autoincrement())
  activityId   Int
  activityType Boolean
  images       String
  hollyPoints  Int?
  isDeleted    Boolean
  createdAt    DateTime @default(now())
  updatedAt    DateTime @default(now()) @updatedAt

  @@map("holly_snap")
}

model ShamanPrize {
  id                Int        @id @default(autoincrement())
  type              ShamanEnum
  startDate         DateTime
  endDate           DateTime
  prize             String
  prizeType         PrizeEnum
  prizeImage        String
  prizeSponsorImage String
  user              User?      @relation(fields: [winnerId], references: [id], onDelete: Cascade)
  winnerId          Int?
  delivery          Boolean
  isDeleted         Boolean    @default(false)
  createdAt         DateTime   @default(now())
  updatedAt         DateTime   @default(now()) @updatedAt

  @@map("shaman_prize")
}

model PrizeWheel {
  id        Int      @id @default(autoincrement())
  prizes    String
  isDeleted Boolean
  createdAt DateTime @default(now())
  updatedAt DateTime @default(now()) @updatedAt
  @@map("prize_wheel")
}

model WheelPrizes {
  id           Int       @id @default(autoincrement())
  name         String
  image        String?
  sponsorImage String?   // Sponsor resmi için yeni alan
  type         PrizeEnum
  bigPrize     Boolean
  percentage   Float?
  number       Int?
  remaining    Int?
  isDeleted    Boolean   @default(false)
  createdAt    DateTime  @default(now())
  updatedAt    DateTime  @updatedAt
  @@map("wheel_prizes")
}

enum PrizeEnum {
  product
  hollyPoints
  ticket
}

enum ShamanEnum {
  week
  month
  all
}

model PrizeWheelSpinHistory {
  id        Int      @id @default(autoincrement())
  user      User     @relation(fields: [userId], references: [id], onDelete: Cascade)
  userId    Int
  prize     String
  delivery  Boolean
  createdAt DateTime @default(now())
  updatedAt DateTime @default(now()) @updatedAt

  @@map("prize_wheel_spin_history")
}

model Agreement {
  id        Int      @id @default(autoincrement())
  agreement String
  text      String
  createdAt DateTime @default(now())
  updatedAt DateTime @default(now()) @updatedAt

  @@map("agreements")
}

model ShopCategory {
  id        Int      @id @default(autoincrement())
  name      String
  rank      Int
  status    Boolean
  isDeleted Boolean
  createdAt DateTime @default(now())
  updatedAt DateTime @default(now()) @updatedAt

  @@map("shop_categories")
}

model ShopProduct {
  id              Int         @id @default(autoincrement())
  vendorId        Int
  details         String?
  name            String
  description     String      @db.VarChar(800)
  images          String
  rank            Int
  taxRate	        Int
  price           Float
  discountedPrice Float?
  options         String?
  hollyPoints     Int?
  status          Boolean
  isDeleted       Boolean
  categoryId      Int
  createdAt       DateTime    @default(now())
  updatedAt       DateTime    @default(now()) @updatedAt
  OrderItem       OrderItem[]

  @@map("shop_products")
}

model ShopOrder {
  id                     Int                 @id @default(autoincrement())
  user                   User                @relation(fields: [userId], references: [id], onDelete: Cascade)
  userId                 Int
  address                UserAddress         @relation(fields: [addressId], references: [id], onDelete: Cascade)
  addressId              Int
  billingAddress         UserBillingAddress? @relation(fields: [billingAddressId], references: [id], onDelete: Cascade)
  billingAddressId       Int?
  billingType            Boolean
  taxNumber              Int?
  taxOffice              String?
  companyName            String?
  efatura                Boolean?
  trackingNumber         String?
  carrier                String?
  deliveryTrackingNumber String?
  estimatedDeliveryDate  DateTime?
  price                  Float
  cToken                 String
  cName                  String
  cLastFour              String
  cSchema                String
  hollyPoints            Int
  status                 Int
  payment                Boolean
  createdAt              DateTime            @default(now())
  updatedAt              DateTime            @default(now()) @updatedAt
  OrderItem              OrderItem[]

  @@map("orders")
}

model OrderItem {
  id             Int         @id @default(autoincrement())
  order          ShopOrder   @relation(fields: [orderId], references: [id], onDelete: Cascade)
  orderId        Int
  product        ShopProduct @relation(fields: [productId], references: [id], onDelete: Cascade)
  productId      Int
  quantity       Int
  price          Float
  selectedOption String
  createdAt      DateTime    @default(now())
  updatedAt      DateTime    @default(now()) @updatedAt

  @@map("order_items")
}

model RefundApplication {
  id             Int      @id @default(autoincrement())
  concert        Concert  @relation(fields: [concertId], references: [id], onDelete: Cascade)
  concertId      Int
  firstName      String
  lastName       String
  email          String
  phoneNumber    String
  identityNumber String
  birthDate      DateTime
  tickets        String
  reason         String
  status         Boolean  @default(false)
  isRead         Boolean  @default(false)
  isDeleted      Boolean  @default(false)
  createdAt      DateTime @default(now())
  updatedAt      DateTime @default(now()) @updatedAt

  @@map("refund_applications")
}

model Franchise {
  id               Int      @id @default(autoincrement())
  franchise        String
  nameLastName     String
  email            String
  birthDate        DateTime
  address          String
  phoneNumber      String
  landPhoneNumber  String?
  currentJob       String
  experience       String
  q1               String
  q2               String
  property         String
  areaSize         Int
  floorCount       Int
  roofHeight       Int
  buildingAddress  String
  investmentAmount String
  images           String
  isRead           Boolean  @default(false)
  isDeleted        Boolean  @default(false)
  createdAt        DateTime @default(now())
  updatedAt        DateTime @default(now()) @updatedAt

  @@map("franchise_applications")
}

model Performer {
  id           Int      @id @default(autoincrement())
  city         City     @relation(fields: [cityId], references: [id], onDelete: Cascade)
  cityId       Int
  nameLastName String
  phoneNumber  String
  email        String
  description  String   @db.VarChar(800)
  videos       String
  isRead       Boolean  @default(false)
  isDeleted    Boolean  @default(false)
  createdAt    DateTime @default(now())
  updatedAt    DateTime @default(now()) @updatedAt

  @@map("performer_applications")
}

model JobApplication {
  id           Int      @id @default(autoincrement())
  city         City     @relation(fields: [cityId], references: [id], onDelete: Cascade)
  cityId       Int
  nameLastName String
  phoneNumber  String
  email        String
  birthDate    DateTime
  resume       String
  profession   String
  description  String   @db.VarChar(800)
  isRead       Boolean  @default(false)
  isDeleted    Boolean  @default(false)
  createdAt    DateTime @default(now())
  updatedAt    DateTime @default(now()) @updatedAt

  @@map("job_applications")
}

model ContactApplication {
  id           Int      @id @default(autoincrement())
  city         City     @relation(fields: [cityId], references: [id], onDelete: Cascade)
  cityId       Int
  nameLastName String
  phoneNumber  String
  email        String
  topic        String
  message      String
  isRead       Boolean  @default(false)
  isDeleted    Boolean  @default(false)
  createdAt    DateTime @default(now())
  updatedAt    DateTime @default(now()) @updatedAt

  @@map("contact_applications")
}

