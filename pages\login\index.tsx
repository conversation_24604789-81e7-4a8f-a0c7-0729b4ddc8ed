import React, { useState } from 'react';
import Head from 'next/head';
import Swal from 'sweetalert2';
import { CircularProgress } from '@mui/material';
import { withRouter } from 'next/router';
import { authMiddlewareLogin } from '../../middleware';
import { setCookie } from 'cookies-next';
import { Box, Card, Typography, Container, Button, FormControl, OutlinedInput, styled } from '@mui/material';
import BaseLayout from 'src/layouts/BaseLayout';

const MainContent = styled(Box)(
    () => `
      height: 100%;
      display: flex;
      flex: 1;
      flex-direction: column;
    `
);

const TopWrapper = styled(Box)(
    ({ theme }) => `
      display: flex;
      width: 100%;
      flex: 1;
      align-items: center;
      justify-content: center;
      padding: ${theme.spacing(6)};
    `
);

const OutlinedInputWrapper = styled(OutlinedInput)(
    ({ theme }) => `
      background-color: ${theme.palette.grey[100]};
    `
);

function Login({ router }) {
    const [username, setUsername] = useState("");
    const [password, setPassword] = useState("");
    const [loading, setLoading] = useState(false);

    const handleLogin = async (event) => {
        event.preventDefault();
        setLoading(true);

        try {
            const response = await fetch('/api/login', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({ username, password }),
            });

            if (response.status === 200) {
                const json = await response.json();
                setCookie('token', json.token);
                setCookie('authorityLevel', json.authorityLevel);
                setCookie('modules', json.modules);
                setCookie('vendorId', json.vendorId);
                localStorage.setItem('userId', json.id);
                localStorage.setItem('token', json.token);
                localStorage.setItem('userName', json.userName);
                localStorage.setItem('email', json.email);
                localStorage.setItem('vendorId', json.vendorId);
                localStorage.setItem('authorityLevel', json.authorityLevel);
                localStorage.setItem('modules', json.modules);
                if (json.authorityLevel == 3) {
                    router.push('/cash-register');
                } else {
                    router.push('/');
                }
            } else {
                setLoading(false);
                console.error('Login failed');
                Swal.fire({
                    icon: 'error',
                    title: 'Hata',
                    text: 'Lütfen girdiğiniz bilgileri kontrol edin!',
                });
            }
        } catch (error) {
            setLoading(false);
            console.error('An error occurred:', error);
        }
    };

    return (
        <>
            <Head>
                <title>Login</title>
            </Head>
            <MainContent>
                <TopWrapper>
                    <Container maxWidth="sm">
                        <Box textAlign="center">
                            <Typography variant="h2" sx={{ my: 2 }}>
                                Giriş Yap
                            </Typography>
                        </Box>
                        <Container maxWidth="sm">
                            <Card sx={{ textAlign: 'center', mt: 3, p: 4 }}>
                                <form onSubmit={handleLogin}>
                                    <FormControl variant="outlined" fullWidth>
                                        <OutlinedInputWrapper
                                            sx={{ mb: 2 }}
                                            type="text"
                                            required
                                            value={username}
                                            onChange={(e: any) => { setUsername(e.target.value) }}
                                            placeholder="Kullanıcı adı..."
                                        />
                                    </FormControl>
                                    <FormControl variant="outlined" fullWidth>
                                        <OutlinedInputWrapper
                                            sx={{ mb: 2 }}
                                            type="password"
                                            required
                                            value={password}
                                            onChange={(e: any) => { setPassword(e.target.value) }}
                                            placeholder="Şifre..."
                                        />
                                    </FormControl>
                                    <Button type="submit" variant="outlined" disabled={loading} fullWidth>
                                        {loading ? <CircularProgress size={24} /> : 'Giriş Yap'}
                                    </Button>
                                </form>
                            </Card>
                        </Container>
                    </Container>
                </TopWrapper>
            </MainContent>
        </>
    );
}

export const getServerSideProps = authMiddlewareLogin(async (_context: any) => {
    const { query } = _context;
    const where = query.where || '/';
    return {
        props: {
            where
        },
    };
});

export default withRouter(Login); // Apply the withRouter HOC here

Login.getLayout = function getLayout(page) {
    return <BaseLayout>{page}</BaseLayout>;
};
