import { PrismaClient } from '@prisma/client';

const prisma = new PrismaClient();

export default async function handler(req, res) {
    if (req.method === 'POST') {
        const { title, rank, abbreviation } = req.body;

        try {
            const createdCity = await prisma.city.create({
                data: {
                    title,
                    rank,
                    abbreviation,
                    isDeleted: false
                },
            });

            res.status(201).json({ message: '<PERSON><PERSON><PERSON> eklendi!', createdItem: createdCity });
        } catch (error) {
            console.error(error);
            res.status(500).json({ message: 'Internal server error', error });
        }
    } else {
        res.status(405).json({ message: 'Method not allowed' });
    }
}
