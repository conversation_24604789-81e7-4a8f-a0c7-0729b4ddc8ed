import React, { useState, useEffect } from 'react';
import Head from 'next/head';
import { authMiddleware } from '../../middleware';
import SidebarLayout from '@/layouts/SidebarLayout';
import PageTitleWrapper from '@/components/PageTitleWrapper';
import { Typography, Container, Grid, CircularProgress } from '@mui/material';
import { Image, Card, CardBody, CardHeader, CardFooter, Button, Input, TimeInput, Textarea, Divider } from '@nextui-org/react';
import { Select, SelectItem } from "@nextui-org/select";
import Footer from '@/components/Footer';
import Swal from 'sweetalert2';
import { useRouter } from 'next/router';
import ArrowBackIcon from '@mui/icons-material/ArrowBack';
import Link from 'next/link';
import { Time } from "@internationalized/date";


const NewConcertPage = () => {
    const router = useRouter();
    const [item, setItem]: any = useState({  gateDate: new Time(20, 30), });
    const [stages, setStages] = useState([]);
    const [vendors, setVendors] = useState([]);
    const [selectedImage, setSelectedImage] = useState(null);
    const [imageLoading, setImageLoading] = useState(false);

    const loadStages = async () => {
        const url = `/api/stages`;

        try {
            const res = await fetch(url);

            if (res.status === 200) {
                const json = await res.json();
                setStages(json.results);
            } else {
                Swal.fire({
                    icon: 'error',
                    title: 'Hata',
                    text: 'Bir sorun meydana geldi!',
                });
            }
        } catch (error) {
            console.error(error);
        }
    };

    const loadVendors = async () => {
        const url = `/api/vendors`;

        try {
            const res = await fetch(url);

            if (res.status === 200) {
                const json = await res.json();
                setVendors(json.results);
            } else {
                Swal.fire({
                    icon: 'error',
                    title: 'Hata',
                    text: 'Bir sorun meydana geldi!',
                });
            }
        } catch (error) {
            console.error(error);
        }
    };

    useEffect(() => {
        loadStages();
        loadVendors();
    }, []);

    const handleImageChange = (event: React.ChangeEvent<HTMLInputElement>) => {
        const file = event.target.files?.[0];

        if (file) {
            if (file.type.startsWith('image/')) {
                setSelectedImage(URL.createObjectURL(file));
                setItem({ ...item, image: file });
            } else {
                Swal.fire({
                    icon: 'error',
                    title: 'Hata',
                    text: 'Seçilen format desteklenmiyor!',
                });
                return;
            }
        }
    };

    const handleInsert = async (event: React.FormEvent) => {
        event.preventDefault();
        if (!item.name && !item.description) {
            Swal.fire({
                icon: 'error',
                title: 'Hata',
                text: 'Lütfen alanları doldurun!',
            });
            return;
        }
        if (!item.image) {
            Swal.fire({
                icon: 'error',
                title: 'Hata',
                text: 'Lütfen bir resim seçin!',
            });
            return;
        }
        try {
            const response = await fetch(`/api/concerts/insert`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify(item),
            });

            if (response.ok) {
                const data = await response.json();
                const id = data.createdItem.id;

                setImageLoading(true);
                const formData = new FormData();
                formData.append('image', item.image);
                formData.append('fileName', `concerts/${id}`);

                try {
                    const response = await fetch(`https://api.hollystone.com.tr/api/functions/upload`, {
                        method: 'POST',
                        headers: {
                            'Authorization': `Bearer ${localStorage.getItem('token')}`
                        },
                        body: formData,
                    });

                    if (response.ok) {
                        const data = await response.json();

                        if (data.type == "success") {
                            Swal.fire({
                                icon: 'success',
                                title: 'Başarılı',
                                text: 'Kayıt başarılı!',
                            }).then(() => {
                                router.push('/concerts');
                            });
                        } else {
                            Swal.fire({
                                icon: 'error',
                                title: 'Hata',
                                text: data.error,
                            });
                        }
                    } else {
                        Swal.fire({
                            icon: 'error',
                            title: 'Hata',
                            text: 'Bir sorun meydana geldi!',
                        });
                    }
                } catch (error) {
                    Swal.fire({
                        icon: 'error',
                        title: 'Hata',
                        text: 'Bir sorun meydana geldi!',
                    });
                }
            } else {
                Swal.fire({
                    icon: 'error',
                    title: 'Hata',
                    text: 'Bir sorun meydana geldi!',
                });
            }
        } catch (error) {
            console.error('An error occurred:', error);
        }
        setImageLoading(false);
    };

    const renderContent = () => {
        return (
            <>
                <Head>
                    <title>Konserler - Oluştur</title>
                </Head>
                <PageTitleWrapper>
                    <Grid container alignItems="center">
                        <Grid item>
                            <Typography variant="h3" component="h3" gutterBottom>
                                Konser Oluştur
                            </Typography>
                        </Grid>
                    </Grid>
                </PageTitleWrapper>
                <Container>
                    <Grid
                        container
                        direction="row"
                        justifyContent="center"
                        alignItems="stretch"
                        spacing={4}
                    >
                        <Grid item xs={12}>
                            <Card
                                isBlurred
                                className="border-none bg-white dark:bg-default-100/50"
                                shadow="sm"
                            >
                                <form onSubmit={handleInsert}>
                                    <CardHeader className="flex justify-between gap-3">
                                        <div>
                                            <Link href="/concerts">
                                                <Button type="button" color="default" startContent={<ArrowBackIcon />}>
                                                    Konserler
                                                </Button>
                                            </Link>
                                        </div>
                                        <div>Konser Bilgileri</div>
                                        <div className="flex-end">
                                            <Button type="submit" color="primary">
                                                Oluştur
                                            </Button>
                                        </div>
                                    </CardHeader>

                                    <Divider />

                                    <CardBody>
                                        <div className="flex">
                                            <div className="relative w-1/2">
                                                <label className="block cursor-pointer">
                                                    {imageLoading ? (
                                                        <CircularProgress
                                                            size={48}
                                                            style={{
                                                                position: 'absolute',
                                                                top: '40%',
                                                                left: '40%',
                                                                transform: 'translate(-40%, -40%)',
                                                                zIndex: 999,
                                                            }}
                                                        />
                                                    ) : selectedImage ? (
                                                        <img
                                                            alt="Selected cover"
                                                            className="object-cover"
                                                            width={400}
                                                            src={selectedImage}
                                                            height="100%"
                                                        />
                                                    ) : (
                                                        <Image
                                                            width={400}
                                                            height={400}
                                                            radius="none"
                                                            loading="eager"
                                                            style={{
                                                                height: "100%"
                                                            }}
                                                            src={`https://api.hollystone.com.tr/resources/images/${item.image}`}
                                                            fallbackSrc="https://via.placeholder.com/400x400.png?text="
                                                        />
                                                    )}
                                                    <Input
                                                        type="file"
                                                        label="Resim"
                                                        name="image"
                                                        className="hidden"
                                                        onChange={handleImageChange}
                                                    />
                                                </label>
                                            </div>

                                            <div className="p-4 w-full">
                                                <div className="w-full mb-2 flex flex-row">
                                                    <Input
                                                        type="text"
                                                        label="Başlık"
                                                        name="name"
                                                        value={item.name}
                                                        className="mr-2"
                                                        onChange={(e: any) => setItem({ ...item, name: e.target.value })}
                                                    />
                                                    <Select
                                                        label="Sahne"
                                                        placeholder="Bir seçim yapın..."
                                                        defaultSelectedKeys={[item.stageId?.toString()]}
                                                        className="mr-2"
                                                        onChange={(e: any) => setItem({ ...item, stageId: parseInt(e.target.value) })}
                                                    >
                                                        {stages.map((stage, index) => {
                                                            return (
                                                                <SelectItem key={stage.id} value={index}>
                                                                    {stage.name}
                                                                </SelectItem>
                                                            )
                                                        })}
                                                    </Select>
                                                    <Select
                                                        label="Bayi"
                                                        placeholder="Bir seçim yapın..."
                                                        defaultSelectedKeys={[item.vendorId?.toString()]}
                                                        onChange={(e: any) => setItem({ ...item, vendorId: parseInt(e.target.value) })}
                                                    >
                                                        {vendors.map((vendor, index) => {
                                                            return (
                                                                <SelectItem key={vendor.id} value={index}>
                                                                    {vendor.name}
                                                                </SelectItem>
                                                            )
                                                        })}
                                                    </Select>
                                                </div>
                                                <div className="w-full mb-2 flex flex-row">
                                                <TimeInput
        label="Kapı Açılış Saati"
        name="gateDate"
        hourCycle={24}
        value={item.gateDate}
        onChange={(value: Time) => {
          setItem({ ...item, gateDate: value });
        }}
        className="mr-2"
      />
  <Input
    type="datetime-local"
    label="Konser Tarihi"
    name="date"
    value={item.date?.toISOString()?.slice(0, 16)}
    onChange={(e: any) => setItem({ ...item, date: new Date(e.target.value + 'Z') })}
  />
                                                </div>
                                                <Textarea
                                                    label="Açıklama"
                                                    name="description"
                                                    value={item.description}
                                                    className="mb-2"
                                                    onChange={(e: any) => setItem({ ...item, description: e.target.value })}
                                                />
                                                <Input
                                                    type="text"
                                                    label="Yönlendirme"
                                                    name="redirect"
                                                    value={item.redirect}
                                                    className="mb-2"
                                                    onChange={(e: any) => setItem({ ...item, redirect: e.target.value })}
                                                />
                                            </div>
                                        </div>
                                    </CardBody>
                                </form>

                                <CardFooter>
                                    Sanatçı profilleri
                                </CardFooter>
                            </Card>
                        </Grid>
                    </Grid>
                </Container>
                <Footer />
            </>
        );
    };

    return renderContent();
};

NewConcertPage.getLayout = (page: React.ReactNode) => <SidebarLayout>{page}</SidebarLayout>;

export const getServerSideProps = authMiddleware(async (_context: any) => {
    return {
        props: {},
    };
});

export default NewConcertPage;
