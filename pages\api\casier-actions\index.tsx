import { PrismaClient } from '@prisma/client';
import { startOfYear, startOfMonth, startOfWeek } from 'date-fns';

const prisma = new PrismaClient();

export default async function handler(req, res) {
    if (req.method !== 'GET') {
        return res.status(405).json({ message: 'Method not allowed' });
    }

    const { page = 1, pageSize = 10, filter = "", dateRange = 0 } = req.query;

    const currentDate = new Date();

    let startDate;

    switch (parseInt(dateRange)) {
        case 1: // This month
            startDate = startOfMonth(currentDate);
            break;
        case 2: // This week
            startDate = startOfWeek(currentDate);
            break;
        default: // This year (or any other value)
            startDate = startOfYear(currentDate);
            break;
    }

    const where = {
        user: {
            firstName: {
                contains: filter
            },
            lastName: {
                contains: filter
            }
        },
        createdAt: {
            gte: startDate,
        },
    };

    try {
        const actions = await prisma.cashierAction.findMany({
            where,
            include: {
                user: true,
                admin: {
                    include: {
                        vendor: true
                    }
                }
            },
            skip: (parseInt(page) - 1) * parseInt(pageSize),
            take: parseInt(pageSize),
        });

        const totalCount = await prisma.cashierAction.count({ where });
        // Close the Prisma client connection
        await prisma.$disconnect();

        res.status(200).json({ results: actions, totalCount });
    } catch (error) {
        console.error(error);
        // Close the Prisma client connection
        await prisma.$disconnect();
        res.status(500).json({ message: 'Internal server error' });
    }
}
