import { PrismaClient } from '@prisma/client';

const prisma = new PrismaClient();

export default async function handler(req, res) {
    if (req.method === 'PUT') {
        const { id, active, firstName, lastName, phoneNumber, email, dateOfBirth, hollyPoints } = req.body;

        try {
            const updatedUser = await prisma.user.update({
                where: {
                    id: parseInt(id),
                },
                data: {
                    status: active,
                    firstName,
                    lastName,
                    phoneNumber,
                    email,
                    dateOfBirth,
                    hollyPoints: parseInt(hollyPoints)
                },
            });

            res.status(200).json({ message: '<PERSON><PERSON><PERSON><PERSON><PERSON> güncellendi!', updatedItem: updatedUser });
        } catch (error) {
            console.error(error);
            res.status(500).json({ message: 'Internal server error', error });
        }
    } else {
        res.status(405).json({ message: 'Method not allowed' });
    }
}
