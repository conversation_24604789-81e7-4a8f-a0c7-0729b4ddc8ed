import React, { useState, useEffect } from 'react';
import <PERSON> from 'next/head';
import { authMiddleware } from 'middleware';
import Swal from 'sweetalert2';
import { Container, Grid, Typography } from '@mui/material';
import { Card, CardBody, CardHeader, Button, Input, Divider } from '@nextui-org/react';
import SidebarLayout from '@/layouts/SidebarLayout';
import PageTitleWrapper from '@/components/PageTitleWrapper';
import Footer from '@/components/Footer';

const EditProfilePage = () => {
    const [item, setItem]: any = useState({});
    const [loading, setLoading] = useState(true);

    const loadList = async (id: any) => {
        const url = `/api/admins?id=${id}`;

        try {
            const res: any = await fetch(url);

            if (res.status === 200) {
                const json = await res.json();
                setItem(json.results[0]);
            } else {
                Swal.fire({
                    icon: 'error',
                    title: '<PERSON><PERSON>',
                    text: res.message,
                });
            }
        } catch (error) {
            Swal.fire({
                icon: 'error',
                title: '<PERSON>a',
                text: 'Bir sorun meydana geldi!',
            });
            console.error(error);
        } finally {
            setLoading(false);
        }
    };

    useEffect(() => {
        loadList(localStorage.getItem("userId"));
    }, []);

    const handleUpdate = async (event) => {
        event.preventDefault();
        if (!item.username ||
            !item.email ||
            !item.phone ||
            !item.identityNumber ||
            !item.vendorId
        ) {
            Swal.fire({
                icon: 'error',
                title: 'Hata',
                text: 'Lütfen alanları doldurun!',
            });
            return;
        }
        try {
            const response = await fetch(`/api/admins/update`, {
                method: 'PUT',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify(item),
            });

            if (response.ok) {
                await loadList(localStorage.getItem("userId"));
                Swal.fire({
                    icon: 'success',
                    title: 'Başarılı',
                    text: 'Kayıt güncellendi!',
                });
            } else {
                const json = await response.json();
                Swal.fire({
                    icon: 'error',
                    title: 'Hata',
                    text: json.message,
                });
            }
        } catch (error) {
            console.error('An error occurred:', error);
        }
    }

    if (loading) {
        return "Loading";
    }

    return (
        <>
            <Head>
                <title>Profil Bilgileri</title>
            </Head>
            <PageTitleWrapper>
                <Grid container alignItems="center">
                    <Grid item>
                        <Typography variant="h3" component="h3" gutterBottom>
                            Profil Bilgileri
                        </Typography>
                    </Grid>
                </Grid>
            </PageTitleWrapper>
            <Container maxWidth="lg">
                <Grid
                    container
                    direction="row"
                    justifyContent="center"
                    alignItems="stretch"
                    spacing={4}
                >
                    <Grid item xs={12}>
                        <Card
                            isBlurred
                            className="border-none bg-white dark:bg-default-100/50 max-w-[610px]"
                            shadow="sm"
                        >
                            <form onSubmit={handleUpdate}>
                                <CardHeader className="flex justify-between gap-3">
                                    <div>
                                    </div>
                                    <div>Profil Bilgileri</div>
                                    <div className="flex-end">
                                        <Button type="submit" color="primary">
                                            Güncelle
                                        </Button>
                                    </div>
                                </CardHeader>

                                <Divider />

                                <CardBody>
                                    <div className="flex">
                                        <div className="p-4 w-full">
                                            <div className="w-full mb-2 flex flex-row">
                                                <Input
                                                    type="text"
                                                    label="Kullanıcı Adı"
                                                    name="username"
                                                    value={item.username}
                                                    className="mr-2"
                                                    onChange={(e: any) => setItem({ ...item, username: e.target.value })}
                                                />
                                                <Input
                                                    type="text"
                                                    label="E-mail"
                                                    name="email"
                                                    value={item.email}
                                                    className="mr-2"
                                                    onChange={(e: any) => setItem({ ...item, email: e.target.value })}
                                                />
                                                <Input
                                                    type="text"
                                                    label="Şifre"
                                                    placeholder="Değişmeyecekse boş bırakın..."
                                                    name="newPassword"
                                                    value={item.newPassword}
                                                    onChange={(e: any) => setItem({ ...item, newPassword: e.target.value })}
                                                />
                                            </div>
                                            <div className="w-full mb-2 flex flex-row">
                                                <Input
                                                    type="text"
                                                    label="Telefon Numarası"
                                                    name="phone"
                                                    value={item.phone}
                                                    className="mr-2"
                                                    onChange={(e: any) => setItem({ ...item, phone: e.target.value })}
                                                />
                                                <Input
                                                    type="text"
                                                    label="Kimlik Numarası"
                                                    name="identityNumber"
                                                    value={item.identityNumber}
                                                    onChange={(e: any) => setItem({ ...item, identityNumber: e.target.value })}
                                                />
                                            </div>
                                        </div>
                                    </div>
                                </CardBody>
                            </form>
                        </Card>
                    </Grid>
                </Grid>
            </Container>
            <Footer />
        </>
    );
};

EditProfilePage.getLayout = (page: React.ReactNode) => <SidebarLayout>{page}</SidebarLayout>;

export const getServerSideProps = authMiddleware(async (_context: any) => {
    return {
        props: {},
    };
});

export default EditProfilePage;
