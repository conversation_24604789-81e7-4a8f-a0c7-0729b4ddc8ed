import { useContext, useEffect, useState } from 'react';
import { useRouter } from 'next/router';
import { getCookie } from 'cookies-next';

import {
  ListSubheader,
  alpha,
  Box,
  List,
  styled,
  Button,
  Badge,
  ListItem
} from '@mui/material';
import Link from 'next/link';
import { SidebarContext } from 'src/contexts/SidebarContext';
import DesignServicesTwoToneIcon from '@mui/icons-material/DesignServicesTwoTone';
import MusicNoteIcon from '@mui/icons-material/MusicNote';
import EventNoteIcon from '@mui/icons-material/EventNote';
import PersonIcon from '@mui/icons-material/Person';
import PhotoCameraIcon from '@mui/icons-material/PhotoCamera';
import CardGiftcardIcon from '@mui/icons-material/CardGiftcard';
import InfoIcon from '@mui/icons-material/Info';
import GavelIcon from '@mui/icons-material/Gavel';
import SecurityIcon from '@mui/icons-material/Security';
import LoyaltyIcon from '@mui/icons-material/Loyalty';
import BackspaceIcon from '@mui/icons-material/Backspace';
import TextSnippetIcon from '@mui/icons-material/TextSnippet';
import CategoryIcon from '@mui/icons-material/Category';
import AddShoppingCartIcon from '@mui/icons-material/AddShoppingCart';
import ShoppingBagIcon from '@mui/icons-material/ShoppingBag';
import BusinessIcon from '@mui/icons-material/Business';
import RecordVoiceOverIcon from '@mui/icons-material/RecordVoiceOver';
import WorkIcon from '@mui/icons-material/Work';
import ThreePIcon from '@mui/icons-material/ThreeP';
import NotificationsActiveTwoToneIcon from '@mui/icons-material/NotificationsActiveTwoTone';
import LensBlurIcon from '@mui/icons-material/LensBlur';
import ConfirmationNumberIcon from '@mui/icons-material/ConfirmationNumber';
import CampaignIcon from '@mui/icons-material/Campaign';
import PriceCheckIcon from '@mui/icons-material/PriceCheck';
import StorefrontIcon from '@mui/icons-material/Storefront';
import ManageAccountsIcon from '@mui/icons-material/ManageAccounts';
import ApartmentIcon from '@mui/icons-material/Apartment';
import PriceChangeIcon from '@mui/icons-material/PriceChange';
import CurrencyBitcoinIcon from '@mui/icons-material/CurrencyBitcoin';
import EmojiEventsIcon from '@mui/icons-material/EmojiEvents';
import AdfScannerIcon from '@mui/icons-material/AdfScanner';

const MenuWrapper = styled(Box)(
  ({ theme }) => `
  .MuiList-root {
    padding: ${theme.spacing(1)};

    & > .MuiList-root {
      padding: 0 ${theme.spacing(0)} ${theme.spacing(1)};
    }
  }

    .MuiListSubheader-root {
      text-transform: uppercase;
      font-weight: bold;
      font-size: ${theme.typography.pxToRem(12)};
      color: ${theme.colors.alpha.trueWhite[50]};
      padding: ${theme.spacing(0, 2.5)};
      line-height: 1.4;
    }
`
);

const SubMenuWrapper = styled(Box)(
  ({ theme }) => `
    .MuiList-root {

      .MuiListItem-root {
        padding: 1px 0;

        .MuiBadge-root {
          position: absolute;
          right: ${theme.spacing(3.2)};

          .MuiBadge-standard {
            background: ${theme.colors.primary.main};
            font-size: ${theme.typography.pxToRem(10)};
            font-weight: bold;
            text-transform: uppercase;
            color: ${theme.palette.primary.contrastText};
          }
        }
    
        .MuiButton-root {
          display: flex;
          color: ${theme.colors.alpha.trueWhite[70]};
          background-color: transparent;
          width: 100%;
          justify-content: flex-start;
          padding: ${theme.spacing(1.2, 3)};

          .MuiButton-startIcon,
          .MuiButton-endIcon {
            transition: ${theme.transitions.create(['color'])};

            .MuiSvgIcon-root {
              font-size: inherit;
              transition: none;
            }
          }

          .MuiButton-startIcon {
            color: ${theme.colors.alpha.trueWhite[30]};
            font-size: ${theme.typography.pxToRem(20)};
            margin-right: ${theme.spacing(1)};
          }
          
          .MuiButton-endIcon {
            color: ${theme.colors.alpha.trueWhite[50]};
            margin-left: auto;
            opacity: .8;
            font-size: ${theme.typography.pxToRem(20)};
          }

          &.active,
          &:hover {
            background-color: ${alpha(theme.colors.alpha.trueWhite[100], 0.06)};
            color: ${theme.colors.alpha.trueWhite[100]};

            .MuiButton-startIcon,
            .MuiButton-endIcon {
              color: ${theme.colors.alpha.trueWhite[100]};
            }
          }
        }

        &.Mui-children {
          flex-direction: column;

          .MuiBadge-root {
            position: absolute;
            right: ${theme.spacing(7)};
          }
        }

        .MuiCollapse-root {
          width: 100%;

          .MuiList-root {
            padding: ${theme.spacing(1, 0)};
          }

          .MuiListItem-root {
            padding: 1px 0;

            .MuiButton-root {
              padding: ${theme.spacing(0.8, 3)};

              .MuiBadge-root {
                right: ${theme.spacing(3.2)};
              }

              &:before {
                content: ' ';
                background: ${theme.colors.alpha.trueWhite[100]};
                opacity: 0;
                transition: ${theme.transitions.create([
    'transform',
    'opacity'
  ])};
                width: 6px;
                height: 6px;
                transform: scale(0);
                transform-origin: center;
                border-radius: 20px;
                margin-right: ${theme.spacing(1.8)};
              }

              &.active,
              &:hover {

                &:before {
                  transform: scale(1);
                  opacity: 1;
                }
              }
            }
          }
        }
      }
    }
`
);

const NotificationsBadge = styled(Badge)(
  ({ theme }) => `
    
    .MuiBadge-badge {
        background-color: ${alpha(theme.palette.error.main, 0.1)};
        color: ${theme.palette.error.main};
        min-width: 16px; 
        height: 16px;
        padding: 0;

        &::after {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            border-radius: 50%;
            box-shadow: 0 0 0 1px ${alpha(theme.palette.error.main, 0.3)};
            content: "";
        }
    }
`
);

function SidebarMenu() {
  const { closeSidebar } = useContext(SidebarContext);
  const authorityLevel = parseInt(getCookie("authorityLevel"));
  const modules = getCookie("modules");
  const router = useRouter();
  const currentRoute = router.pathname;
  const [franchiseNotificaton, setFranchiseNotificaton] = useState(0);
  const [jobApplicationNotificaton, setJobApplicationNotificaton] = useState(0);
  const [performerNotificaton, setPerformerNotificaton] = useState(0);
  const [contactsNotificaton, setContactsNotificaton] = useState(0);
  const [isHollyShop, setIsHollyShop] = useState(false);

  const loadNotifications = async (url, setNotification) => {
    try {
      const res = await fetch(url);

      if (res.status === 200) {
        const json = await res.json();
        setNotification(json.totalCountUnRead);
      }
    } catch (error) {
      console.error(error);
    }
  };

  useEffect(() => {
    loadNotifications("/api/franchise", setFranchiseNotificaton);
    loadNotifications("/api/job-applications", setJobApplicationNotificaton);
    loadNotifications("/api/performer", setPerformerNotificaton);
    loadNotifications("/api/contact-applications", setContactsNotificaton);

    if (modules.length > 0) {
      const modulesArray = modules.split(',');
      if (modulesArray.includes("0")) {
        setIsHollyShop(true);
      }
    }
  }, []);

  return (
    <>
      <MenuWrapper>
        <List component="div">
          <SubMenuWrapper>
            <List component="div">
              {
                authorityLevel != 3 ? (
                  <>
                    <ListItem component="div">
                      <Link href="/" passHref>
                        <Button
                          className={currentRoute === '/' ? 'active' : ''}
                          disableRipple
                          component="a"
                          onClick={closeSidebar}
                          startIcon={<DesignServicesTwoToneIcon />}
                        >
                          Pano
                        </Button>
                      </Link>
                    </ListItem>
                  </>
                ) : <></>
              }
              {
                authorityLevel == 1 ? (
                  <>
                    <ListItem component="div">
                      <Link href="/city-reports" passHref>
                        <Button
                          className={currentRoute === '/city-reports' ? 'active' : ''}
                          disableRipple
                          component="a"
                          onClick={closeSidebar}
                          startIcon={<AdfScannerIcon />}
                        >
                          Şehir Rapor
                        </Button>
                      </Link>
                    </ListItem>
                    <ListItem component="div">
                      <Link href="/vendor-reports" passHref>
                        <Button
                          className={currentRoute === '/vendor-reports' ? 'active' : ''}
                          disableRipple
                          component="a"
                          onClick={closeSidebar}
                          startIcon={<AdfScannerIcon />}
                        >
                          Bayi Rapor
                        </Button>
                      </Link>
                    </ListItem>
                  </>
                ) : <></>
              }
            </List>
          </SubMenuWrapper>
        </List>
        {
          authorityLevel != 3 ? (
            <>
              <List
                component="div"
                subheader={
                  <ListSubheader component="div" disableSticky>
                    Holly Ticket
                  </ListSubheader>
                }
              >
                <SubMenuWrapper>
                  <List component="div">
                    {
                      authorityLevel == 1 ? (
                        <>
                          <ListItem component="div">
                            <Link href="/stages" passHref>
                              <Button
                                className={
                                  currentRoute.startsWith('/stages') ? 'active' : ''
                                }
                                disableRipple
                                component="a"
                                onClick={closeSidebar}
                                startIcon={<LensBlurIcon />}
                              >
                                Sahneler
                              </Button>
                            </Link>
                          </ListItem>
                          <ListItem component="div">
                            <Link href="/concerts" passHref>
                              <Button
                                className={
                                  currentRoute.startsWith('/concerts') ? 'active' : ''
                                }
                                disableRipple
                                component="a"
                                onClick={closeSidebar}
                                startIcon={<MusicNoteIcon />}
                              >
                                Konserler
                              </Button>
                            </Link>
                          </ListItem>
                        </>
                      ) : <></>
                    }
                    <ListItem component="div">
                      <Link href="/daily-activities" passHref>
                        <Button
                          className={
                            currentRoute.startsWith('/daily-activities') ? 'active' : ''
                          }
                          disableRipple
                          component="a"
                          onClick={closeSidebar}
                          startIcon={<EventNoteIcon />}
                        >
                          Günlük Etkinlikler
                        </Button>
                      </Link>
                    </ListItem>
                    <ListItem component="div">
                      <Link href="/upcoming-tickets" passHref>
                        <Button
                          className={
                            currentRoute.startsWith('/upcoming-tickets') ? 'active' : ''
                          }
                          disableRipple
                          component="a"
                          onClick={closeSidebar}
                          startIcon={<ConfirmationNumberIcon />}
                        >
                          Satış Listesi
                        </Button>
                      </Link>
                    </ListItem>
                    <ListItem component="div">
                      <Link href="/tickets" passHref>
                        <Button
                          className={
                            currentRoute.startsWith('/tickets') ? 'active' : ''
                          }
                          disableRipple
                          component="a"
                          onClick={closeSidebar}
                          startIcon={<ConfirmationNumberIcon />}
                        >
                          Satış Arşivi
                        </Button>
                      </Link>
                    </ListItem>
                  </List>
                </SubMenuWrapper>
              </List>
            </>
          ) : <></>
        }
        <List
          component="div"
          subheader={
            <ListSubheader component="div" disableSticky>
              Yönetim
            </ListSubheader>
          }
        >
          <SubMenuWrapper>
            <List component="div">
              {
                authorityLevel == 1 ? (
                  <>
                    <ListItem component="div">
                      <Link href="/announcements" passHref>
                        <Button
                          className={
                            currentRoute.startsWith('/announcements') ? 'active' : ''
                          }
                          disableRipple
                          component="a"
                          onClick={closeSidebar}
                          startIcon={<CampaignIcon />}
                        >
                          Duyurular
                        </Button>
                      </Link>
                    </ListItem>
                    <ListItem component="div">
                      <Link href="/transfer-payment" passHref>
                        <Button
                          className={
                            currentRoute.startsWith('/transfer-payment') ? 'active' : ''
                          }
                          disableRipple
                          component="a"
                          onClick={closeSidebar}
                          startIcon={<PriceCheckIcon />}
                        >
                          Bayi Ödeme Onayı
                        </Button>
                      </Link>
                    </ListItem>
                    <ListItem component="div">
                      <Link href="/vendors" passHref>
                        <Button
                          className={
                            currentRoute.startsWith('/vendors') ? 'active' : ''
                          }
                          disableRipple
                          component="a"
                          onClick={closeSidebar}
                          startIcon={<StorefrontIcon />}
                        >
                          Bayi Yönetimi
                        </Button>
                      </Link>
                    </ListItem>
                    <ListItem component="div">
                      <Link href="/cities" passHref>
                        <Button
                          className={
                            currentRoute.startsWith('/cities') ? 'active' : ''
                          }
                          disableRipple
                          component="a"
                          onClick={closeSidebar}
                          startIcon={<ApartmentIcon />}
                        >
                          Şehir Yönetimi
                        </Button>
                      </Link>
                    </ListItem>
                    <ListItem component="div">
                      <Link href="/admins" passHref>
                        <Button
                          className={
                            currentRoute.startsWith('/admins') ? 'active' : ''
                          }
                          disableRipple
                          component="a"
                          onClick={closeSidebar}
                          startIcon={<ManageAccountsIcon />}
                        >
                          Admin Yönetimi
                        </Button>
                      </Link>
                    </ListItem>
                    <ListItem component="div">
                      <Link href="/cashier-actions" passHref>
                        <Button
                          className={
                            currentRoute.startsWith('/cashier-actions') ? 'active' : ''
                          }
                          disableRipple
                          component="a"
                          onClick={closeSidebar}
                          startIcon={<PriceChangeIcon />}
                        >
                          Kasa İşlem Geçmişi
                        </Button>
                      </Link>
                    </ListItem>
                  </>
                ) : <></>
              }
              <ListItem component="div">
                <Link href="/cash-register" passHref>
                  <Button
                    className={
                      currentRoute.startsWith('/cash-register') ? 'active' : ''
                    }
                    disableRipple
                    component="a"
                    onClick={closeSidebar}
                    startIcon={<PriceChangeIcon />}
                  >
                    Kasa Yönetimi
                  </Button>
                </Link>
              </ListItem>
              <ListItem component="div">
                <Link href="/wallet-transctions" passHref>
                  <Button
                    className={
                      currentRoute.startsWith('/wallet-transctions') ? 'active' : ''
                    }
                    disableRipple
                    component="a"
                    onClick={closeSidebar}
                    startIcon={<PriceChangeIcon />}
                  >
                    Cüzdan Ödeme Takip
                  </Button>
                </Link>
              </ListItem>
            </List>
          </SubMenuWrapper>
        </List>
        {
          authorityLevel != 3 && isHollyShop == true ? (
            <>
              <List
                component="div"
                subheader={
                  <ListSubheader component="div" disableSticky>
                    Holly Shop
                  </ListSubheader>
                }
              >
                <SubMenuWrapper>
                  <List component="div">
                    {
                      authorityLevel == 1 ? (
                        <>
                          <ListItem component="div">
                            <Link href="/shop-categories" passHref>
                              <Button
                                className={
                                  currentRoute.startsWith('/shop-categories') ? 'active' : ''
                                }
                                disableRipple
                                component="a"
                                onClick={closeSidebar}
                                startIcon={<CategoryIcon />}
                              >
                                Kategoriler
                              </Button>
                            </Link>
                          </ListItem>
                        </>
                      ) : <></>
                    }
                    <ListItem component="div">
                      <Link href="/shop-products" passHref>
                        <Button
                          className={
                            currentRoute.startsWith('/shop-products') ? 'active' : ''
                          }
                          disableRipple
                          component="a"
                          onClick={closeSidebar}
                          startIcon={<AddShoppingCartIcon />}
                        >
                          Ürünler
                        </Button>
                      </Link>
                    </ListItem>
                    <ListItem component="div">
                      <Link href="/shop-orders" passHref>
                        <Button
                          className={
                            currentRoute.startsWith('/shop-orders') ? 'active' : ''
                          }
                          disableRipple
                          component="a"
                          onClick={closeSidebar}
                          startIcon={<ShoppingBagIcon />}
                        >
                          Siparişler
                        </Button>
                      </Link>
                    </ListItem>
                  </List>
                </SubMenuWrapper>
              </List>
            </>
          ) : <></>
        }
        {
          authorityLevel == 1 ? (
            <>
              <List
                component="div"
                subheader={
                  <ListSubheader component="div" disableSticky>
                    Kullanıcı Yönetimi
                  </ListSubheader>
                }
              >
                <SubMenuWrapper>
                  <List component="div">
                    <ListItem component="div">
                      <Link href="/users" passHref>
                        <Button
                          className={
                            currentRoute.startsWith('/users') ? 'active' : ''
                          }
                          disableRipple
                          component="a"
                          onClick={closeSidebar}
                          startIcon={<PersonIcon />}
                        >
                          Kullanıcı Listesi
                        </Button>
                      </Link>
                    </ListItem>
                    <ListItem component="div">
                      <Link href="/holly-points" passHref>
                        <Button
                          className={
                            currentRoute.startsWith('/holly-points') ? 'active' : ''
                          }
                          disableRipple
                          component="a"
                          onClick={closeSidebar}
                          startIcon={<CurrencyBitcoinIcon />}
                        >
                          Holly Puan Listesi
                        </Button>
                      </Link>
                    </ListItem>
                  </List>
                </SubMenuWrapper>
              </List>
              <List
                component="div"
                subheader={
                  <ListSubheader component="div" disableSticky>
                    Başvurular
                  </ListSubheader>
                }
              >
                <SubMenuWrapper>
                  <List component="div">
                    <ListItem component="div">
                      <Link href="/ticket-refund-applications" passHref>
                        <Button
                          className={
                            currentRoute.startsWith('/ticket-refund-applications') ? 'active' : ''
                          }
                          disableRipple
                          component="a"
                          onClick={closeSidebar}
                          startIcon={<ConfirmationNumberIcon />}
                        >
                          Bilet İade Başvurusu
                        </Button>
                      </Link>
                    </ListItem>
                    <ListItem component="div">
                      {franchiseNotificaton ? (
                        <NotificationsBadge
                          badgeContent={franchiseNotificaton}
                        >
                          <NotificationsActiveTwoToneIcon />
                        </NotificationsBadge>
                      ) : ""}
                      <Link href="/franchise" passHref>
                        <Button
                          className={
                            currentRoute.startsWith('/franchise') ? 'active' : ''
                          }
                          disableRipple
                          component="a"
                          onClick={closeSidebar}
                          startIcon={<BusinessIcon />}
                        >
                          Franchise
                        </Button>
                      </Link>
                    </ListItem>
                    <ListItem component="div">
                      {performerNotificaton ? (
                        <NotificationsBadge
                          badgeContent={performerNotificaton}
                        >
                          <NotificationsActiveTwoToneIcon />
                        </NotificationsBadge>
                      ) : ""}
                      <Link href="/performer" passHref>
                        <Button
                          className={
                            currentRoute.startsWith('/performer') ? 'active' : ''
                          }
                          disableRipple
                          component="a"
                          onClick={closeSidebar}
                          startIcon={<RecordVoiceOverIcon />}
                        >
                          Sanatçı
                        </Button>
                      </Link>
                    </ListItem>
                    <ListItem component="div">
                      {jobApplicationNotificaton ? (
                        <NotificationsBadge
                          badgeContent={jobApplicationNotificaton}
                        >
                          <NotificationsActiveTwoToneIcon />
                        </NotificationsBadge>
                      ) : ""}
                      <Link href="/job-applications" passHref>
                        <Button
                          className={
                            currentRoute.startsWith('/job-applications') ? 'active' : ''
                          }
                          disableRipple
                          component="a"
                          onClick={closeSidebar}
                          startIcon={<WorkIcon />}
                        >
                          İş Başvurusu
                        </Button>
                      </Link>
                    </ListItem>
                    <ListItem component="div">
                      {contactsNotificaton ? (
                        <NotificationsBadge
                          badgeContent={contactsNotificaton}
                        >
                          <NotificationsActiveTwoToneIcon />
                        </NotificationsBadge>
                      ) : ""}
                      <Link href="/contact-applications" passHref>
                        <Button
                          className={
                            currentRoute.startsWith('/contact-applications') ? 'active' : ''
                          }
                          disableRipple
                          component="a"
                          onClick={closeSidebar}
                          startIcon={<ThreePIcon />}
                        >
                          İletişim Mesajları
                        </Button>
                      </Link>
                    </ListItem>
                  </List>
                </SubMenuWrapper>
              </List>
              <List
                component="div"
                subheader={
                  <ListSubheader component="div" disableSticky>
                    Holly Snap
                  </ListSubheader>
                }
              >
                <SubMenuWrapper>
                  <List component="div">
                    <ListItem component="div">
                      <Link href="/hollysnap" passHref>
                        <Button
                          className={
                            currentRoute.startsWith('/hollysnap') ? 'active' : ''
                          }
                          disableRipple
                          component="a"
                          onClick={closeSidebar}
                          startIcon={<PhotoCameraIcon />}
                        >
                          Snap Liste
                        </Button>
                      </Link>
                    </ListItem>
                  </List>
                </SubMenuWrapper>
              </List>
              <List
                component="div"
                subheader={
                  <ListSubheader component="div" disableSticky>
                    Ödül Yönetimi
                  </ListSubheader>
                }
              >
                <SubMenuWrapper>
                  <List component="div">
                    <ListItem component="div">
                      <Link href="/weekly-shaman" passHref>
                        <Button
                          className={
                            currentRoute.startsWith('/weekly-shaman') ? 'active' : ''
                          }
                          disableRipple
                          component="a"
                          onClick={closeSidebar}
                          startIcon={<CardGiftcardIcon />}
                        >
                          Haftalık Şaman
                        </Button>
                      </Link>
                    </ListItem>
                    <ListItem component="div">
                      <Link href="/monthly-shaman" passHref>
                        <Button
                          className={
                            currentRoute.startsWith('/monthly-shaman') ? 'active' : ''
                          }
                          disableRipple
                          component="a"
                          onClick={closeSidebar}
                          startIcon={<CardGiftcardIcon />}
                        >
                          Aylık Şaman
                        </Button>
                      </Link>
                    </ListItem>
                    <ListItem component="div">
                      <Link href="/all-time-shaman" passHref>
                        <Button
                          className={
                            currentRoute.startsWith('/all-time-shaman') ? 'active' : ''
                          }
                          disableRipple
                          component="a"
                          onClick={closeSidebar}
                          startIcon={<CardGiftcardIcon />}
                        >
                          Tüm Zamanlar
                        </Button>
                      </Link>
                    </ListItem>
                    <ListItem component="div">
                      <Link href="/wheel" passHref>
                        <Button
                          className={
                            currentRoute.startsWith('/wheel') ? 'active' : ''
                          }
                          disableRipple
                          component="a"
                          onClick={closeSidebar}
                          startIcon={<CardGiftcardIcon />}
                        >
                          Çark
                        </Button>
                      </Link>
                    </ListItem>
                  </List>
                </SubMenuWrapper>
              </List>
              <List
                component="div"
                subheader={
                  <ListSubheader component="div" disableSticky>
                    Kazanım Yönetimi
                  </ListSubheader>
                }
              >
                <SubMenuWrapper>
                  <List component="div">
                    <ListItem component="div">
                      <Link href="/winners-weekly-shaman" passHref>
                        <Button
                          className={
                            currentRoute.startsWith('/winners-weekly-shaman') ? 'active' : ''
                          }
                          disableRipple
                          component="a"
                          onClick={closeSidebar}
                          startIcon={<EmojiEventsIcon />}
                        >
                          Haftalık Şaman
                        </Button>
                      </Link>
                    </ListItem>
                    <ListItem component="div">
                      <Link href="/winners-monthly-shaman" passHref>
                        <Button
                          className={
                            currentRoute.startsWith('/winners-monthly-shaman') ? 'active' : ''
                          }
                          disableRipple
                          component="a"
                          onClick={closeSidebar}
                          startIcon={<EmojiEventsIcon />}
                        >
                          Aylık Şaman
                        </Button>
                      </Link>
                    </ListItem>
                    <ListItem component="div">
                      <Link href="/winners-all-time-shaman" passHref>
                        <Button
                          className={
                            currentRoute.startsWith('/winners-all-time-shaman') ? 'active' : ''
                          }
                          disableRipple
                          component="a"
                          onClick={closeSidebar}
                          startIcon={<EmojiEventsIcon />}
                        >
                          Tüm Zamanlar
                        </Button>
                      </Link>
                    </ListItem>
                    <ListItem component="div">
                      <Link href="/winners-wheel" passHref>
                        <Button
                          className={
                            currentRoute.startsWith('/winners-wheel') ? 'active' : ''
                          }
                          disableRipple
                          component="a"
                          onClick={closeSidebar}
                          startIcon={<EmojiEventsIcon />}
                        >
                          Çark
                        </Button>
                      </Link>
                    </ListItem>
                  </List>
                </SubMenuWrapper>
              </List>
              <List
                component="div"
                subheader={
                  <ListSubheader component="div" disableSticky>
                    Bilgilendirme Yönetimi
                  </ListSubheader>
                }
              >
                <SubMenuWrapper>
                  <List component="div">
                    <ListItem component="div">
                      <Link href="/kvkk" passHref>
                        <Button
                          className={
                            currentRoute === '/kvkk' ? 'active' : ''
                          }
                          disableRipple
                          component="a"
                          onClick={closeSidebar}
                          startIcon={<TextSnippetIcon />}
                        >
                          KVKK Hakkında
                        </Button>
                      </Link>
                    </ListItem>
                  </List>
                  <List component="div">
                    <ListItem component="div">
                      <Link href="/refund" passHref>
                        <Button
                          className={
                            currentRoute === '/refund' ? 'active' : ''
                          }
                          disableRipple
                          component="a"
                          onClick={closeSidebar}
                          startIcon={<BackspaceIcon />}
                        >
                          İptal ve Iade
                        </Button>
                      </Link>
                    </ListItem>
                  </List>
                  <List component="div">
                    <ListItem component="div">
                      <Link href="/sale-agreement" passHref>
                        <Button
                          className={
                            currentRoute === '/sale-agreement' ? 'active' : ''
                          }
                          disableRipple
                          component="a"
                          onClick={closeSidebar}
                          startIcon={<LoyaltyIcon />}
                        >
                          Satış Sözleşmesi
                        </Button>
                      </Link>
                    </ListItem>
                  </List>
                  <List component="div">
                    <ListItem component="div">
                      <Link href="/privacy-agreement" passHref>
                        <Button
                          className={
                            currentRoute === '/privacy-agreement' ? 'active' : ''
                          }
                          disableRipple
                          component="a"
                          onClick={closeSidebar}
                          startIcon={<SecurityIcon />}
                        >
                          Gizlilik Sözleşmesi
                        </Button>
                      </Link>
                    </ListItem>
                  </List>
                  <List component="div">
                    <ListItem component="div">
                      <Link href="/terms-of-use" passHref>
                        <Button
                          className={
                            currentRoute === '/terms-of-use' ? 'active' : ''
                          }
                          disableRipple
                          component="a"
                          onClick={closeSidebar}
                          startIcon={<GavelIcon />}
                        >
                          Kullanım Koşulları
                        </Button>
                      </Link>
                    </ListItem>
                  </List>
                  <List component="div">
                    <ListItem component="div">
                      <Link href="/about-us" passHref>
                        <Button
                          className={
                            currentRoute === '/about-us' ? 'active' : ''
                          }
                          disableRipple
                          component="a"
                          onClick={closeSidebar}
                          startIcon={<InfoIcon />}
                        >
                          Hakkımızda
                        </Button>
                      </Link>
                    </ListItem>
                  </List>
                </SubMenuWrapper>
              </List>
            </>
          ) : <></>
        }
      </MenuWrapper>
    </>
  );
}

export default SidebarMenu;
