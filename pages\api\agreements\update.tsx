import { PrismaClient } from '@prisma/client';

const prisma = new PrismaClient();

export default async function handler(req, res) {
    if (req.method === 'PUT') {
        const { id, text } = req.body;

        try {
            const updatedAgreement = await prisma.agreement.update({
                where: {
                    id: parseInt(id),
                },
                data: {
                    text: text
                },
            });

            res.status(200).json({ message: 'Sözleşme güncellendi!', updatedItem: updatedAgreement });
        } catch (error) {
            console.error(error);
            res.status(500).json({ message: 'Internal server error', error });
        }
    } else {
        res.status(405).json({ message: 'Method not allowed' });
    }
}
