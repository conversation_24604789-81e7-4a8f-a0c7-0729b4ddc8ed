import { PrismaClient } from '@prisma/client';

const prisma = new PrismaClient();

export default async function handler(req, res) {
    if (req.method === 'PUT') {
        const { id, name } = req.body;

        try {
            const updatedStage = await prisma.stage.update({
                where: {
                    id: parseInt(id),
                },
                data: {
                    name: name,
                },
            });

            res.status(200).json({ message: '<PERSON>hne güncellen<PERSON>!', updatedItem: updatedStage });
        } catch (error) {
            console.error(error);
            res.status(500).json({ message: 'Internal server error', error });
        }
    } else {
        res.status(405).json({ message: 'Method not allowed' });
    }
}
