import React, { useState, useEffect } from 'react';
import <PERSON> from 'next/head';
import { authMiddleware } from 'middleware';
import Swal from 'sweetalert2';
import { Container, Grid, Typography } from '@mui/material';
import { Card, CardBody, CardHeader, Button, Divider } from '@nextui-org/react';
import SidebarLayout from '@/layouts/SidebarLayout';
import PageTitleWrapper from '@/components/PageTitleWrapper';
import Footer from '@/components/Footer';
import { Editor } from '@tinymce/tinymce-react';
import initFullProps from '../src/components/initFullProps';

const EditKVKKPage = () => {
    const [loading, setLoading] = useState(true);
    const [item, setItem]: any = useState({});

    const loadList = async () => {
        const url = `/api/agreements?id=5`;

        try {
            const res = await fetch(url);

            if (res.status === 200) {
                const json = await res.json();
                setItem(json.results[0]);
            } else {
                Swal.fire({
                    icon: 'error',
                    title: 'Hata',
                    text: 'Bir sorun meydana geldi!',
                });
            }
        } catch (error) {
            console.error(error);
        } finally {
            setLoading(false);
        }
    };

    useEffect(() => {
        loadList();
    }, []);

    const handleUpdate = async (event) => {
        event.preventDefault();
        const updatedData = {
            id: 5,
            text: item.text
        }
        try {
            const response = await fetch(`/api/agreements/update`, {
                method: 'PUT',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify(updatedData),
            });

            if (response.ok) {
                await loadList();
                Swal.fire({
                    icon: 'success',
                    title: 'Başarılı',
                    text: 'Kayıt güncellendi!',
                });
            } else {
                Swal.fire({
                    icon: 'error',
                    title: 'Hata',
                    text: 'Bir sorun meydana geldi!',
                });
            }
        } catch (error) {
            console.error('An error occurred:', error);
        }
    }

    if (loading) {
        return "Loading";
    }

    return (
        <>
            <Head>
                <title>Kullanım Koşulları</title>
            </Head>
            <PageTitleWrapper>
                <Grid container alignItems="center">
                    <Grid item>
                        <Typography variant="h3" component="h3" gutterBottom>
                            Kullanım Koşulları
                        </Typography>
                    </Grid>
                </Grid>
            </PageTitleWrapper>
            <Container maxWidth="lg">
                <Grid
                    container
                    direction="row"
                    justifyContent="center"
                    alignItems="stretch"
                    spacing={4}
                >
                    <Grid item xs={12}>
                        <Card
                            isBlurred
                            className="border-none bg-white dark:bg-default-100/50 max-w-[610px]"
                            shadow="sm"
                        >
                            <form onSubmit={handleUpdate}>
                                <CardHeader className="flex justify-between gap-3">
                                    <div></div>
                                    <div>Kullanım Koşulları</div>
                                    <div className="flex-end">
                                        <Button type="submit" color="primary">
                                            Güncelle
                                        </Button>
                                    </div>
                                </CardHeader>

                                <Divider />

                                <CardBody>
                                    <div className="flex">
                                        <div className="p-4 w-full">
                                            <Editor
                                                apiKey="xjpwhf7s5mz8s9db3a6pgy1i94ldaimjfxzlupgepjhb6kaw"
                                                initialValue={item.text}
                                                init={{ ...initFullProps }}
                                                onChange={(e: any) => setItem({ ...item, text: e.target.value })}
                                            />
                                        </div>
                                    </div>
                                </CardBody>
                            </form>
                        </Card>
                    </Grid>
                </Grid>
            </Container>
            <Footer />
        </>
    );
};

EditKVKKPage.getLayout = (page: React.ReactNode) => <SidebarLayout>{page}</SidebarLayout>;

export const getServerSideProps = authMiddleware(async (_context: any) => {
    return {
        props: {},
    };
});

export default EditKVKKPage;
