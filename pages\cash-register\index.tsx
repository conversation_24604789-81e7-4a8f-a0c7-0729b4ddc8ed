import React, { useEffect, useState } from 'react';
import Head from 'next/head';
import { authMiddleware } from 'middleware';
import { Container, Grid, Typography } from '@mui/material';
import { Card, CardBody, CardHeader, Button, Divider, Modal, ModalContent, ModalHeader, ModalBody, ModalFooter, Input } from '@nextui-org/react';
import SidebarLayout from '@/layouts/SidebarLayout';
import PageTitleWrapper from '@/components/PageTitleWrapper';
import Footer from '@/components/Footer';
import { QrReader } from 'react-qr-reader';
import Swal from 'sweetalert2';

const CashRegisterPage = () => {
    const [isOpen, setIsOpen] = useState(false);
    const [isOpen2, setIsOpen2] = useState(false);
    const [result, setResult]: any = useState<object | null>(null);
    const [giftCard, setGiftCard]: any = useState<string | null>(null);
    const [adminId, setAdminId]: any = useState(0);
    const [vendorId, setVendorId]: any = useState(0);
    const [amount, setAmount]: any = useState(0.0);

    useEffect(() => {
        setAdminId(localStorage.getItem('userId'));
        setVendorId(localStorage.getItem('vendorId'));
    }, []);

    const handleUseBalance = () => {
        const url = `/api/use-balance?id=${result?.userId}&amount=${result?.amount}&vendorId=${vendorId}&adminId=${adminId}`;
        fetch(url)
            .then((response) => response.json())
            .then((data) => {
                if (data.result === true) {
                    Swal.fire({
                        title: 'Kasa Durumu',
                        text: data.message,
                        icon: 'success',
                        confirmButtonText: 'Tamam',
                    });
                } else {
                    Swal.fire({
                        title: 'Kasa Durumu',
                        text: data.message,
                        icon: 'error',
                        confirmButtonText: 'Tamam',
                    });
                }
            });
        setResult(null);
    }

    const handleUseGiftCard = () => {
        const url = `/api/use-gift-card?code=${giftCard}&amount=${amount}&adminId=${adminId}`;
        fetch(url)
            .then((response) => response.json())
            .then((data) => {
                if (data.result === true) {
                    Swal.fire({
                        title: 'Kasa Durumu',
                        text: data.message,
                        icon: 'success',
                        confirmButtonText: 'Tamam',
                    });
                } else {
                    Swal.fire({
                        title: 'Kasa Durumu',
                        text: data.message,
                        icon: 'error',
                        confirmButtonText: 'Tamam',
                    });
                }
            });
        setGiftCard(null);
        setAmount(0.0);
    }

    const checkBalance = () => {
        const url = `/api/check-balance?id=${result?.userId}&amount=${result?.amount}`;
        fetch(url)
            .then((response) => response.json())
            .then((data) => {
                if (data.result === true) {
                    Swal.fire({
                        title: 'Kasa Durumu',
                        text: data.message,
                        icon: 'success',
                        showDenyButton: true,
                        confirmButtonText: 'Evet',
                        denyButtonText: 'Hayır',
                    }).then((result) => {
                        if (result.isConfirmed) {
                            handleUseBalance();
                        } else if (result.isDenied) {
                            Swal.fire({
                                title: 'Başarısız',
                                text: 'İşlem iptal edildi!',
                                icon: 'info',
                                confirmButtonText: 'Tamam',
                            })
                        }
                    })
                } else {
                    Swal.fire({
                        title: 'Kasa Durumu',
                        text: data.message,
                        icon: 'error',
                        confirmButtonText: 'Tamam',
                    });
                }
            });
        setResult(null);
    }

    const checkGiftCard = () => {
        const url = `/api/check-gift-card?code=${giftCard}&amount=${amount}`;
        fetch(url)
            .then((response) => response.json())
            .then((data) => {
                if (data.result === true) {
                    Swal.fire({
                        title: 'Kasa Durumu',
                        text: data.message,
                        icon: 'success',
                        showDenyButton: true,
                        confirmButtonText: 'Evet',
                        denyButtonText: 'Hayır',
                    }).then((result) => {
                        if (result.isConfirmed) {
                            handleUseGiftCard();
                        } else if (result.isDenied) {
                            Swal.fire({
                                title: 'Başarısız',
                                text: 'İşlem iptal edildi!',
                                icon: 'info',
                                confirmButtonText: 'Tamam',
                            })
                        }
                    })
                } else {
                    Swal.fire({
                        title: 'Kasa Durumu',
                        text: data.message,
                        icon: 'error',
                        confirmButtonText: 'Tamam',
                    });
                }
            });
        setGiftCard(null);
        setAmount(0.0);
    }

    useEffect(() => {
        if (result) {
            checkBalance();
        }
    }, [result]);

    const onOpen = () => {
        setIsOpen(true);
    };

    const onClose = () => {
        setIsOpen(false);
    };

    const onOpenChange = (newIsOpen) => {
        console.log('isOpen changed to', newIsOpen);
    };

    const onOpen2 = () => {
        setIsOpen2(true);
    };

    const onClose2 = () => {
        setIsOpen2(false);
    };

    const onOpenChange2 = (newIsOpen) => {
        console.log('isOpen changed to', newIsOpen);
    };

    return (
        <>
            <Head>
                <title>Kasa Yönetimi</title>
            </Head>
            <PageTitleWrapper>
                <Grid container alignItems="center">
                    <Grid item>
                        <Typography variant="h3" component="h3" gutterBottom>
                            Kasa Yönetimi
                        </Typography>
                    </Grid>
                </Grid>
            </PageTitleWrapper>
            <Container maxWidth="lg">
                <Grid
                    container
                    direction="row"
                    justifyContent="center"
                    alignItems="stretch"
                    spacing={4}
                >
                    <Grid item xs={12}>
                        <Card
                            isBlurred
                            className="border-none bg-white dark:bg-default-100/50 max-w-[610px]"
                            shadow="sm"
                        >
                            <CardHeader className="flex justify-between gap-3">
                                <div>
                                </div>
                                <div>Kasa Yönetimi</div>
                                <div className="flex-end">
                                </div>
                            </CardHeader>

                            <Divider />

                            <CardBody>
                                <div className="flex">
                                    <div className="p-4 w-full">
                                        <div style={{ maxWidth: '400px' }}>
                                            <Button onClick={onOpen} className="mr-2">Holly Puan Kullan</Button>
                                            <Button onClick={onOpen2}>Gift Card Kullan</Button>
                                        </div>
                                    </div>
                                </div>
                            </CardBody>
                        </Card>
                    </Grid>
                </Grid>
            </Container>
            <Modal isOpen={isOpen} onOpenChange={onOpenChange} onClose={onClose} backdrop="opaque">
                <ModalContent>
                    <ModalHeader className="flex flex-col gap-1">QR Okut</ModalHeader>
                    <ModalBody>
                        <QrReader
                            onResult={(result: any, error: any) => {
                                if (!!result) {
                                    try {
                                        const parsedData = JSON.parse(result?.text);
                                        setResult(parsedData);
                                    } catch (error) {
                                        Swal.fire({
                                            icon: 'error',
                                            title: 'Hata',
                                            text: 'QR Formatı Hatalı!',
                                            confirmButtonText: 'Tamam',
                                            confirmButtonColor: 'red',
                                            showConfirmButton: true,
                                            allowOutsideClick: false,
                                        });
                                    }
                                    onClose();
                                }

                                if (!!error) {
                                    console.info(error);
                                }
                            }}
                            constraints={undefined}
                        />
                    </ModalBody>
                    <ModalFooter>
                        <Button color="danger" variant="light" onPress={onClose}>
                            Vazgeç
                        </Button>
                    </ModalFooter>
                </ModalContent>
            </Modal>
            <Modal isOpen={isOpen2} onOpenChange={onOpenChange2} onClose={onClose2} backdrop="opaque">
                <ModalContent>
                    <ModalHeader className="flex flex-col gap-1">QR Okut</ModalHeader>
                    <ModalBody>
                        <Input
                            type="text"
                            label="Gift Card Numarası"
                            placeholder="Gift Card Numarası"
                            value={giftCard}
                            onChange={(e) => setGiftCard(e.target.value)}
                        />
                        <Input
                            type="number"
                            label="Kullanılacak miktar"
                            placeholder="Kullanılacak miktar"
                            value={amount}
                            onChange={(e) => setAmount(e.target.value)}
                        />
                    </ModalBody>
                    <ModalFooter>
                        <Button color="danger" variant="light" onPress={onClose2}>
                            Vazgeç
                        </Button>
                        <Button color="primary" variant="light" onPress={checkGiftCard}>
                            Kontrol Et
                        </Button>
                    </ModalFooter>
                </ModalContent>
            </Modal>
            <Footer />
        </>
    );
};

CashRegisterPage.getLayout = (page: React.ReactNode) => <SidebarLayout>{page}</SidebarLayout>;

export const getServerSideProps = authMiddleware(async (_context: any) => {
    return {
        props: {},
    };
});

export default CashRegisterPage;
