import { PrismaClient } from '@prisma/client';

const prisma = new PrismaClient();

export default async function handler(req, res) {
    if (req.method === 'PUT') {
        const { id, header, content, detail, detailId, vendorId  } = req.body;

        try {
            const updatedAnnouncement = await prisma.announcement.update({
                where: {
                    id: parseInt(id),
                },
                data: {
                    vendorId,
                    header,
                    content,
                    detail,
                    detailId
                },
            });

            res.status(200).json({ message: '<PERSON><PERSON><PERSON> güncellendi!', updatedItem: updatedAnnouncement });
        } catch (error) {
            console.error(error);
            res.status(500).json({ message: 'Internal server error', error });
        }
    } else {
        res.status(405).json({ message: 'Method not allowed' });
    }
}
