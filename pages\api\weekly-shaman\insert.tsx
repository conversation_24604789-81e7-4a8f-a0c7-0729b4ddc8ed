import { PrismaClient } from '@prisma/client';

const prisma = new PrismaClient();

export default async function handler(req, res) {
    if (req.method === 'POST') {
        const { startDate, endDate, prize, prizeType } = req.body;

        try {
            const createdPrize = await prisma.shamanPrize.create({
                data: {
                    startDate,
                    endDate,
                    prize: prize.toString(),
                    prizeType,
                    prizeImage: "",
                    prizeSponsorImage: "",
                    delivery: false,
                    winnerId: 0,
                    type: "week",
                    isDeleted: false
                },
            });

            await prisma.shamanPrize.update({
                where: {
                    id: createdPrize.id,
                },
                data: {
                    prizeImage: `shaman/p${createdPrize.id}.webp`,
                    prizeSponsorImage: `shaman/s${createdPrize.id}.webp`,
                },
            });

            res.status(201).json({ message: 'Şaman eklendi!', createdItem: createdPrize });
        } catch (error) {
            console.error(error);
            res.status(500).json({ message: 'Internal server error', error });
        }
    } else {
        res.status(405).json({ message: 'Method not allowed' });
    }
}
