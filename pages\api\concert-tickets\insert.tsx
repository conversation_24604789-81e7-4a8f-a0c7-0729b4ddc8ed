import { PrismaClient } from '@prisma/client';

const prisma = new PrismaClient();

export default async function handler(req, res) {
    if (req.method === 'POST') {
        const { concertId, title, hollyPoints, price, quota, type } = req.body;

        try {
            const createdTicket = await prisma.concertTicket.create({
                data: {
                    concertId,
                    title,
                    hollyPoints,
                    price: parseFloat(price),
                    quota,
                    type,
                    isDeleted: false
                },
            });

            res.status(201).json({ message: 'Bilet eklendi!', createdItem: createdTicket });
        } catch (error) {
            console.error(error);
            res.status(500).json({ message: 'Internal server error', error });
        }
    } else {
        res.status(405).json({ message: 'Method not allowed' });
    }
}
