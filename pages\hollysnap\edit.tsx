import React, { useState, useEffect } from 'react';
import Head from 'next/head';
import { useRouter } from 'next/router';
import { authMiddleware } from 'middleware';
import Swal from 'sweetalert2';
import { Container, Grid, Typography, CircularProgress } from '@mui/material';
import { Image, Card, CardBody, CardHeader, Button, Input, Divider, Accordion, AccordionItem } from '@nextui-org/react';
import { Select, SelectItem } from "@nextui-org/select";
import SidebarLayout from '@/layouts/SidebarLayout';
import PageTitleWrapper from '@/components/PageTitleWrapper';
import Footer from '@/components/Footer';
import { PlusIcon } from '@/components/PlusIcon';
import DeleteForeverIcon from '@mui/icons-material/DeleteForever';
import ArrowBackIcon from '@mui/icons-material/ArrowBack';
import Link from 'next/link';

const EditSnapPage = () => {
    const router = useRouter();
    const [item, setItem]: any = useState({});
    const { id } = router.query;
    const [loading, setLoading] = useState(true);
    const [imageLoading, setImageLoading] = useState(false);
    const [currentTimestamp, setCurrentTimestamp] = useState(new Date().getTime());
    const [dropdownItems, setDropdownItems] = useState([]);

    const loadList = async () => {
        const url = `/api/hollysnap?id=${id}`;

        try {
            const res = await fetch(url);

            if (res.status === 200) {
                const json = await res.json();
                json.results[0].images = json.results[0]?.images.split(',');
                setItem(json.results[0]);
            } else {
                Swal.fire({
                    icon: 'error',
                    title: 'Hata',
                    text: 'Bir sorun meydana geldi!',
                });
            }
        } catch (error) {
            console.error(error);
        } finally {
            setLoading(false);
        }
    };

    const loadConcerts = async () => {
        const url = `/api/concerts`;

        try {
            const res = await fetch(url);

            if (res.status === 200) {
                const json = await res.json();
                setDropdownItems(json.results);
            } else {
                Swal.fire({
                    icon: 'error',
                    title: 'Hata',
                    text: 'Bir sorun meydana geldi!',
                });
            }
        } catch (error) {
            console.error(error);
        }
    };

    const loadDailyActivities = async () => {
        const url = `/api/daily-activities`;

        try {
            const res = await fetch(url);

            if (res.status === 200) {
                const json = await res.json();
                setDropdownItems(json.results);
            } else {
                Swal.fire({
                    icon: 'error',
                    title: 'Hata',
                    text: 'Bir sorun meydana geldi!',
                });
            }
        } catch (error) {
            console.error(error);
        }
    };

    const fetchList = async () => {
        await loadList();
    };

    const fetchData = async () => {
        if (item.activityType === false) {
            await loadConcerts();
        } else {
            await loadDailyActivities();
        }
    };

    useEffect(() => {
        fetchList();
    }, []);

    useEffect(() => {
        fetchData();
    }, [item.activityType]);

    const updateImage = async () => {
        const updatedData = {
            id: id,
            images: item.images.join(",")
        }
        try {
            const response = await fetch(`/api/hollysnap/update`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify(updatedData),
            });

            if (response.ok) {
                Swal.fire({
                    icon: 'success',
                    title: 'Başarılı',
                    text: 'Resim listesi güncellendi!',
                });
            } else {
                Swal.fire({
                    icon: 'error',
                    title: 'Hata',
                    text: 'Bir sorun meydana geldi!',
                });
            }
        } catch (error) {
            console.error('An error occurred:', error);
        }
    }

    const handleNewImage = async () => {
        const latestImage = item.images[item.images.length - 1];
        const lastImageParts = latestImage.split('-');
        const lastImageNumber = parseInt(lastImageParts[1].replace(".webp", ""));
        let newImages = item.images;
        newImages.push(`holly_snap/${id}-${lastImageNumber + 1}.webp`);
        setItem({ ...item, images: newImages })
        await updateImage();
    }

    const handleDeleteImage = async (key) => {
        const shouldDelete = await Swal.fire({
            title: 'Dikkat!',
            text: 'Bu resmi silmek istediğine gerçekten emin misin?',
            icon: 'warning',
            showCancelButton: true,
            confirmButtonText: 'Evet, sil!',
            cancelButtonText: 'Vazgeç',
        });

        if (shouldDelete.isConfirmed) {
            let newImages = item.images;
            const filePath = newImages[key];

            try {
                const response = await fetch(`https://api.hollystone.com.tr/api/functions/delete`, {
                    method: 'POST',
                    headers: {
                        'Authorization': `Bearer ${localStorage.getItem('token')}`,
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({ fileName: filePath })
                });

                if (response.ok) {
                    const data = await response.json();

                    if (data.type == "success") {
                        newImages.splice(key, 1);
                        setItem({ ...item, images: newImages })
                        await updateImage();
                        Swal.fire({
                            icon: 'success',
                            title: 'Başarılı',
                            text: 'Resim güncellendi!',
                        });
                    } else {
                        Swal.fire({
                            icon: 'error',
                            title: 'Hata',
                            text: data.error,
                        });
                    }
                } else {
                    Swal.fire({
                        icon: 'error',
                        title: 'Hata',
                        text: 'Bir sorun meydana geldi!',
                    });
                }
            } catch (error) {
                Swal.fire({
                    icon: 'error',
                    title: 'Hata',
                    text: 'Bir sorun meydana geldi!',
                });
            }
        }
    }

    const handleImageChange = async (event, image) => {
        const file = event.target.files[0];
        const filePath = image.split('.');

        if (file) {
            if (!file.type.startsWith('image/')) {
                Swal.fire({
                    icon: 'error',
                    title: 'Hata',
                    text: 'Seçilen format desteklenmiyor!',
                });
                return;
            }
            setImageLoading(true);
            const formData = new FormData();
            formData.append('image', file);
            formData.append('fileName', filePath[0]);

            try {
                const response = await fetch(`https://api.hollystone.com.tr/api/functions/upload`, {
                    method: 'POST',
                    headers: {
                        'Authorization': `Bearer ${localStorage.getItem('token')}`
                    },
                    body: formData,
                });

                if (response.ok) {
                    const data = await response.json();

                    if (data.type == "success") {
                        Swal.fire({
                            icon: 'success',
                            title: 'Başarılı',
                            text: 'Resim güncellendi!',
                        });
                        setCurrentTimestamp(new Date().getTime());
                    } else {
                        Swal.fire({
                            icon: 'error',
                            title: 'Hata',
                            text: data.error,
                        });
                    }
                } else {
                    Swal.fire({
                        icon: 'error',
                        title: 'Hata',
                        text: 'Bir sorun meydana geldi!',
                    });
                }
            } catch (error) {
                Swal.fire({
                    icon: 'error',
                    title: 'Hata',
                    text: 'Bir sorun meydana geldi!',
                });
            }
            setImageLoading(false);
        }
    };

    const handleDelete = async () => {
        const shouldDelete = await Swal.fire({
            title: 'Dikkat!',
            text: 'Bu kaydı silmek istediğine gerçekten emin misin?',
            icon: 'warning',
            showCancelButton: true,
            confirmButtonText: 'Evet, sil!',
            cancelButtonText: 'Vazgeç',
        });

        if (shouldDelete.isConfirmed) {
            try {
                const response = await fetch(`/api/hollysnap/delete`, {
                    method: 'DELETE',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({ id: id }),
                });

                if (response.ok) {
                    router.push('/hollysnap');
                    Swal.fire({
                        icon: 'success',
                        title: 'Başarılı',
                        text: 'Kayıt silindi!',
                    });
                } else {
                    Swal.fire({
                        icon: 'error',
                        title: 'Hata',
                        text: 'Bir sorun meydana geldi!',
                    });
                }
            } catch (error) {
                console.error('An error occurred:', error);
            }
        }
    };

    const handleUpdate = async (event) => {
        event.preventDefault();
        const updatedData = {
            id: id,
            hollyPoints: item.hollyPoints,
            activityId: item.activityId,
            activityType: item.activityType
        }
        try {
            const response = await fetch(`/api/hollysnap/update`, {
                method: 'PUT',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify(updatedData),
            });

            if (response.ok) {
                await loadList();
                Swal.fire({
                    icon: 'success',
                    title: 'Başarılı',
                    text: 'Kayıt güncellendi!',
                });
            } else {
                Swal.fire({
                    icon: 'error',
                    title: 'Hata',
                    text: 'Bir sorun meydana geldi!',
                });
            }
        } catch (error) {
            console.error('An error occurred:', error);
        }
    }

    if (loading) {
        return "Loading";
    }

    return (
        <>
            <Head>
                <title>HollySnap - Düzenle</title>
            </Head>
            <PageTitleWrapper>
                <Grid container alignItems="center">
                    <Grid item>
                        <Typography variant="h3" component="h3" gutterBottom>
                            HollySnap Düzenle
                        </Typography>
                    </Grid>
                </Grid>
            </PageTitleWrapper>
            <Container maxWidth="lg">
                <Grid
                    container
                    direction="row"
                    justifyContent="center"
                    alignItems="stretch"
                    spacing={4}
                >
                    <Grid item xs={12}>
                        <Card
                            isBlurred
                            className="border-none bg-white dark:bg-default-100/50"
                            shadow="sm"
                        >
                            <form onSubmit={handleUpdate}>
                                <CardHeader className="flex justify-between gap-3">
                                    <div>
                                        <Link href="/hollysnap">
                                            <Button type="button" color="default" startContent={<ArrowBackIcon />}>
                                                Snap Liste
                                            </Button>
                                        </Link>
                                    </div>
                                    <div>HollySnap Bilgileri</div>
                                    <div className="flex-end">
                                        <Button type="button" onClick={handleDelete} color="danger" className="mr-2">
                                            Sil
                                        </Button>
                                        <Button type="submit" color="primary">
                                            Güncelle
                                        </Button>
                                    </div>
                                </CardHeader>

                                <Divider />

                                <CardBody>
                                    <div className="flex">
                                        <div className="relative w-1/2" style={{
                                            minWidth: 400
                                        }}>
                                            <Button color="primary" onClick={handleNewImage} endContent={<PlusIcon width={undefined} height={undefined} />}>
                                                Foroğraf Ekle
                                            </Button>
                                            <Accordion defaultExpandedKeys={["0"]}>
                                                {
                                                    item.images.map((image: any, index: number) => {
                                                        return (
                                                            <AccordionItem
                                                                key={index}
                                                                aria-label={`Resim ${index + 1}`}
                                                                title={`Resim ${index + 1}`}
                                                                startContent={
                                                                    (
                                                                        <div className="w-full mb-2 flex flex-row">
                                                                            <Button color="danger" onClick={() => { handleDeleteImage(index) }} className="min-w-unit-10 mr-2">
                                                                                <DeleteForeverIcon />
                                                                            </Button>
                                                                            <Image
                                                                                radius="none"
                                                                                loading="eager"
                                                                                className="h-unit-10"
                                                                                src={`https://api.hollystone.com.tr/resources/images/${image}?t=${currentTimestamp}`}
                                                                            />
                                                                        </div>
                                                                    )
                                                                }>
                                                                <label className="block cursor-pointer">
                                                                    <div className="relative">
                                                                        <Image
                                                                            width={400}
                                                                            height={400}
                                                                            radius="none"
                                                                            loading="eager"
                                                                            style={{
                                                                                height: "100%"
                                                                            }}
                                                                            src={`https://api.hollystone.com.tr/resources/images/${image}?t=${currentTimestamp}`}
                                                                            fallbackSrc="https://via.placeholder.com/400x400.png?text=Resim+Bulunamad%C4%B1"
                                                                        />
                                                                        {imageLoading ? (
                                                                            <CircularProgress
                                                                                size={48}
                                                                                style={{
                                                                                    position: 'absolute',
                                                                                    top: '40%',
                                                                                    left: '40%',
                                                                                    transform: 'translate(-40%, -40%)',
                                                                                    zIndex: 999,
                                                                                }}
                                                                            />
                                                                        ) : null}
                                                                    </div>
                                                                    <Input
                                                                        type="file"
                                                                        label="Resim"
                                                                        name="image"
                                                                        className="hidden"
                                                                        accept="image/*"
                                                                        onChange={(e: any) => { handleImageChange(e, image) }}
                                                                    />
                                                                </label>
                                                            </AccordionItem>
                                                        )
                                                    })
                                                }
                                            </Accordion>
                                        </div>

                                        <div className="p-4 w-full">
                                            <Select
                                                label="Etkinlik Türü"
                                                placeholder="Bir seçim yapın..."
                                                defaultSelectedKeys={[item.activityType?.toString()]}
                                                onSelectionChange={(e: any) => {
                                                    setItem({ ...item, activityType: (/true/i).test(e.currentKey) });
                                                }}
                                                className="mb-2"
                                            >
                                                <SelectItem key="false" value="false">
                                                    Konser
                                                </SelectItem>
                                                <SelectItem key="true" value="true">
                                                    Günlük Etkinlik
                                                </SelectItem>
                                            </Select>
                                            <Select
                                                label={item.activityType === false ? 'Konser' : 'Günlük Etkinlik'}
                                                placeholder="Bir seçim yapın..."
                                                defaultSelectedKeys={[item.activityId?.toString()]}
                                                onSelectionChange={(e: any) => {
                                                    setItem({ ...item, activityId: parseInt(e.currentKey) });
                                                }}
                                                className="mb-2"
                                            >
                                                {dropdownItems.map((dItem, dIndex) => {
                                                    return (
                                                        <SelectItem key={dItem.id} value={dIndex}>
                                                            {dItem.name}
                                                        </SelectItem>
                                                    )
                                                })}
                                            </Select>
                                            <Input
                                                type="number"
                                                label="Holly Puan"
                                                placeholder="Holly Puan"
                                                name="hollyPoints"
                                                min={0}
                                                value={item.hollyPoints}
                                                className="mb-2"
                                                onChange={(e: any) => setItem({ ...item, hollyPoints: parseInt(e.target.value) })}
                                            />
                                        </div>
                                    </div>
                                </CardBody>
                            </form>
                        </Card>
                    </Grid>
                </Grid>
            </Container>
            <Footer />
        </>
    );
};

EditSnapPage.getLayout = (page: React.ReactNode) => <SidebarLayout>{page}</SidebarLayout>;

export const getServerSideProps = authMiddleware(async (_context: any) => {
    return {
        props: {},
    };
});

export default EditSnapPage;
