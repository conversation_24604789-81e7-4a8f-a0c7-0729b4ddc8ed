import React, { useState, useEffect } from 'react';
import Head from 'next/head';
import { useRouter } from 'next/router';
import { authMiddleware } from 'middleware';
import Swal from 'sweetalert2';
import { Container, Grid, Typography } from '@mui/material';
import {
    Card,
    CardBody,
    CardHeader,
    Button,
    Divider,
    Textarea,
} from '@nextui-org/react';

import SidebarLayout from '@/layouts/SidebarLayout';
import PageTitleWrapper from '@/components/PageTitleWrapper';
import Footer from '@/components/Footer';
import ArrowBackIcon from '@mui/icons-material/ArrowBack';
import Link from 'next/link';

const AnswerFranchisePage = () => {
    const router = useRouter();
    const [item, setItem]: any = useState({});
    const { id } = router.query;
    const [loading, setLoading] = useState(true);
    const [message, setMessage] = useState('')

    const loadList = async () => {
        const url = `/api/franchise?id=${id}`;

        try {
            const res = await fetch(url);

            if (res.status === 200) {
                const json = await res.json();
                json.results[0].birthDate = new Date(json.results[0]?.birthDate);
                setItem(json.results[0]);
            } else {
                Swal.fire({
                    icon: 'error',
                    title: 'Hata',
                    text: 'Bir sorun meydana geldi!',
                });
            }
        } catch (error) {
            console.error(error);
        } finally {
            setLoading(false);
        }
    };

    const sendMail = async () => {
        const mailData = {
            from: '<EMAIL>',
            to: item.email,
            subject: 'Franchise Başvuru Cevap',
            text: message,
            html: `<div>${message}</div>`
        }
        try {
            const res = await fetch('/api/send-mail', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify(mailData)
            });
            if (res.status === 200) {
                Swal.fire({
                    icon: 'success',
                    title: 'Başarılı',
                    text: 'Mail gönderildi!',
                }).then(() => {
                    router.push(`/franchise/edit?id=${id}`)
                })
            } else {
                Swal.fire({
                    icon: 'error',
                    title: 'Hata',
                    text: 'Bir sorun meydana geldi!',
                });
            }
        } catch (error) {
            console.error(error);
        }
    }

    const sendSms = async () => {
        try {
            const res = await fetch('https://api.hollystone.com.tr/api/ansver-application', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'Authorization': `Bearer ${localStorage.getItem('token')}`
                },
                body: JSON.stringify({
                    phoneNumber: item.phoneNumber,
                    message
                })
            });

            if (res.status === 200) {
                const data = await res.json();
                if (data.type == "success") {
                    Swal.fire({
                        icon: 'success',
                        title: 'Başarılı',
                        text: 'SMS gönderildi!',
                    }).then(() => {
                        router.push(`/franchise/edit?id=${id}`)
                    })
                } else {
                    Swal.fire({
                        icon: 'error',
                        title: 'Hata',
                        text: data.error,
                    });
                }
            } else {
                Swal.fire({
                    icon: 'error',
                    title: 'Hata',
                    text: 'Bir sorun meydana geldi!',
                });
            }
        } catch (error) {
            console.error(error);
        }
    }

    useEffect(() => {
        loadList();
    }, []);

    if (loading) {
        return 'Loading';
    }

    return (
        <>
            <Head>
                <title>Franchise - Cevap</title>
            </Head>
            <PageTitleWrapper>
                <Grid container alignItems="center">
                    <Grid item>
                        <Typography variant="h3" component="h3" gutterBottom>
                            Franchise Cevap
                        </Typography>
                    </Grid>
                </Grid>
            </PageTitleWrapper>
            <Container maxWidth="lg">
                <Grid
                    container
                    direction="row"
                    justifyContent="center"
                    alignItems="stretch"
                    spacing={4}
                >
                    <Grid item xs={12}>
                        <Card
                            isBlurred
                            className="border-none bg-white dark:bg-default-100/50 max-w-[610px]"
                            shadow="sm"
                        >
                            <CardHeader className="flex justify-between gap-3">
                                <div>
                                    <Link href={`/franchise/edit?id=${id}`}>
                                        <Button type="button" color="default" startContent={<ArrowBackIcon />}>
                                            Başvuru Detay
                                        </Button>
                                    </Link>
                                </div>
                                <div>Franchise Cevap</div>
                                <div className="flex-end">
                                    <Button color="primary" className="mr-2" onClick={sendSms}>SMS Gönder</Button>
                                    <Button color="primary" onClick={sendMail}>Mail Gönder</Button>
                                </div>
                            </CardHeader>

                            <Divider />

                            <CardBody>
                                <div className="flex">
                                    <div className="p-4 w-full">
                                        <Textarea
                                            label="Cevap"
                                            placeholder="Cevap"
                                            name="message"
                                            value={message}
                                            onChange={(e) => {
                                                setMessage(e.target.value)
                                            }}
                                            className="mb-2"
                                        ></Textarea>
                                    </div>
                                </div>
                            </CardBody>
                        </Card>
                    </Grid>
                </Grid>
            </Container>
            <Footer />
        </>
    );
};

AnswerFranchisePage.getLayout = (page: React.ReactNode) => (
    <SidebarLayout>{page}</SidebarLayout>
);

export const getServerSideProps = authMiddleware(async (_context: any) => {
    return {
        props: {},
    };
});

export default AnswerFranchisePage;
