import React from 'react';
import { But<PERSON>, Input, Modal, Modal<PERSON>ontent, ModalHeader, ModalBody, ModalFooter } from '@nextui-org/react';

const NextUIModal = (props) => {
    const { newTitle, setNewTitle, handleNew, isOpen, onClose, onOpenChange } = props;

    return (
        <Modal isOpen={isOpen} onOpenChange={onOpenChange} onClose={onClose} backdrop="opaque">
            <ModalContent>
                <ModalHeader className="flex flex-col gap-1">Yeni</ModalHeader>
                <ModalBody>
                    <Input
                        type="text"
                        label=""
                        placeholder=""
                        value={newTitle}
                        className="mb-2"
                        onChange={(e: any) => { setNewTitle(e.target.value) }}
                    />
                </ModalBody>
                <ModalFooter>
                    <Button color="danger" variant="light" onPress={onClose}>
                        Vazgeç
                    </Button>
                    <Button color="primary" onPress={() => { handleNew(); onClose(); }}>
                        Ekle
                    </Button>
                </ModalFooter>
            </ModalContent>
        </Modal>
    )
}

export default NextUIModal;
