import React, { useState, useEffect } from 'react';
import Head from 'next/head';
import { useRouter } from 'next/router';
import { authMiddleware } from 'middleware';
import Swal from 'sweetalert2';
import { Container, Grid, Typography } from '@mui/material';
import {
    Card,
    CardBody,
    CardHeader,
    Button,
    Input,
    Divider,
} from '@nextui-org/react';

import SidebarLayout from '@/layouts/SidebarLayout';
import PageTitleWrapper from '@/components/PageTitleWrapper';
import Footer from '@/components/Footer';
import ArrowBackIcon from '@mui/icons-material/ArrowBack';
import Link from 'next/link';

const EditJobApplicationPage = () => {
    const router = useRouter();
    const [item, setItem]: any = useState({});
    const { id } = router.query;
    const [loading, setLoading] = useState(true);

    const loadList = async () => {
        const url = `/api/ticket-refund-applications?id=${id}`;

        try {
            const res = await fetch(url);

            if (res.status === 200) {
                const json = await res.json();
                json.results[0].birthDate = new Date(json.results[0]?.birthDate);
                setItem(json.results[0]);
            } else {
                Swal.fire({
                    icon: 'error',
                    title: 'Hata',
                    text: 'Bir sorun meydana geldi!',
                });
            }
        } catch (error) {
            console.error(error);
        } finally {
            setLoading(false);
        }
    };

    const fetchList = async () => {
        await loadList();
    };

    useEffect(() => {
        fetchList();
    }, []);

    const handleDelete = async () => {
        const shouldDelete = await Swal.fire({
            title: 'Dikkat!',
            text: 'Bu kaydı silmek istediğine gerçekten emin misin?',
            icon: 'warning',
            showCancelButton: true,
            confirmButtonText: 'Evet, sil!',
            cancelButtonText: 'Vazgeç',
        });

        if (shouldDelete.isConfirmed) {
            try {
                const response = await fetch(`/api/ticket-refund-applications/delete`, {
                    method: 'DELETE',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({ id: id }),
                });

                if (response.ok) {
                    router.push('/ticket-refund-applications');
                    Swal.fire({
                        icon: 'success',
                        title: 'Başarılı',
                        text: 'Kayıt silindi!',
                    });
                } else {
                    Swal.fire({
                        icon: 'error',
                        title: 'Hata',
                        text: 'Bir sorun meydana geldi!',
                    });
                }
            } catch (error) {
                console.error('An error occurred:', error);
            }
        }
    };

    const handleAnswer = async () => {
        try {
            const response = await fetch(`/api/ticket-refund-applications/approve`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({ id: id }),
            });

            if (response.ok) {
                router.push('/ticket-refund-applications');
                Swal.fire({
                    icon: 'success',
                    title: 'Başarılı',
                    text: 'İade onaylandı!',
                });
            } else {
                Swal.fire({
                    icon: 'error',
                    title: 'Hata',
                    text: 'Bir sorun meydana geldi!',
                });
            }
        } catch (error) {
            console.error('An error occurred:', error);
        }
    }

    if (loading) {
        return 'Loading';
    }

    return (
        <>
            <Head>
                <title>İade Başvuru - Detay</title>
            </Head>
            <PageTitleWrapper>
                <Grid container alignItems="center">
                    <Grid item>
                        <Typography variant="h3" component="h3" gutterBottom>
                            İade Başvuru Detay
                        </Typography>
                    </Grid>
                </Grid>
            </PageTitleWrapper>
            <Container maxWidth="lg">
                <Grid
                    container
                    direction="row"
                    justifyContent="center"
                    alignItems="stretch"
                    spacing={4}
                >
                    <Grid item xs={12}>
                        <Card
                            isBlurred
                            className="border-none bg-white dark:bg-default-100/50 max-w-[610px]"
                            shadow="sm"
                        >
                            <CardHeader className="flex justify-between gap-3">
                                <div>
                                    <Link href="/ticket-refund-applications">
                                        <Button type="button" color="default" startContent={<ArrowBackIcon />}>
                                            Başvurular
                                        </Button>
                                    </Link>
                                </div>
                                <div>İade Başvuru Bilgileri</div>
                                <div className="flex-end">
                                    <Button
                                        type="button"
                                        onClick={handleDelete}
                                        color="danger"
                                        className="mr-2"
                                    >
                                        Sil
                                    </Button>
                                    <Button
                                        type="button"
                                        onClick={() => handleAnswer()}
                                        color="primary"
                                    >
                                        Onayla
                                    </Button>
                                </div>
                            </CardHeader>

                            <Divider />

                            <CardBody>
                                <div className="flex">
                                    <div className="p-4 w-full">
                                        <div className="w-full mb-2 flex flex-row">
                                            <Input
                                                isReadOnly
                                                type="text"
                                                label="Ad"
                                                placeholder="Ad"
                                                name="name"
                                                value={item.concertName}
                                                className="mr-2"
                                            />
                                            <Input
                                                isReadOnly
                                                type="text"
                                                label="Ad"
                                                placeholder="Ad"
                                                name="firstName"
                                                value={item.firstName}
                                                className="mr-2"
                                            />
                                            <Input
                                                isReadOnly
                                                type="text"
                                                label="Soyad"
                                                placeholder="Soyad"
                                                name="lastName"
                                                value={item.lastName}
                                            />
                                        </div>
                                        <div className="w-full mb-2 flex flex-row">
                                            <Input
                                                isReadOnly
                                                type="text"
                                                label="Telefon Numarası"
                                                placeholder="Telefon Numarası"
                                                name="phoneNumber"
                                                value={item.phoneNumber}
                                                className="mr-2"
                                            />
                                            <Input
                                                isReadOnly
                                                type="text"
                                                label="E-mail"
                                                placeholder="E-mail"
                                                name="email"
                                                value={item.email}
                                                className="mr-2"
                                            />
                                            <Input
                                                isReadOnly
                                                type="date"
                                                label="Doğum Tarihi"
                                                placeholder="Doğum Tarihi"
                                                name="birthDate"
                                                value={item.birthDate?.toISOString().substr(0, 10)}
                                            />
                                        </div>
                                    </div>
                                </div>
                            </CardBody>
                        </Card>
                    </Grid>
                </Grid>
            </Container>
            <Footer />
        </>
    );
};

EditJobApplicationPage.getLayout = (page: React.ReactNode) => (
    <SidebarLayout>{page}</SidebarLayout>
);

export const getServerSideProps = authMiddleware(async (_context: any) => {
    return {
        props: {},
    };
});

export default EditJobApplicationPage;
