import { PrismaClient } from '@prisma/client';

const prisma = new PrismaClient();

export default async function handler(req, res) {
    if (req.method !== 'GET') {
        return res.status(405).json({ message: 'Method not allowed' });
    }

    const { id, page = 1, pageSize = 10, filter = "" } = req.query;

    let where = {};

    if (id) {
        where = {
            id: parseInt(id),
        };
        
        try {
            await prisma.refundApplication.update({
                where: {
                    id: parseInt(id),
                },
                data: {
                    isRead: true,
                    isDeleted: false
                },
            });
        } catch (error) {
            console.error(error);
            res.status(500).json({ message: 'Internal server error', error });
        }
    } else {
        where = {
            AND: [
                {
                    OR: [
                        { firstName: { contains: filter } },
                        { lastName: { contains: filter } },
                        { phoneNumber: { contains: filter } },
                        { email: { contains: filter } },
                    ],
                },
                { isDeleted: false }
            ],
        };
    }

    try {
        const jobApplications = await prisma.refundApplication.findMany({
            where,
            orderBy: {
                createdAt: 'desc'
            },
            skip: (parseInt(page) - 1) * parseInt(pageSize),
            take: parseInt(pageSize),
            include: {
                concert: true,
            },
        });

        const totalCount = await prisma.refundApplication.count({ where });

        const totalCountUnRead = await prisma.refundApplication.count({ where: { isRead: false, isDeleted: false } });

        const formattedJobApplications = jobApplications.map(jobApplication => ({
            ...jobApplication,
            concertName: jobApplication.concert.name,
        }));
        // Close the Prisma client connection
        await prisma.$disconnect();

        res.status(200).json({ results: formattedJobApplications, totalCount, totalCountUnRead });
    } catch (error) {
        console.error(error);
        // Close the Prisma client connection
        await prisma.$disconnect();
        res.status(500).json({ message: 'Internal server error' });
    }
}
