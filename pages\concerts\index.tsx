import React from 'react';
import Head from 'next/head';
import { authMiddleware } from '../../middleware';
import SidebarLayout from '@/layouts/SidebarLayout';
import PageTitleWrapper from '@/components/PageTitleWrapper';
import { Container, Grid, Typography } from '@mui/material';
import Footer from '@/components/Footer';
import ConcertsTable from '../components/concerts-table'

const ConcertsPage = () => {
    return (
        <>
            <Head>
                <title>Konserler</title>
            </Head>
            <PageTitleWrapper>
                <Grid container alignItems="center">
                    <Grid item>
                        <Typography variant="h3" component="h3" gutterBottom>
                            Konserler
                        </Typography>
                    </Grid>
                </Grid>
            </PageTitleWrapper>
            <Container maxWidth="lg">
                <Grid
                    container
                    direction="row"
                    justifyContent="center"
                    alignItems="stretch"
                    spacing={4}
                >
                    <Grid item xs={12}>
                        <ConcertsTable></ConcertsTable>
                    </Grid>
                </Grid>
            </Container>
            <Footer />
        </>
    );
}

ConcertsPage.getLayout = (page: any) => <SidebarLayout>{page}</SidebarLayout>;

export const getServerSideProps = authMiddleware(async (_context: any) => {
    // Your page-specific logic...
    return {
        props: {}, // You can add any props you need for the page
    };
});

export default ConcertsPage;
