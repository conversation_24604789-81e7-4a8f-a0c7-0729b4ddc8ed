import { PrismaClient } from '@prisma/client';

const prisma = new PrismaClient();

export default async function handler(_req, res) {
    if (_req.method !== 'GET') {
        return res.status(405).json({ message: 'Method not allowed' });
    }

    try {
        // HollyPointsValue değerini Setting modelinden al
        const setting = await prisma.setting.findFirst({
            select: {
                hollyPointsValue: true
            }
        });

        // HollyPointsValue değeri bulunamadıysa varsayılan değeri kullan
        const hollyPointsValue = setting ? parseFloat(setting.hollyPointsValue) : 0;

        // Type'i expense olan ve kasaonay null olan bütün işlemleri getir
        const transactions = await prisma.transaction.findMany({
            where: {
                kasaonay: 0
            },
            include: {
                user: {
                    select: {
                        firstName: true,
                        lastName: true,
                    }
                }
            },
            orderBy: {
                createdAt: 'desc' // En yeni tarihten en eskiye doğru sıralama
            },
        });

        // İşlem sonuçlarını hollyPointsValue ile çarp
        const transactionsWithHollyPoints = transactions.map(transaction => {
            const usedHollyPoints = transaction.usedHollyPoints || 0;
            const calculatedHollyPoints = hollyPointsValue * usedHollyPoints;
            return { ...transaction, calculatedHollyPoints };
        });

        await prisma.$disconnect();

        res.status(200).json(transactionsWithHollyPoints);
    } catch (error) {
        await prisma.$disconnect();
        res.status(500).json({ message: 'Internal server error' });
    }
}
