import { PrismaClient } from '@prisma/client';

const prisma = new PrismaClient();

export default async function handler(req, res) {
    if (req.method === 'PUT') {
        const { id, details, name, description, rank, price, discountedPrice, options, hollyPoints, status } = req.body;

        try {
            const updatedProduct = await prisma.shopProduct.update({
                where: {
                    id: parseInt(id),
                },
                data: {
                    name: name,
                    hollyPoints: hollyPoints,
                    details: details,
                    description: description,
                    rank: rank,
                    price: price,
                    discountedPrice: discountedPrice,
                    options: options,
                    status: status,
                },
            });

            res.status(200).json({ message: '<PERSON>rün güncellendi!', updatedItem: updatedProduct });
        } catch (error) {
            console.error(error);
            res.status(500).json({ message: 'Internal server error', error });
        }
    } else {
        if (req.method === 'POST') {
            const { id, images } = req.body;
    
            try {
                const updatedProduct = await prisma.shopProduct.update({
                    where: {
                        id: parseInt(id),
                    },
                    data: {
                        images: images,
                    },
                });
    
                res.status(200).json({ message: '<PERSON><PERSON><PERSON><PERSON> güncellendi!', updatedItem: updatedProduct });
            } catch (error) {
                console.error(error);
                res.status(500).json({ message: 'Internal server error', error });
            }
        } else {
            res.status(405).json({ message: 'Method not allowed' });
        }
    }
}
