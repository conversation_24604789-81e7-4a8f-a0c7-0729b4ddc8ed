import { PrismaClient } from '@prisma/client';

const prisma = new PrismaClient();

export default async function handler(req, res) {
    if (req.method === 'PUT') {
        const { hollyPointsValue, commission } = req.body;

        try {
            const updatedSettings = await prisma.setting.update({
                where: {
                    id: 1,
                },
                data: {
                    hollyPointsValue,
                    commission: parseInt(commission)
                },
            });

            res.status(200).json({ message: 'Ayarlar güncellendi!', updatedItem: updatedSettings });
        } catch (error) {
            console.error(error);
            res.status(500).json({ message: 'Internal server error', error });
        }
    } else {
        res.status(405).json({ message: 'Method not allowed' });
    }
}
