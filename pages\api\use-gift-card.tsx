import { PrismaClient } from '@prisma/client';

const prisma = new PrismaClient();

export default async function handler(req, res) {
    if (req.method !== 'GET') {
        return res.status(405).json({ message: 'Method not allowed' });
    }

    const { code, amount, adminId } = req.query;

    try {
        const giftCard: any = await prisma.giftCard.findFirst({
            where: {
                code,
                status: true
            }
        });

        // Close the Prisma client connection
        await prisma.$disconnect();

        if (!giftCard) {
            return res.status(404).json({ message: 'Hediye kartı bulunamadı' });
        }

        if (giftCard.value < amount) {
            return res.status(400).json({ message: '<PERSON><PERSON><PERSON> bakiye' });
        }

        await prisma.giftCard.update({
            where: {
                id: giftCard.id
            },
            data: {
                value: giftCard.value - amount
            }
        })

        await prisma.cashierAction.create({
            data: {
                userId: giftCard.userId,
                adminId: parseInt(adminId),
                type: "giftCard",
                amount: parseFloat(amount),
                giftCardCode: code
            }
        })

        res.status(200).json({ result: true, message: `${amount} ₺ kullanıldı` });
    } catch (error) {
        console.error(error);
        // Close the Prisma client connection
        await prisma.$disconnect();
        res.status(500).json({ message: 'Internal server error' });
    }
}
