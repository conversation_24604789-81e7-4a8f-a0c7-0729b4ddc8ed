import React, { useState, useEffect } from 'react';
import {
  Card,
  Box,
  Grid,
  Typography,
  useTheme,
  Divider,
  CardHeader,
} from '@mui/material';
import { Image } from '@nextui-org/react';
import { Spinner } from '@nextui-org/react';

function HollyPointSummary() {
  const theme = useTheme();

  const [total, setTotal] = useState(0);
  const [totalEarned, setTotalEarned] = useState(0);
  const [totalSpent, setTotalSpent] = useState(0);
  const [hollyPointsValue, setHollyPointsValue] = useState(0);
  const [topValueUserName, setTopValueUserName] = useState("");
  const [topValueUserImage, setTopValueUserImage] = useState("");
  const [loading, setLoading] = useState(true);

  const loadList = async () => {
    const url = `/api/holly-point-summary`;
    setLoading(true);

    try {
      const res = await fetch(url);

      if (res.status === 200) {
        const json = await res.json();
        setTotal(json.total);
        setTotalEarned(json.totalEarned);
        setTotalSpent(json.totalSpent);
        setHollyPointsValue(json.value);
        setTopValueUserName(json.topValueUserName);
        setTopValueUserImage(json.topValueUserImage);
      } else {
        console.log("API call failed");
      }
    } catch (error) {
      console.error(error);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    loadList();
  }, []);

  return (
    <Card>
      <CardHeader title="Holly Point" />
      {
        loading ? (
          <div className="flex">
            <div className="p-4 w-full flex flex-col justify-center items-center">
              <Spinner />
            </div>
          </div>
        ) : (
          <Grid spacing={0} container>
            <Grid item xs={12} md={6}>
              <Box p={4}>
                <Typography
                  sx={{
                    pb: 3
                  }}
                  variant="h4"
                >
                </Typography>
                <Box>
                  <div className="w-full mb-2 flex flex-row">
                    <div className="mr-2">
                      <Typography
                        sx={{
                          pb: 3
                        }}
                        variant="h4"
                      >
                        Kazanılan
                      </Typography>
                      <Typography variant="h1" gutterBottom sx={{
                        color: "green"
                      }}>
                        {totalEarned ?? 0}
                      </Typography>
                    </div>
                    <div className="ml-2 mr-2">
                      <Typography
                        sx={{
                          pb: 3
                        }}
                        variant="h4"
                      >
                        Harcanan
                      </Typography>
                      <Typography variant="h1" gutterBottom sx={{
                        color: "red"
                      }}>
                        {totalSpent ?? 0}
                      </Typography>
                    </div>
                    <div className="ml-2">
                      <Typography
                        sx={{
                          pb: 3
                        }}
                        variant="h4"
                      >
                        Genel Toplam
                      </Typography>
                      <Typography variant="h1" gutterBottom>
                        {total ?? 0}
                      </Typography>
                    </div>
                  </div>
                  <Box
                    display="flex"
                    sx={{
                      py: 4,
                      mr: 2
                    }}
                    alignItems="center"
                  >
                    <Image
                      width={theme.spacing(8)}
                      height={theme.spacing(8)}
                      radius="md"
                      loading="eager"
                      src={`https://api.hollystone.com.tr/resources/images/holly_points.png`}
                    />
                    <Box sx={{
                      ml: 2
                    }}>
                      <Typography variant="h4">{hollyPointsValue} ₺</Typography>
                      <Typography variant="subtitle2" noWrap>
                        1 Holly Puan
                      </Typography>
                    </Box>
                  </Box>
                </Box>
              </Box>
            </Grid>
            <Grid
              sx={{
                position: 'relative'
              }}
              display="flex"
              alignItems="center"
              item
              xs={12}
              md={6}
            >
              <Box
                component="span"
                sx={{
                  display: { xs: 'none', md: 'inline-block' }
                }}
              >
                <Divider absolute orientation="vertical" />
              </Box>
              <Box py={4} pr={4} flex={1}
                sx={{
                  ml: 18
                }}>
                <Grid container spacing={0}>
                  <Grid xs={12} sm={7} item display="flex" alignItems="center">
                    <Box
                      display="flex"
                      sx={{
                        py: 4,
                        mr: 2
                      }}
                      alignItems="center"
                    >
                      <Image
                        width={theme.spacing(8)}
                        height={theme.spacing(8)}
                        radius="md"
                        loading="eager"
                        src={`https://api.hollystone.com.tr/resources/images/${topValueUserImage}`}
                      />
                      <Box sx={{
                        ml: 2
                      }}>
                        <Typography variant="h4">{topValueUserName}</Typography>
                        <Typography variant="subtitle2" noWrap>
                          En çok holly puan sahibi
                        </Typography>
                      </Box>
                    </Box>
                  </Grid>
                </Grid>
              </Box>
            </Grid>
          </Grid>
        )
      }
    </Card>
  );
}

export default HollyPointSummary;
