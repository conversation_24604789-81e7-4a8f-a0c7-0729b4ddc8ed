import { PrismaClient } from '@prisma/client';

const prisma = new PrismaClient();

export default async function handler(req, res) {
    if (req.method !== 'GET') {
        return res.status(405).json({ message: 'Method not allowed' });
    }

    try {
        const total = await prisma.$queryRaw`SELECT SUM(hollyPoints) as total FROM users;`;
        const totalEarned = await prisma.$queryRaw`SELECT SUM(hollyPoints) as total FROM holly_points_earning_history;`;
        const totalSpent = await prisma.$queryRaw`SELECT SUM(hollyPoints) as total FROM holly_points_spend_history;`;
        const value = await prisma.$queryRaw`SELECT hollyPointsValue as value FROM settings WHERE id = 1;`;
        const topValueUser = await prisma.$queryRaw`SELECT firstName, lastName, image FROM users ORDER BY hollyPoints DESC LIMIT 1;`;

        // Close the Prisma client connection
        await prisma.$disconnect();

        res.status(200).json({ total: parseInt(total[0].total), totalEarned: parseInt(totalEarned[0].total), totalSpent: parseInt(totalSpent[0].total), value: parseInt(value[0].value), topValueUserName: `${topValueUser[0].firstName} ${topValueUser[0].lastName}`, topValueUserImage: topValueUser[0].image });
    } catch (error) {
        console.error(error);
        // Close the Prisma client connection
        await prisma.$disconnect();
        res.status(500).json({ message: 'Internal server error' });
    }
}
