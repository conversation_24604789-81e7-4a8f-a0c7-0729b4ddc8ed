import { PrismaClient } from '@prisma/client';

const prisma = new PrismaClient();

export default async function handler(req, res) {
  if (req.method !== 'GET') {
    return res.status(405).json({ message: 'Method not allowed' });
  }

  const { id, page = 1, pageSize = 10, filter = "" } = req.query;

  let where = {};

  if (id) {
    where = {
      id: parseInt(id),
    };
  } else {
    where = {
      isDeleted: false,
    };
  }

  try {
    const snaps = await prisma.hollySnap.findMany({
      where,
      orderBy: {
          createdAt: 'desc'
      },
      skip: (parseInt(page) - 1) * parseInt(pageSize),
      take: parseInt(pageSize),
      select: {
        id: true,
        activityId: true,
        activityType: true,
        images: true,
        hollyPoints: true,
        isDeleted: true,
        createdAt: true,
        updatedAt: true,
      },
    });

    const totalCount = await prisma.hollySnap.count({ where });

    const concertIds = snaps
      .filter((snap) => snap.activityType === false)
      .map((snap) => snap.activityId);

    const dailyActivityIds = snaps
      .filter((snap) => snap.activityType === true)
      .map((snap) => snap.activityId);

    const concerts = await prisma.concert.findMany({
      where: {
        id: {
          in: concertIds,
        },
      },
      select: {
        id: true,
        name: true,
      },
    });

    const dailyActivities = await prisma.dailyActivity.findMany({
      where: {
        id: {
          in: dailyActivityIds,
        },
      },
      select: {
        id: true,
        name: true,
      },
    });

    let results = snaps.map((snap) => {
      if (snap.activityType === false) {
        const concert = concerts.find((c) => c.id === snap.activityId);
        return {
          ...snap,
          activityName: concert?.name,
        };
      } else if (snap.activityType === true) {
        const dailyActivity = dailyActivities.find(
          (da) => da.id === snap.activityId
        );
        return {
          ...snap,
          activityName: dailyActivity?.name,
        };
      }
      return snap;
    });

    if (filter) {
      results = results.filter((snap: any) =>
        snap.activityName.toLowerCase().includes(filter.toLowerCase())
      );
    }

    // Close the Prisma client connection
    await prisma.$disconnect();

    res.status(200).json({ results, totalCount });
  } catch (error) {
    console.error(error);
    // Close the Prisma client connection
    await prisma.$disconnect();
    res.status(500).json({ message: 'Internal server error' });
  }
}
