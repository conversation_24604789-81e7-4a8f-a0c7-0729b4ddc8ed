import React, { useState, useEffect } from 'react';
import Head from 'next/head';
import { authMiddleware } from '../../middleware';
import SidebarLayout from '@/layouts/SidebarLayout';
import PageTitleWrapper from '@/components/PageTitleWrapper';
import { Typography, Container, Grid, CircularProgress } from '@mui/material';
import { Image, Card, CardBody, CardHeader, Button, Input, Textarea, Divider } from '@nextui-org/react';
import { Select, SelectItem } from "@nextui-org/select";
import Footer from '@/components/Footer';
import Swal from 'sweetalert2';
import { useRouter } from 'next/router';
import ArrowBackIcon from '@mui/icons-material/ArrowBack';
import Link from 'next/link';

const NewAnnouncementPage = () => {
    const router = useRouter();
    const [item, setItem]: any = useState({});
    const [detailItem, setDetailItem]: any = useState([]);
    const [vendors, setVendors] = useState([]);
    const [selectedImage, setSelectedImage] = useState(null);
    const [imageLoading, setImageLoading] = useState(false);

    const getDetail = async (detail: number) => {
        let url = "";
        detail == 1 ? (url = `/api/concerts`) : detail == 2 ? (url = `/api/daily-activities`) : detail == 3 ? (url = `/api/shop-products`) : (url = `/api/hollysnap`);

        try {
            const res = await fetch(url);

            if (res.status === 200) {
                const json = await res.json();
                setDetailItem(json.results);
            } else {
                Swal.fire({
                    icon: 'error',
                    title: 'Hata',
                    text: 'Bir sorun meydana geldi!',
                });
            }
        } catch (error) {
            console.error(error);
        }
    };

    const loadVendors = async () => {
        const url = `/api/vendors`;

        try {
            const res = await fetch(url);

            if (res.status === 200) {
                const json = await res.json();
                setVendors(json.results);
            } else {
                Swal.fire({
                    icon: 'error',
                    title: 'Hata',
                    text: 'Bir sorun meydana geldi!',
                });
            }
        } catch (error) {
            console.error(error);
        }
    };

    useEffect(() => {
        loadVendors();
    }, []);

    useEffect(() => {
        getDetail(item.detail);
    }, [item.detail]);

    const handleImageChange = (event: React.ChangeEvent<HTMLInputElement>) => {
        const file = event.target.files?.[0];

        if (file) {
            if (file.type.startsWith('image/')) {
                setSelectedImage(URL.createObjectURL(file));
                setItem({ ...item, image: file });
            } else {
                Swal.fire({
                    icon: 'error',
                    title: 'Hata',
                    text: 'Seçilen format desteklenmiyor!',
                });
                return;
            }
        }
    };

    const handleInsert = async (event: React.FormEvent) => {
        event.preventDefault();
        if (!item.header && !item.content) {
            Swal.fire({
                icon: 'error',
                title: 'Hata',
                text: 'Lütfen alanları doldurun!',
            });
            return;
        }
        if (!item.image) {
            Swal.fire({
                icon: 'error',
                title: 'Hata',
                text: 'Lütfen bir resim seçin!',
            });
            return;
        }
        try {
            const response = await fetch(`/api/announcements/insert`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify(item),
            });

            if (response.ok) {
                const data = await response.json();
                const id = data.createdItem.id;

                setImageLoading(true);
                const formData = new FormData();
                formData.append('image', item.image);
                formData.append('fileName', `announcements/${id}`);

                try {
                    const response = await fetch(`https://api.hollystone.com.tr/api/functions/upload`, {
                        method: 'POST',
                        headers: {
                            'Authorization': `Bearer ${localStorage.getItem('token')}`
                        },
                        body: formData,
                    });

                    if (response.ok) {
                        const data = await response.json();

                        if (data.type == "success") {
                            Swal.fire({
                                icon: 'success',
                                title: 'Başarılı',
                                text: 'Kayıt başarılı!',
                            }).then(() => {
                                router.push('/announcements');
                            });
                        } else {
                            Swal.fire({
                                icon: 'error',
                                title: 'Hata',
                                text: data.error,
                            });
                        }
                    } else {
                        Swal.fire({
                            icon: 'error',
                            title: 'Hata',
                            text: 'Bir sorun meydana geldi!',
                        });
                    }
                } catch (error) {
                    Swal.fire({
                        icon: 'error',
                        title: 'Hata',
                        text: 'Bir sorun meydana geldi!',
                    });
                }
            } else {
                Swal.fire({
                    icon: 'error',
                    title: 'Hata',
                    text: 'Bir sorun meydana geldi!',
                });
            }
        } catch (error) {
            console.error('An error occurred:', error);
        }
        setImageLoading(false);
    };

    const renderContent = () => {
        return (
            <>
                <Head>
                    <title>Duyurular - Oluştur</title>
                </Head>
                <div className="container mx-auto px-4 py-8">
                <div className="flex items-center justify-between mb-6">
                    <button
                        onClick={() => router.push('/announcements')}
                        className="bg-gray-200 text-gray-700 px-4 py-2 rounded hover:bg-gray-300"
                    >
                        Geri Dön
                    </button>
                    <h1 className="text-2xl font-bold">Duyuru Oluştur</h1>
                    <button
                        onClick={handleInsert}
                        className="bg-green-500 text-white px-4 py-2 rounded hover:bg-green-600"
                    >
                        Oluştur
                    </button>
                </div>

                <form onSubmit={handleInsert} className="bg-white shadow-md rounded px-8 pt-6 pb-8 mb-4">
                    <div className="mb-4">
                        <label className="block text-gray-700 text-sm font-bold mb-2" htmlFor="header">
                            Başlık
                        </label>
                        <input
                            id="header"
                            type="text"
                            placeholder="Başlık girin"
                            className="shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline"
                            value={item.header || ''}
                            onChange={(e) => setItem({ ...item, header: e.target.value })}
                        />
                    </div>

                    <div className="mb-4">
                        <label className="block text-gray-700 text-sm font-bold mb-2" htmlFor="content">
                            İçerik
                        </label>
                        <textarea
                            id="content"
                            placeholder="İçerik girin"
                            className="shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline"
                            value={item.content || ''}
                            onChange={(e) => setItem({ ...item, content: e.target.value })}
                        ></textarea>
                    </div>

                    <div className="mb-4">
                        <label className="block text-gray-700 text-sm font-bold mb-2" htmlFor="image">
                            Resim
                        </label>
                        <input
                            id="image"
                            type="file"
                            className="shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline"
                            onChange={handleImageChange}
                        />
                        {selectedImage && (
                            <img src={selectedImage} alt="Selected" className="mt-4 max-w-full h-auto" />
                        )}
                    </div>

                    <div className="mb-4">
                        <label className="block text-gray-700 text-sm font-bold mb-2">
                            Konu
                        </label>
                        <select
                            className="shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline"
                            value={item.detail || ''}
                            onChange={(e) => setItem({ ...item, detail: parseInt(e.target.value) })}
                        >
                            <option value="">Bir seçim yapın...</option>
                            <option value="1">Konser</option>
                            <option value="2">Günlük Etkinlik</option>
                            <option value="3">Holly Shop</option>
                            <option value="4">Holly Snap</option>
                        </select>
                    </div>

                    <div className="mb-4">
                        <label className="block text-gray-700 text-sm font-bold mb-2">
                            Hedef
                        </label>
                        <select
                            className="shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline"
                            value={item.detailId || ''}
                            onChange={(e) => setItem({ ...item, detailId: parseInt(e.target.value) })}
                        >
                            <option value="">Bir seçim yapın...</option>
                            {detailItem.map((innerItem) => (
                                <option key={innerItem.id} value={innerItem.id}>
                                    {item.detail == 4 ? innerItem.activityName : innerItem.name}
                                </option>
                            ))}
                        </select>
                    </div>

                    <div className="mb-4">
                        <label className="block text-gray-700 text-sm font-bold mb-2">
                            Bayi
                        </label>
                        <select
                            className="shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline"
                            value={item.vendorId || ''}
                            onChange={(e) => setItem({ ...item, vendorId: parseInt(e.target.value) })}
                        >
                            <option value="">Bir seçim yapın...</option>
                            {vendors.map((vendor) => (
                                <option key={vendor.id} value={vendor.id}>
                                    {vendor.name}
                                </option>
                            ))}
                        </select>
                    </div>
                </form>
            </div>
                <Footer />
            </>
        );
    };

    return renderContent();
};

NewAnnouncementPage.getLayout = (page: React.ReactNode) => <SidebarLayout>{page}</SidebarLayout>;

export const getServerSideProps = authMiddleware(async (_context: any) => {
    return {
        props: {},
    };
});

export default NewAnnouncementPage;
