import React, { useState, useEffect } from 'react';
import Head from 'next/head';
import { useRouter } from 'next/router';
import { authMiddleware } from 'middleware';
import { getCookie } from 'cookies-next';
import Swal from 'sweetalert2';
import { Container, Grid, Typography } from '@mui/material';
import { Card, CardBody, CardHeader, Button, Input, Divider, Textarea, Accordion, AccordionItem, Image } from '@nextui-org/react';
import { Select, SelectItem } from "@nextui-org/select";
import SidebarLayout from '@/layouts/SidebarLayout';
import PageTitleWrapper from '@/components/PageTitleWrapper';
import Footer from '@/components/Footer';
import ArrowBackIcon from '@mui/icons-material/ArrowBack';
import Link from 'next/link';

const EditOrderPage = () => {
    const router = useRouter();
    const token = getCookie("token")
    const [item, setItem]: any = useState({});
    const { id } = router.query;
    const [loading, setLoading] = useState(true);

    const loadList = async () => {
        const url = `/api/shop-orders?id=${id}`;

        try {
            const res = await fetch(url);

            if (res.status === 200) {
                const json = await res.json();
                json.results[0].estimatedDeliveryDate = new Date(json.results[0]?.estimatedDeliveryDate);
                setItem(json.results[0]);
            } else {
                Swal.fire({
                    icon: 'error',
                    title: 'Hata',
                    text: 'Bir sorun meydana geldi!',
                });
            }
        } catch (error) {
            console.error(error);
        } finally {
            setLoading(false);
        }
    };

    useEffect(() => {
        loadList();
    }, []);

    const handleRefund = async () => {
        const shouldDelete = await Swal.fire({
            title: 'Dikkat!',
            text: 'Bu siparişi iade etmek istediğine gerçekten emin misin?',
            icon: 'warning',
            showCancelButton: true,
            confirmButtonText: 'Evet, eminim!',
            cancelButtonText: 'Vazgeç',
        });

        if (shouldDelete.isConfirmed) {
            setLoading(true);
            try {
                const response = await fetch('https://api.hollystone.com.tr/api/payment/refund-order', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'Authorization': `Bearer ${token}`
                    },
                    body: JSON.stringify({
                        orderId: id
                    })
                });

                if (response.status === 200) {
                    const json = await response.json();
                    if (json.type == "success") {
                        Swal.fire({
                            icon: 'success',
                            title: 'Başarılı',
                            text: 'Bilet başarılı bir şekilde iade edildi',
                        });
                        router.reload();
                    } else {
                        Swal.fire({
                            icon: 'error',
                            title: 'Hata',
                            text: json.error,
                        });
                    }
                    setLoading(false);
                } else {
                    Swal.fire({
                        icon: 'error',
                        title: 'Hata',
                        text: 'Bir sorun meydana geldi!',
                    });
                    setLoading(false);
                }
            } catch (error) {
                Swal.fire({
                    icon: 'error',
                    title: 'Hata',
                    text: 'Bir sorun meydana geldi!',
                });
                setLoading(false);
            }
        }
    };

    const handleUpdate = async (event) => {
        event.preventDefault();
        try {
            const response = await fetch(`/api/shop-orders/update`, {
                method: 'PUT',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify(item),
            });

            if (response.ok) {
                await loadList();
                Swal.fire({
                    icon: 'success',
                    title: 'Başarılı',
                    text: 'Kayıt güncellendi!',
                });
            } else {
                Swal.fire({
                    icon: 'error',
                    title: 'Hata',
                    text: 'Bir sorun meydana geldi!',
                });
            }
        } catch (error) {
            console.error('An error occurred:', error);
        }
    }

    if (loading) {
        return "Loading";
    }

    return (
        <>
            <Head>
                <title>Sipariş - Düzenle</title>
            </Head>
            <PageTitleWrapper>
                <Grid container alignItems="center">
                    <Grid item>
                        <Typography variant="h3" component="h3" gutterBottom>
                            Sipariş Düzenle
                        </Typography>
                    </Grid>
                </Grid>
            </PageTitleWrapper>
            <Container maxWidth="lg">
                <Grid
                    container
                    direction="row"
                    justifyContent="center"
                    alignItems="stretch"
                    spacing={4}
                    style={{
                        marginBottom: 20
                    }}
                >
                    <Grid item xs={12}>
                        <Card
                            isBlurred
                            className="border-none bg-white dark:bg-default-100/50 max-w-[610px]"
                            shadow="sm"
                        >
                            <form onSubmit={handleUpdate}>
                                <CardHeader className="flex justify-between gap-3">
                                    <div>
                                        <Link href="/shop-orders">
                                            <Button type="button" color="default" startContent={<ArrowBackIcon />}>
                                                Siparişler
                                            </Button>
                                        </Link>
                                    </div>
                                    <div>Sipariş Bilgileri</div>
                                    <div className="flex-end">
                                        <Button type="button" onClick={handleRefund} color="danger" className="mr-2">
                                            İade Et
                                        </Button>
                                        <Button type="submit" color="primary">
                                            Güncelle
                                        </Button>
                                    </div>
                                </CardHeader>

                                <Divider />

                                <CardBody>
                                    <div className="flex">
                                        <div className="p-4 w-full">
                                            <div className="w-full mb-2 flex flex-row">
                                                <Input
                                                    type="text"
                                                    label="Kargo Firması"
                                                    placeholder="Kargo Firması"
                                                    name="carrier"
                                                    value={item.carrier}
                                                    className="mr-2"
                                                    onChange={(e: any) => setItem({ ...item, carrier: e.target.value })}
                                                />
                                                <Input
                                                    type="text"
                                                    label="Kargo Takip No"
                                                    placeholder="Kargo Takip No"
                                                    name="deliveryTrackingNumber"
                                                    value={item.deliveryTrackingNumber}
                                                    className="mr-2"
                                                    onChange={(e: any) => setItem({ ...item, deliveryTrackingNumber: e.target.value })}
                                                />
                                                <Input
                                                    type="datetime-local"
                                                    label="Tahmini Teslim Tarihi"
                                                    placeholder="Tahmini Teslim Tarihi"
                                                    name="estimatedDeliveryDate"
                                                    value={item.estimatedDeliveryDate?.toISOString().slice(0, 16)}
                                                    onChange={(e: any) => setItem({ ...item, estimatedDeliveryDate: new Date(e.target.value + 'Z') })}
                                                />
                                            </div>
                                            <div className="w-full mb-2 flex flex-row">
                                                <Input
                                                    isReadOnly
                                                    type="text"
                                                    label="Toplam Tutar"
                                                    placeholder="Toplam Tutar"
                                                    name="price"
                                                    value={item.price}
                                                    className="mr-2"
                                                />
                                                <Input
                                                    isReadOnly
                                                    type="text"
                                                    label="Kullanılan Holly Puanlar"
                                                    placeholder="Kullanılan Holly Puanlar"
                                                    name="hollyPoints"
                                                    value={item.hollyPoints}
                                                    className="mr-2"
                                                />
                                                <Select
                                                    label="Durum"
                                                    placeholder="Bir seçim yapın..."
                                                    defaultSelectedKeys={[item.status?.toString()]}
                                                    onChange={(e: any) => setItem({ ...item, status: parseInt(e.target.value) })}
                                                >
                                                    <SelectItem key="1" value="1">
                                                        Hazırlanıyor
                                                    </SelectItem>
                                                    <SelectItem key="2" value="2">
                                                        Kargoda
                                                    </SelectItem>
                                                    <SelectItem key="3" value="3">
                                                        Teslim Edildi
                                                    </SelectItem>
                                                    <SelectItem key="4" value="4">
                                                        İptal Edildi
                                                    </SelectItem>
                                                </Select>
                                            </div>
                                        </div>
                                    </div>
                                </CardBody>
                            </form>
                        </Card>
                    </Grid>
                </Grid>
                <Grid
                    container
                    direction="row"
                    justifyContent="center"
                    alignItems="stretch"
                    spacing={4}
                    style={{
                        marginBottom: 20
                    }}
                >
                    <Grid item xs={12}>
                        <Card
                            isBlurred
                            className="border-none bg-white dark:bg-default-100/50 max-w-[610px]"
                            shadow="sm"
                        >
                            <CardHeader className="flex justify-between gap-3">
                                <div></div>
                                <div>Adres Bilgileri</div>
                                <div></div>
                            </CardHeader>

                            <Divider />

                            <CardBody>
                                <div className="flex">
                                    <div className="p-4 w-full">
                                        <Input
                                            isReadOnly
                                            type="text"
                                            label="Adres Başlık"
                                            placeholder="Adres Başlık"
                                            name="addressTitle"
                                            value={item.address.title}
                                            className="mb-2"
                                        />
                                        <div className="w-full mb-2 flex flex-row">
                                            <Input
                                                isReadOnly
                                                type="text"
                                                label="İl"
                                                placeholder="İl"
                                                name="addressCity"
                                                value={item.address.city.title}
                                                className="mr-2"
                                            />
                                            <Input
                                                isReadOnly
                                                type="text"
                                                label="İlçe"
                                                placeholder="İlçe"
                                                name="addressDistrict"
                                                value={item.address.district.title}
                                                className="mr-2"
                                            />
                                            <Input
                                                isReadOnly
                                                type="text"
                                                label="Mahalle"
                                                placeholder="Mahalle"
                                                name="addressNeighborhood"
                                                value={item.address.neighborhood.title}
                                                className="mr-2"
                                            />
                                            <Input
                                                isReadOnly
                                                type="text"
                                                label="Posta Kodu"
                                                placeholder="Posta Kodu"
                                                name="addressPostalCode"
                                                value={item.address.postalCode}
                                            />
                                        </div>
                                        <Textarea
                                            isReadOnly
                                            label="Açık Adres"
                                            placeholder="Açık Adres"
                                            name="addressFullAddress"
                                            value={item.address.fullAddress}
                                        ></Textarea>
                                    </div>
                                </div>
                            </CardBody>
                        </Card>
                    </Grid>
                </Grid>
                <Grid
                    container
                    direction="row"
                    justifyContent="center"
                    alignItems="stretch"
                    spacing={4}
                >
                    <Grid item xs={12}>
                        <Card
                            isBlurred
                            className="border-none bg-white dark:bg-default-100/50 max-w-[610px]"
                            shadow="sm"
                        >
                            <CardHeader className="flex justify-between gap-3">
                                <div></div>
                                <div>Ürünler</div>
                                <div></div>
                            </CardHeader>

                            <Divider />

                            <CardBody>
                                <div className="flex">
                                    <div className="p-4 w-full">
                                        <Accordion defaultExpandedKeys={["0"]} className="bg-gray">
                                            {
                                                item.OrderItem.map((oItem, index) => {
                                                    const images = oItem.product.images.split(',');
                                                    return (
                                                        <AccordionItem
                                                            key={index}
                                                            aria-label={oItem.product.name}
                                                            title={oItem.product.name}
                                                            startContent={
                                                                <Button color="primary">
                                                                    <Link href={'/shop-products/edit?id=' + oItem.id}>
                                                                        <a target="_blank" rel="noopener noreferrer">
                                                                            Ürün detay
                                                                        </a>
                                                                    </Link>
                                                                </Button>
                                                            }>
                                                            <div className="flex">
                                                                <div className="relative w-1/2" style={{
                                                                    minWidth: 400
                                                                }}>
                                                                    <Image
                                                                        width={400}
                                                                        height={400}
                                                                        radius="none"
                                                                        loading="eager"
                                                                        style={{
                                                                            height: "100%"
                                                                        }}
                                                                        src={`https://api.hollystone.com.tr/resources/images/${images[0]}`}
                                                                        fallbackSrc="https://via.placeholder.com/400x400.png?text=Resim+Bulunamad%C4%B1"
                                                                    />
                                                                </div>
                                                                <div className="p-4 w-full">
                                                                    <Input
                                                                        isReadOnly
                                                                        type="text"
                                                                        label="Seçili Beden"
                                                                        placeholder="Seçili Beden"
                                                                        name="selectedOption"
                                                                        value={oItem.selectedOption}
                                                                        className="mb-2"
                                                                    />
                                                                    <Input
                                                                        isReadOnly
                                                                        type="text"
                                                                        label="Ücret"
                                                                        placeholder="Ücret"
                                                                        name="selectedOption"
                                                                        value={oItem.price}
                                                                        className="mb-2"
                                                                    />
                                                                    <Input
                                                                        isReadOnly
                                                                        type="text"
                                                                        label="Adet"
                                                                        placeholder="Adet"
                                                                        name="quantity"
                                                                        value={oItem.quantity}
                                                                    />
                                                                </div>
                                                            </div>
                                                        </AccordionItem>
                                                    )
                                                })
                                            }
                                        </Accordion>
                                    </div>
                                </div>
                            </CardBody>
                        </Card>
                    </Grid>
                </Grid>
            </Container>
            <Footer />
        </>
    );
};

EditOrderPage.getLayout = (page: React.ReactNode) => <SidebarLayout>{page}</SidebarLayout>;

export const getServerSideProps = authMiddleware(async (_context: any) => {
    return {
        props: {},
    };
});

export default EditOrderPage;
