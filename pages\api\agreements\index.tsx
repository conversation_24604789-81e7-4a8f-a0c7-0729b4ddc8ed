import { PrismaClient } from '@prisma/client';

const prisma = new PrismaClient();

export default async function handler(req, res) {
  if (req.method !== 'GET') {
    return res.status(405).json({ message: 'Method not allowed' });
  }

  const { id } = req.query;

  const where = {
    id: parseInt(id),
  };

  try {
    const agreements = await prisma.agreement.findMany({
      where
    });

    res.status(200).json({ results: agreements });
  } catch (error) {
    console.error(error);
    res.status(500).json({ message: 'Internal server error' });
  }
}
