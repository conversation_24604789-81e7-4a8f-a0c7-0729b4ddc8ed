import { PrismaClient } from '@prisma/client';
import { startOfYear, startOfMonth, startOfWeek } from 'date-fns';

const prisma = new PrismaClient();

export default async function handler(req, res) {
    if (req.method !== 'GET') {
        return res.status(405).json({ message: 'Method not allowed' });
    }

    const { id, page = 1, pageSize = 10, filter = "", dateRange = 0 } = req.query;

    const currentDate = new Date();

    let startDate;

    switch (parseInt(dateRange)) {
        case 1: // This month
            startDate = startOfMonth(currentDate);
            break;
        case 2: // This week
            startDate = startOfWeek(currentDate);
            break;
        default: // This year (or any other value)
            startDate = startOfYear(currentDate);
            break;
    }

    const where = {
        vendor: {
            city: {
                id: parseInt(id),
            }
        },
        name: {
            contains: filter
        },
        date: {
            gte: startDate,
        },
    };

    try {
        const concerts = await prisma.concert.findMany({
            where,
            skip: (parseInt(page) - 1) * parseInt(pageSize),
            take: parseInt(pageSize),
        });

        const tickets = await prisma.userTicket.findMany({
            where: {
                ticket: {
                    concert: {
                        vendor: {
                            city: {
                                id: parseInt(id),
                            }
                        },
                        name: {
                            contains: filter
                        },
                        date: {
                            gte: startDate,
                        },
                    }
                }
            },
            include: {
                ticket: true,
                user: true
            },
            orderBy: {
                user: {
                    firstName: 'asc'
                }
            }
        });

        const results = concerts.map((concert) => {
            const matchingTickets = tickets.filter((t) => t.ticket.concertId === concert.id);
            return {
                ...concert,
                tickets: matchingTickets,
            };
        });

        const totalCount = await prisma.concert.count({ where });
        // Close the Prisma client connection
        await prisma.$disconnect();

        res.status(200).json({ results, totalCount });
    } catch (error) {
        console.error(error);
        // Close the Prisma client connection
        await prisma.$disconnect();
        res.status(500).json({ message: 'Internal server error' });
    }
}
