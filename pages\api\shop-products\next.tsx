import { PrismaClient } from '@prisma/client';

const prisma = new PrismaClient();

export default async function handler(req, res) {
    if (req.method === 'GET') {
        try {
            const result = await prisma.$queryRaw`SELECT MAX(id) AS maxId FROM shop_products;`

            const nextId = (result[0]?.maxId || 0) + 1;

            res.status(200).json({ nextId });
        } catch (error) {
            console.error("Error retrieving next ID:", error);
            res.status(500).json({ error: "Internal server error" });
        } finally {
            await prisma.$disconnect();
        }
    } else {
        res.status(405).json({ message: 'Method not allowed' });
    }
};
