import { PrismaClient } from '@prisma/client';

const prisma = new PrismaClient();

export default async function handler(req, res) {
    if (req.method === 'POST') {
        const { vendorId, name, description, details, rank, price, discountedPrice, options, status, hollyPoints, images, categoryId } = req.body;

        try {
            const createdSnap = await prisma.shopProduct.create({
                data: {
                    vendorId,
                    categoryId,
                    name,
                    description,
                    rank: rank ?? null,
                    price: parseFloat(price),
                    discountedPrice: parseFloat(discountedPrice),
                    status,
                    taxRate: 20,
                    hollyPoints,
                    details: JSON.stringify(details),
                    options: options ? options.join(",") : null,
                    images: images.join(","),
                    isDeleted: false
                },
            });

            res.status(201).json({ type: 'success', message: '<PERSON><PERSON><PERSON><PERSON> eklendi!', createdItem: createdSnap });
        } catch (error) {
            console.error(error);
            res.status(500).json({ type: 'error', message: 'Internal server error', error });
        }
    } else {
        res.status(405).json({ message: 'Method not allowed' });
    }
}
