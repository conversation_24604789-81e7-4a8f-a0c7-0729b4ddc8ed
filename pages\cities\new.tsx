import React, { useState } from 'react';
import Head from 'next/head';
import { useRouter } from 'next/router';
import { authMiddleware } from 'middleware';
import Swal from 'sweetalert2';
import { Container, Grid, Typography, CircularProgress, Backdrop } from '@mui/material';
import { Card, CardBody, CardHeader, Button, Input, Divider } from '@nextui-org/react';
import SidebarLayout from '@/layouts/SidebarLayout';
import PageTitleWrapper from '@/components/PageTitleWrapper';
import Footer from '@/components/Footer';
import ArrowBackIcon from '@mui/icons-material/ArrowBack';
import Link from 'next/link';

const NewStagePage = () => {
    const router = useRouter();
    const [item, setItem]: any = useState({});
    const [loading, setLoading] = useState(false);

    const handleInsert = async (event: React.FormEvent) => {
        event.preventDefault();
        if (!item.title || !item.abbreviation || item.rank == null) {
            Swal.fire({
                icon: 'error',
                title: '<PERSON>a',
                text: '<PERSON>ütfen alanları doldurun!',
            });
            return;
        }
        try {
            setLoading(true);
            const response = await fetch(`/api/cities/insert`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify(item),
            });

            if (response.ok) {
                Swal.fire({
                    icon: 'success',
                    title: 'Başarılı',
                    text: 'Kayıt başarılı!',
                }).then(() => {
                    router.push('/cities');
                });
            } else {
                Swal.fire({
                    icon: 'error',
                    title: 'Hata',
                    text: 'Bir sorun meydana geldi!',
                });
            }
        } catch (error) {
            console.error('An error occurred:', error);
        } finally {
            setLoading(false);
        }
    };

    return (
        <>
            <Head>
                <title>Şehirler - Oluştur</title>
            </Head>
            <PageTitleWrapper>
                <Grid container alignItems="center">
                    <Grid item>
                        <Typography variant="h3" component="h3" gutterBottom>
                            Şehir Oluştur
                        </Typography>
                    </Grid>
                </Grid>
            </PageTitleWrapper>
            <Container maxWidth="lg">
                <Grid
                    container
                    direction="row"
                    justifyContent="center"
                    alignItems="stretch"
                    spacing={4}
                >
                    <Grid item xs={12}>
                        <Card
                            isBlurred
                            className="border-none bg-white dark:bg-default-100/50 max-w-[610px]"
                            shadow="sm"
                        >
                            <form onSubmit={handleInsert}>
                                <CardHeader className="flex justify-between gap-3">
                                    <div>
                                        <Link href="/cities">
                                            <Button type="button" color="default" startContent={<ArrowBackIcon />}>
                                                Şehirler
                                            </Button>
                                        </Link>
                                    </div>
                                    <div></div>
                                    <div className="flex-end">
                                        <Button type="submit" color="primary">
                                            Kaydet
                                        </Button>
                                    </div>
                                </CardHeader>

                                <Divider />

                                <CardBody>
                                    <div className="flex">
                                        <div className="p-4 w-full">
                                            <div className="w-full mb-2 flex flex-row">
                                                <Input
                                                    type="text"
                                                    label="Başlık"
                                                    name="title"
                                                    value={item.title}
                                                    className="mr-2"
                                                    onChange={(e: any) => setItem({ ...item, title: e.target.value })}
                                                />
                                                <Input
                                                    type="text"
                                                    label="Kısaltma"
                                                    name="abbreviation"
                                                    value={item.abbreviation}
                                                    className="mr-2"
                                                    onChange={(e: any) => setItem({ ...item, abbreviation: e.target.value.toUpperCase() })}
                                                />
                                                <Input
                                                    type="number"
                                                    label="Sıra"
                                                    placeholder="Sıra"
                                                    min={0}
                                                    name="rank"
                                                    value={item.rank}
                                                    onChange={(e: any) => setItem({ ...item, rank: parseInt(e.target.value) })}
                                                />
                                            </div>
                                        </div>
                                    </div>
                                </CardBody>
                            </form>
                        </Card>
                    </Grid>
                </Grid>
            </Container>
            <Footer />
            <Backdrop open={loading} style={{ zIndex: 9999 }}>
                <CircularProgress color="primary" />
            </Backdrop>
        </>
    );
};

NewStagePage.getLayout = (page: React.ReactNode) => <SidebarLayout>{page}</SidebarLayout>;

export const getServerSideProps = authMiddleware(async (_context: any) => {
    return {
        props: {},
    };
});

export default NewStagePage;
