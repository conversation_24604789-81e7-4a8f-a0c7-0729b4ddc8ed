import { PrismaClient } from '@prisma/client';

const prisma = new PrismaClient();

export default async function handler(req, res) {
    if (req.method === 'PUT') {
        const { id, carrier, deliveryTrackingNumber, estimatedDeliveryDate, status } = req.body;

        try {
            const updatedOrder = await prisma.shopOrder.update({
                where: {
                    id: parseInt(id),
                },
                data: {
                    carrier,
                    deliveryTrackingNumber,
                    estimatedDeliveryDate,
                    status,
                },
            });

            res.status(200).json({ message: '<PERSON>par<PERSON><PERSON> güncellendi!', updatedItem: updatedOrder });
        } catch (error) {
            console.error(error);
            res.status(500).json({ message: 'Internal server error', error });
        }
    } else {
        res.status(405).json({ message: 'Method not allowed' });
    }
}
