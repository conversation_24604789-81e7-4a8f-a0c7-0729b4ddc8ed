import { PrismaClient } from '@prisma/client';

const prisma = new PrismaClient();

export default async function handler(req, res) {
    if (req.method === 'DELETE') {
        const { id } = req.body;

        try {
            const updatedTicket = await prisma.concertTicket.update({
                where: {
                    id: parseInt(id),
                },
                data: {
                    isDeleted: true,
                },
            });

            res.status(200).json({ message: '<PERSON>ilet başarılı bir şekilde silindi!', updatedItem: updatedTicket });
        } catch (error) {
            console.error(error);
            res.status(500).json({ message: 'Internal server error', error });
        }
    } else {
        res.status(405).json({ message: 'Method not allowed' });
    }
}
