import React, { useState, useEffect } from 'react';
import Head from 'next/head';
import { authMiddleware } from '../../middleware';
import SidebarLayout from '@/layouts/SidebarLayout';
import PageTitleWrapper from '@/components/PageTitleWrapper';
import { Typography, Container, Grid, CircularProgress } from '@mui/material';
import { Image, Card, CardBody, CardHeader, Button, Input, Textarea, Divider } from '@nextui-org/react';
import { Select, SelectItem } from '@nextui-org/select';
import Footer from '@/components/Footer';
import { useRouter } from 'next/router';
import Swal from 'sweetalert2';
import ArrowBackIcon from '@mui/icons-material/ArrowBack';
import Link from 'next/link';

const EditDailyActivityPage = () => {
    const router = useRouter();
    const { id } = router.query;

    const [item, setItem]: any = useState({});
    const daysOfWeek = {
        1: "<PERSON><PERSON><PERSON>",
        2: "<PERSON><PERSON>",
        3: "<PERSON><PERSON><PERSON><PERSON><PERSON>",
        4: "<PERSON><PERSON><PERSON><PERSON>",
        5: "<PERSON>uma",
        6: "<PERSON><PERSON><PERSON><PERSON>",
        7: "<PERSON><PERSON>"
    };
    const [stages, setStages] = useState([]);
    const [vendors, setVendors] = useState([]);
    const [loading, setLoading] = useState(true);
    const [currentTimestamp, setCurrentTimestamp] = useState(new Date().getTime());
    const [imageLoading, setImageLoading] = useState(false);

    const loadList = async () => {
        const url = `/api/daily-activities?id=${id}`;

        try {
            const res = await fetch(url);

            if (res.status === 200) {
                const json = await res.json();
                json.results[0].gateDate = new Date(json.results[0]?.gateDate);
                json.results[0].date = new Date(json.results[0]?.date);
                setItem(json.results[0]);
            } else {
                Swal.fire({
                    icon: 'error',
                    title: 'Hata',
                    text: 'Bir sorun meydana geldi!',
                });
            }
        } catch (error) {
            console.error(error);
        } finally {
            setLoading(false);
        }
    };

    const loadStages = async () => {
        const url = `/api/stages`;

        try {
            const res = await fetch(url);

            if (res.status === 200) {
                const json = await res.json();
                setStages(json.results);
            } else {
                Swal.fire({
                    icon: 'error',
                    title: 'Hata',
                    text: 'Bir sorun meydana geldi!',
                });
            }
        } catch (error) {
            console.error(error);
        } finally {
            setLoading(false);
        }
    };

    const loadVendors = async () => {
        const url = `/api/vendors`;

        try {
            const res = await fetch(url);

            if (res.status === 200) {
                const json = await res.json();
                setVendors(json.results);
            } else {
                Swal.fire({
                    icon: 'error',
                    title: 'Hata',
                    text: 'Bir sorun meydana geldi!',
                });
            }
        } catch (error) {
            console.error(error);
        } finally {
            setLoading(false);
        }
    };

    useEffect(() => {
        loadList();
        loadStages();
        loadVendors();
    }, []);

    const handleImageChange = async (event) => {
        const file = event.target.files[0];

        if (file) {
            if (!file.type.startsWith('image/')) {
                Swal.fire({
                    icon: 'error',
                    title: 'Hata',
                    text: 'Seçilen format desteklenmiyor!',
                });
                return;
            }
            setImageLoading(true);
            const formData = new FormData();
            formData.append('image', file);
            formData.append('fileName', `daily_activities/${item.id}`);

            try {
                const response = await fetch(`https://api.hollystone.com.tr/api/functions/upload`, {
                    method: 'POST',
                    headers: {
                        'Authorization': `Bearer ${localStorage.getItem('token')}`,
                    },
                    body: formData,
                });

                if (response.ok) {
                    const data = await response.json();

                    if (data.type == "success") {
                        Swal.fire({
                            icon: 'success',
                            title: 'Başarılı',
                            text: 'Resim güncellendi!',
                        });
                        setCurrentTimestamp(new Date().getTime());
                    } else {
                        Swal.fire({
                            icon: 'error',
                            title: 'Hata',
                            text: data.error,
                        });
                    }
                } else {
                    Swal.fire({
                        icon: 'error',
                        title: 'Hata',
                        text: 'Bir sorun meydana geldi!',
                    });
                }
            } catch (error) {
                Swal.fire({
                    icon: 'error',
                    title: 'Hata',
                    text: 'Bir sorun meydana geldi!',
                });
            }
            setImageLoading(false);
        }
    };

    const handleDelete = async () => {
        const shouldDelete = await Swal.fire({
            title: 'Dikkat!',
            text: 'Bu kaydı silmek istediğine gerçekten emin misin?',
            icon: 'warning',
            showCancelButton: true,
            confirmButtonText: 'Evet, sil!',
            cancelButtonText: 'Vazgeç',
        });

        if (shouldDelete.isConfirmed) {
            try {
                const response = await fetch(`/api/daily-activities/delete`, {
                    method: 'DELETE',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({ id: id }),
                });

                if (response.ok) {
                    router.push('/daily-activities');
                    Swal.fire({
                        icon: 'success',
                        title: 'Başarılı',
                        text: 'Kayıt silindi!',
                    });
                } else {
                    Swal.fire({
                        icon: 'error',
                        title: 'Hata',
                        text: 'Bir sorun meydana geldi!',
                    });
                }
            } catch (error) {
                console.error('An error occurred:', error);
            }
        }
    };

    const handleUpdate = async (event) => {
        event.preventDefault();
        const updatedData = {
            id: id,
            dayOfWeek: item.dayOfWeek,
            stageId: item.stageId,
            name: item.name,
            description: item.description,
            date: item.date,
            gateDate: item.gateDate
        }
        try {
            const response = await fetch(`/api/daily-activities/update`, {
                method: 'PUT',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify(updatedData),
            });

            if (response.ok) {
                await loadList();
                Swal.fire({
                    icon: 'success',
                    title: 'Başarılı',
                    text: 'Kayıt güncellendi!',
                });
            } else {
                Swal.fire({
                    icon: 'error',
                    title: 'Hata',
                    text: 'Bir sorun meydana geldi!',
                });
            }
        } catch (error) {
            console.error('An error occurred:', error);
        }
    }

    const renderContent = () => {
        if (loading) {
            return <p>Loading...</p>;
        }

        return (
            <>
                <Head>
                    <title>Günlük Etkinlik - Düzenle</title>
                </Head>
                <PageTitleWrapper>
                    <Grid container alignItems="center">
                        <Grid item>
                            <Typography variant="h3" component="h3" gutterBottom>
                                Günlük Etkinlik Düzenle
                            </Typography>
                        </Grid>
                    </Grid>
                </PageTitleWrapper>
                <Container maxWidth="lg">
                    <Grid
                        container
                        direction="row"
                        justifyContent="center"
                        alignItems="stretch"
                        spacing={4}
                    >
                        <Grid item xs={12}>
                            <Card
                                isBlurred
                                className="border-none bg-white dark:bg-default-100/50"
                                shadow="sm"
                            >
                                <form onSubmit={handleUpdate}>
                                    <CardHeader className="flex justify-between gap-3">
                                        <div>
                                            <Link href="/daily-activities">
                                                <Button type="button" color="default" startContent={<ArrowBackIcon />}>
                                                    Günlük Etkinlikler
                                                </Button>
                                            </Link>
                                        </div>
                                        <div>Günlük Etkinlik Bilgileri</div>
                                        <div className="flex-end">
                                            <Button type="button" onClick={handleDelete} color="danger" className="mr-2">
                                                Sil
                                            </Button>
                                            <Button type="submit" color="primary">
                                                Güncelle
                                            </Button>
                                        </div>
                                    </CardHeader>

                                    <Divider />

                                    <CardBody>
                                        <div className="flex">
                                            <div className="relative w-1/2">
                                                <label className="block cursor-pointer">
                                                    <Image
                                                        width={400}
                                                        height={400}
                                                        radius="none"
                                                        loading="eager"
                                                        style={{
                                                            height: "100%"
                                                        }}
                                                        src={`https://api.hollystone.com.tr/resources/images/${item.image}?t=${currentTimestamp}`}
                                                        fallbackSrc="https://via.placeholder.com/400x400.png?text="
                                                    />
                                                    {imageLoading ? (
                                                        <CircularProgress
                                                            size={48}
                                                            style={{
                                                                position: 'absolute',
                                                                top: '40%',
                                                                left: '40%',
                                                                transform: 'translate(-40%, -40%)',
                                                                zIndex: 999,
                                                            }}
                                                        />
                                                    ) : null}
                                                    <Input
                                                        type="file"
                                                        label="Resim"
                                                        name="image"
                                                        className="hidden"
                                                        onChange={handleImageChange}
                                                    />
                                                </label>
                                            </div>

                                            <div className="p-4 w-full">
                                                <div className="w-full mb-2 flex flex-row">
                                                    <Input
                                                        type="text"
                                                        label="Başlık"
                                                        name="name"
                                                        value={item.name}
                                                        className="mr-2"
                                                        onChange={(e: any) => setItem({ ...item, name: e.target.value })}
                                                    />
                                                    <Select
                                                        label="Sahne"
                                                        placeholder="Bir seçim yapın..."
                                                        defaultSelectedKeys={[item.stageId?.toString()]}
                                                        className="mr-2"
                                                        onChange={(e: any) => setItem({ ...item, stageId: parseInt(e.target.value) })}
                                                    >
                                                        {stages.map((stage, index) => {
                                                            return (
                                                                <SelectItem key={stage.id} value={index}>
                                                                    {stage.name}
                                                                </SelectItem>
                                                            )
                                                        })}
                                                    </Select>
                                                    <Select
                                                        label="Bayi"
                                                        placeholder="Bir seçim yapın..."
                                                        defaultSelectedKeys={[item.vendorId?.toString()]}
                                                        onChange={(e: any) => setItem({ ...item, vendorId: parseInt(e.target.value) })}
                                                    >
                                                        {vendors.map((vendor, index) => {
                                                            return (
                                                                <SelectItem key={vendor.id} value={index}>
                                                                    {vendor.name}
                                                                </SelectItem>
                                                            )
                                                        })}
                                                    </Select>
                                                </div>
                                                <div className="w-full mb-2 flex flex-row">
                                                    <Input
                                                        type="time"
                                                        label="Kapı Açılış Saati"
                                                        name="gateDate"
                                                        value={item.gateDate?.toISOString().substr(11, 5)}
                                                        className="mr-2"
                                                        onChange={(e: any) => {
                                                            const [hours, minutes] = e.target.value.split(':');
                                                            const newGateDate = new Date(item.gateDate);
                                                            newGateDate.setUTCHours(Number(hours));
                                                            newGateDate.setUTCMinutes(Number(minutes));
                                                            setItem({ ...item, gateDate: newGateDate });
                                                        }}
                                                    />
                                                    <Input
                                                        type="time"
                                                        label="Sahne Saati"
                                                        name="date"
                                                        value={item.date?.toISOString().substr(11, 5)}
                                                        className="mr-2"
                                                        onChange={(e: any) => {
                                                            const [hours, minutes] = e.target.value.split(':');
                                                            const newDate = new Date(item.date);
                                                            newDate.setUTCHours(Number(hours));
                                                            newDate.setUTCMinutes(Number(minutes));
                                                            setItem({ ...item, date: newDate });
                                                        }}
                                                    />
                                                    <Select
                                                        label="Haftanın Günü"
                                                        placeholder="Bir seçim yapın..."
                                                        defaultSelectedKeys={[item.dayOfWeek?.toString()]}
                                                        onChange={(e: any) => setItem({ ...item, dayOfWeek: parseInt(e.target.value) })}
                                                    >
                                                        {Object.keys(daysOfWeek).map((key) => (
                                                            <SelectItem key={key} value={key}>
                                                                {daysOfWeek[key]}
                                                            </SelectItem>
                                                        ))}
                                                    </Select>
                                                </div>
                                                <Textarea
                                                    label="Açıklama"
                                                    name="description"
                                                    value={item.description}
                                                    className="mb-2"
                                                    onChange={(e: any) => setItem({ ...item, description: e.target.value })}
                                                />
                                            </div>
                                        </div>
                                    </CardBody>
                                </form>

                            </Card>
                        </Grid>
                    </Grid>
                </Container>
                <Footer />
            </>
        );
    };

    return renderContent();
};

EditDailyActivityPage.getLayout = (page: React.ReactNode) => <SidebarLayout>{page}</SidebarLayout>;

export const getServerSideProps = authMiddleware(async (_context: any) => {
    return {
        props: {},
    };
});

export default EditDailyActivityPage;
