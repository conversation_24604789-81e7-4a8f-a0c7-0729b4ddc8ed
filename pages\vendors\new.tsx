import React, { useState, useEffect } from 'react';
import Head from 'next/head';
import { useRouter } from 'next/router';
import { authMiddleware } from 'middleware';
import Swal from 'sweetalert2';
import { Container, Grid, Typography, CircularProgress, Backdrop } from '@mui/material';
import { Card, CardBody, CardHeader, Button, Textarea, Input, Divider } from '@nextui-org/react';
import { Select, SelectItem } from "@nextui-org/select";
import SidebarLayout from '@/layouts/SidebarLayout';
import PageTitleWrapper from '@/components/PageTitleWrapper';
import Footer from '@/components/Footer';
import ArrowBackIcon from '@mui/icons-material/ArrowBack';
import Link from 'next/link';

const NewStagePage = () => {
    const router = useRouter();
    const [cities, setCities] = useState([]);
    const [item, setItem]: any = useState({});
    const [loading, setLoading] = useState(false);

    const modules = [
        "Holly Shop"
    ]

    const loadCities = async () => {
        const url = `/api/cities`;

        try {
            const res = await fetch(url);

            if (res.status === 200) {
                const json = await res.json();
                setCities(json.results);
            } else {
                Swal.fire({
                    icon: 'error',
                    title: 'Hata',
                    text: 'Bir sorun meydana geldi!',
                });
            }
        } catch (error) {
            console.error(error);
        } finally {
            setLoading(false);
        }
    };

    useEffect(() => {
        loadCities();
    }, []);

    const handleInsert = async (event: React.FormEvent) => {
        event.preventDefault();
        if (!item.name) {
            Swal.fire({
                icon: 'error',
                title: 'Hata',
                text: 'Lütfen alanları doldurun!',
            });
            return;
        }
        try {
            setLoading(true);
            const response = await fetch(`/api/vendors/insert`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify(item),
            });

            if (response.ok) {
                Swal.fire({
                    icon: 'success',
                    title: 'Başarılı',
                    text: 'Kayıt başarılı!',
                }).then(() => {
                    router.push('/vendors');
                });
            } else {
                Swal.fire({
                    icon: 'error',
                    title: 'Hata',
                    text: 'Bir sorun meydana geldi!',
                });
            }
        } catch (error) {
            console.error('An error occurred:', error);
        } finally {
            setLoading(false);
        }
    };

    return (
        <>
            <Head>
                <title>Bayi - Oluştur</title>
            </Head>
            <PageTitleWrapper>
                <Grid container alignItems="center">
                    <Grid item>
                        <Typography variant="h3" component="h3" gutterBottom>
                            Bayi Oluştur
                        </Typography>
                    </Grid>
                </Grid>
            </PageTitleWrapper>
            <Container maxWidth="lg">
                <Grid
                    container
                    direction="row"
                    justifyContent="center"
                    alignItems="stretch"
                    spacing={4}
                >
                    <Grid item xs={12}>
                        <Card
                            isBlurred
                            className="border-none bg-white dark:bg-default-100/50 max-w-[610px]"
                            shadow="sm"
                        >
                            <form onSubmit={handleInsert}>
                                <CardHeader className="flex justify-between gap-3">
                                    <div>
                                        <Link href="/vendors">
                                            <Button type="button" color="default" startContent={<ArrowBackIcon />}>
                                                Bayiler
                                            </Button>
                                        </Link>
                                    </div>
                                    <div className="flex-end">
                                        <Button type="submit" color="primary">
                                            Kaydet
                                        </Button>
                                    </div>
                                </CardHeader>

                                <Divider />

                                <CardBody>
                                    <div className="flex">
                                        <div className="p-4 w-full">
                                            <div className="w-full mb-2 flex flex-row">
                                                <Input
                                                    type="text"
                                                    label="İsim"
                                                    name="name"
                                                    value={item.name}
                                                    className="mr-2"
                                                    onChange={(e: any) => setItem({ ...item, name: e.target.value })}
                                                />
                                                <Input
                                                    type="text"
                                                    label="E-mail"
                                                    name="email"
                                                    value={item.email}
                                                    className="mr-2"
                                                    onChange={(e: any) => setItem({ ...item, email: e.target.value })}
                                                />
                                                <Input
                                                    type="text"
                                                    label="Telefon Numarası"
                                                    name="phone"
                                                    value={item.phone}
                                                    onChange={(e: any) => setItem({ ...item, phone: e.target.value })}
                                                />
                                            </div>
                                            <div className="w-full mb-2 flex flex-row">
                                                <Input
                                                    type="text"
                                                    label="Banka Hesap Adı"
                                                    name="bankAccountName"
                                                    value={item.bankAccountName}
                                                    className="mr-2"
                                                    onChange={(e: any) => setItem({ ...item, bankAccountName: e.target.value })}
                                                />
                                                <Input
                                                    type="text"
                                                    label="IBAN"
                                                    name="iban"
                                                    value={item.iban}
                                                    className="mr-2"
                                                    onChange={(e: any) => setItem({ ...item, iban: e.target.value })}
                                                />
                                            </div>
                                            <div className="w-full mb-2 flex flex-row">
                                                <Input
                                                    type="text"
                                                    label="Vergi Dairesi"
                                                    name="taxOffice"
                                                    value={item.taxOffice}
                                                    className="mr-2"
                                                    onChange={(e: any) => setItem({ ...item, taxOffice: e.target.value })}
                                                />
                                                <Input
                                                    type="text"
                                                    label="Vergi Numarası"
                                                    name="taxNumber"
                                                    value={item.taxNumber}
                                                    className="mr-2"
                                                    onChange={(e: any) => setItem({ ...item, taxNumber: e.target.value })}
                                                />
                                                <Input
                                                    type="text"
                                                    label="Posta Kodu"
                                                    name="zipCode"
                                                    value={item.zipCode}
                                                    onChange={(e: any) => setItem({ ...item, zipCode: e.target.value })}
                                                />
                                            </div>
                                            <Textarea
                                                label="Adres"
                                                name="address"
                                                value={item.address}
                                                className="mb-2"
                                                onChange={(e: any) => setItem({ ...item, address: e.target.value })}
                                            />
                                            <Input
                                                type="text"
                                                label="Bir Fatura Token"
                                                name="invoiceToken"
                                                value={item.invoiceToken}
                                                className="mb-2"
                                                onChange={(e: any) => setItem({ ...item, invoiceToken: e.target.value })}
                                            />
                                            <div className="w-full mb-2 flex flex-row">
                                                <Select
                                                    label="Modüller"
                                                    placeholder="Bir seçim yapın..."
                                                    selectionMode="multiple"
                                                    className="mr-2"
                                                    defaultSelectedKeys={item.modules ? item.modules.split(',') : []}
                                                    onChange={(e: any) => {
                                                        setItem({ ...item, modules: e.target.value?.toString() });
                                                    }}
                                                >
                                                    {
                                                        modules.map((innerItem: any, index: number) => {
                                                            return (
                                                                <SelectItem key={index} value={index}>{innerItem}</SelectItem>
                                                            )
                                                        })
                                                    }
                                                </Select>
                                                <Select
                                                    label="Şehir"
                                                    placeholder="Bir seçim yapın..."
                                                    defaultSelectedKeys={item.cityId ? [item.cityId?.toString()] : []}
                                                    onChange={(e: any) => setItem({ ...item, cityId: parseInt(e.target.value) })}
                                                >
                                                    {cities.map((city, index) => {
                                                        return (
                                                            <SelectItem key={city.id} value={index}>
                                                                {city.title}
                                                            </SelectItem>
                                                        )
                                                    })}
                                                </Select>
                                            </div>
                                        </div>
                                    </div>
                                </CardBody>
                            </form>
                        </Card>
                    </Grid>
                </Grid>
            </Container>
            <Footer />
            <Backdrop open={loading} style={{ zIndex: 9999 }}>
                <CircularProgress color="primary" />
            </Backdrop>
        </>
    );
};

NewStagePage.getLayout = (page: React.ReactNode) => <SidebarLayout>{page}</SidebarLayout>;

export const getServerSideProps = authMiddleware(async (_context: any) => {
    return {
        props: {},
    };
});

export default NewStagePage;
