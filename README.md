<p align="center">
    <a href="https://bloomui.com" title="BloomUI.com">
        <img src="https://bloomui.s3.us-east-2.amazonaws.com/tokyo-logo.png" alt="Tokyo Free White Typescript Next.js Admin Dashboard">
    </a>
</p>
<h1 align="center">
    <b>Tokyo Free White Typescript Next.js Admin Dashboard</b>
    <br>
    <a href="https://twitter.com/intent/tweet?url=https://bloomui.com&text=I like this Next.js admin dashboard">
        <img src="https://img.shields.io/twitter/url/http/shields.io.svg?style=social" />
    </a>
</h1>
<div align="center">

![version](https://img.shields.io/badge/version-1.0.0-blue.svg)
![license](https://img.shields.io/badge/license-MIT-blue.svg)

<a href="https://bloomui.com/product/tokyo-free-white-nextjs-typescript-material-ui-admin-dashboard/"><img src="https://bloomui.s3.us-east-2.amazonaws.com/tokyo-free-white-nextjs-typescript-material-ui-admin-dashboard.jpg" /></a>
</div>

<a href="https://bloomui.com/product/tokyo-free-white-nextjs-typescript-material-ui-admin-dashboard/"><h3>Free Material-UI Next.js Typescript Dashboard Template with Dark Color Scheme</h3></a>
<p>
    Tokyo Free White Next.js Typescript Dashboard is built using the latest industry standards and features a clean and premium design style, making use of colors and accents to improve the user experience for all included flows and pages.
</p>
<p>
It is powered by Next.js, Typescript and React and contains multiple components customized on top of Material-UI – which is one of the best UI components frameworks available.</p>
<p>
We keep all dependencies updated to their latest stable versions. Each time we release an updated version you will be notified via the email you used to initially download the template.
</p>
<p>
To discover all the features that this free React admin template has to offer, we suggest visiting the live preview we’ve set up.
</p>
<p>There is also a free Javascript version available, if that is what you prefer working with.</p>

---
<h3>Updrade to PRO</h3>

<p>The premium version of this template comes with a lot more components, features and options making it a very powerful friend in your development endeavors. You can download a copy of it from <a href="https://bloomui.com">bloomui.com</a></p>

---

<h2>
    Quick Start
</h2>
<ol>
    <li>Make sure you have the latest stable versions for Node.js and NPM installed</li>
    <li>Clone repository: <code>git clone https://github.com/bloomui/tokyo-free-white-nextjs-admin-dashboard.git</code></li>
    <li>Install dependencies: Run <code>npm install</code> inside the project folder</li>
    <li>Start dev server: After the install finishes, run <code>yarn dev</code>. A browser window will open on http://localhost:3000 where you''ll see the live preview</li>
</ol>

---

<h2>
    Technical Support
</h2>
<p>
    You can open a support ticket by sending an email here: <a href="mailto:<EMAIL>" title="Open Support Ticket">
        <EMAIL>
    </a>
</p>
