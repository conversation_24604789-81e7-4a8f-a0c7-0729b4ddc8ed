import React, { useState, useMemo, useCallback, useEffect } from "react";
import {
    Table,
    TableHeader,
    TableColumn,
    TableBody,
    TableRow,
    TableCell,
    Input,
    Button,
    Pagination,
    Spinner,
    getKeyValue
} from "@nextui-org/react";
import { Select, SelectItem } from "@nextui-org/select";
import { SearchIcon } from "../../src/components/SearchIcon";
import { withRouter } from 'next/router';
import PrintIcon from '@mui/icons-material/Print';
import jsPDF from 'jspdf'
import autoTable from 'jspdf-autotable'

function CityReportDetailTable({ router }) {
    const { id } = router.query;
    let doc = new jsPDF({ filters: ["ASCIIHexEncode"] })
    const [filterValue, setFilterValue] = useState("");
    const [page, setPage] = useState(1);
    const [total, setTotal] = useState(0);
    const [isLoading, setIsLoading] = useState(true);
    const [items, setItems] = useState([]);
    const [dateRange, setDateRange] = useState(0);
    const dropdownItems = [
        "Yıllık",
        "Aylık",
        "Haftalık"
    ];

    const rowsPerPage = 10;

    const replaceTurkishCharacters = (input) => {
        const replacements = {
            'ş': 's',
            'Ş': 'S',
            'ğ': 'g',
            'Ğ': 'G',
            'ı': 'i',
            'İ': 'I',
            'ü': 'u',
            'Ü': 'U',
            'ö': 'o',
            'Ö': 'O',
            'ç': 'c',
            'Ç': 'C'
        };
    
        return input.replace(/[şŞğĞıİüÜöÖçÇ]/g, match => replacements[match]);
    }

    const loadList = async (pageInput: number, filter: string) => {
        const url = `/api/city-reports/detail?page=${pageInput}&pageSize=${rowsPerPage}&filter=${filter}&id=${id}&dateRange=${dateRange}`;

        try {
            const res = await fetch(url);

            if (res.status === 200) {
                const json = await res.json();

                setTotal(json.totalCount);
                setIsLoading(false);
                setItems(json.results);
            } else {
                setIsLoading(false);
                setItems([]);
            }
        } catch (error) {
            setIsLoading(false);
            setItems([]);
            console.error(error);
            throw error;
        }
    };

    const onPrint = async (cell: any) => {
        const headerImg = new Image();
        headerImg.src = 'https://api.hollystone.com.tr/resources/images/logo.png';

        headerImg.onload = () => {
            const body = cell?.tickets?.map((item: any) => {
                return [
                    item.user.firstName,
                    item.user.lastName,
                    item.user.email,
                    item.user.phoneNumber,
                    `${item.paidPrice} TL`
                ];
            });

            doc.addImage(headerImg, 'PNG', 10, 10, 190, 40);

            doc.text(`Konser Adi: ${replaceTurkishCharacters(cell?.name)}`, 10, 70);
            doc.text(`Tarih: ${new Date(cell?.date).toLocaleDateString("tr-TR")}`, 10, 80);

            autoTable(doc, {
                head: [['Isim', 'Soyisim', 'E-mail', 'Telefon', 'Tutar']],
                body,
                startY: 90
            });

            doc.save(`${cell?.name} Bilet Listesi`);
            doc = new jsPDF({ filters: ["ASCIIHexEncode"] });
        };
    };

    useEffect(() => {
        loadList(1, filterValue);
    }, [dateRange]);

    const pages = Math.ceil(total / rowsPerPage);

    const onPaginationChange = useCallback(
        async (pageInput) => {
            setIsLoading(true);
            setPage(pageInput);
            await loadList(pageInput, filterValue);
        },
        []
    );

    const onSearchChange = useCallback(
        (value: string) => {
            if (value) {
                setIsLoading(true);
                setFilterValue(value);
                loadList(page, value);
                setPage(1);
            } else {
                setFilterValue("");
                loadList(page, "");
            }
        },
        []
    );

    const onClear = useCallback(() => {
        setFilterValue("");
        setPage(1);
    }, []);

    const topContent = useMemo(() => {
        return (
            <div className="flex flex-row gap-4">
                <Input
                    isClearable
                    className="w-full sm:max-w-[44%]"
                    placeholder="Arayın..."
                    startContent={<SearchIcon />}
                    value={filterValue}
                    onClear={() => onClear()}
                    onValueChange={onSearchChange}
                />
                <div className="flex gap-3">
                    <Select
                        placeholder="Bir seçim yapın..."
                        labelPlacement="outside"
                        defaultSelectedKeys={[dateRange?.toString()]}
                        onSelectionChange={(e: any) => {
                            setDateRange(parseInt(e.currentKey));
                        }}
                        style={{
                            minWidth: "150px"
                        }}
                        className="mb-2 h-5"
                    >
                        {dropdownItems.map((dItem: any, dIndex: any) => {
                            return (
                                <SelectItem key={dIndex} value={dIndex}>
                                    {dItem}
                                </SelectItem>
                            )
                        })}
                    </Select>
                </div>
            </div>
        );
    }, [
        filterValue,
        onSearchChange,
        onClear,
    ]);

    const renderCell = useCallback((cellValue: any, columnKey: any, rowId: any) => {
        const cell = items.find(item => item.id === rowId);

        if (!cell) {
            // Handle the loading state while waiting for the cell data to be available.
            return <div>Loading...</div>;
        }

        switch (columnKey) {
            case "status":
                return cellValue ? "aktif" : "pasif";
            case "total":
                return cell?.tickets?.length;
            case "price":
                return `${cell?.tickets?.reduce((acc, item) => acc + item.paidPrice, 0) / cell?.tickets?.length}₺`;
            case "income":
                return `${cell?.tickets?.reduce((acc, item) => acc + item.paidPrice, 0)}₺`;
            case "date":
                return new Date(cell?.date).toLocaleDateString("tr-TR");
            case "actions":
                return (
                    <div className="relative flex justify-end items-center gap-2">
                        <Button
                            onClick={() => onPrint(cell)}
                            color="primary"
                            endContent={<PrintIcon />}
                        >
                            Biletleri Yazdır
                        </Button>
                    </div>
                );
            default:
                return cellValue;
        }
    }, [items]);

    return (
        <Table
            aria-label="Example table with client async pagination"
            topContent={topContent}
            topContentPlacement="inside"
            bottomContent={
                pages > 0 ? (
                    <div className="flex w-full justify-center">
                        <Pagination
                            isCompact
                            showControls
                            showShadow
                            color="primary"
                            page={page}
                            total={pages}
                            onChange={onPaginationChange}
                        />
                    </div>
                ) : null
            }
            classNames={{
                table: "min-h-[400px]",
            }}
        >
            <TableHeader>
                <TableColumn key="name">Konser Adı</TableColumn>
                <TableColumn key="date">Konser Tarihi</TableColumn>
                <TableColumn key="price">Bilet Fiyatı</TableColumn>
                <TableColumn key="total">Toplam Satış</TableColumn>
                <TableColumn key="income">Toplam Gelir</TableColumn>
                <TableColumn key="actions">İşlemler</TableColumn>
            </TableHeader>
            <TableBody
                isLoading={isLoading && !items.length}
                items={items}
                loadingContent={<Spinner />}
                emptyContent={"Kayıt bulunamadı!"}
            >
                {(item) => (
                    <TableRow key={item.id}>
                        {(columnKey) => (
                            <TableCell key={columnKey + item.id}>{renderCell(getKeyValue(item, columnKey), columnKey, item.id)}</TableCell>
                        )}
                    </TableRow>
                )}
            </TableBody>
        </Table>
    );
}

export default withRouter(CityReportDetailTable);
