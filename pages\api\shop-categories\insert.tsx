import { PrismaClient } from '@prisma/client';

const prisma = new PrismaClient();

export default async function handler(req, res) {
    if (req.method === 'POST') {
        const { name, rank, status } = req.body;

        try {
            const createdCategory = await prisma.shopCategory.create({
                data: {
                    name: name,
                    rank: rank,
                    status: status,
                    isDeleted: false
                },
            });

            res.status(201).json({ message: 'Kategori eklendi!', createdItem: createdCategory });
        } catch (error) {
            console.error(error);
            res.status(500).json({ message: 'Internal server error', error });
        }
    } else {
        res.status(405).json({ message: 'Method not allowed' });
    }
}
