import React, { useState, useEffect } from 'react';
import Head from 'next/head';
import { useRouter } from 'next/router';
import { authMiddleware } from 'middleware';
import Swal from 'sweetalert2';
import { Container, Grid, Typography, CircularProgress } from '@mui/material';
import { Image, Card, CardBody, CardHeader, Button, Input, Divider, Accordion, AccordionItem } from '@nextui-org/react';
import { Select, SelectItem } from "@nextui-org/select";
import SidebarLayout from '@/layouts/SidebarLayout';
import PageTitleWrapper from '@/components/PageTitleWrapper';
import Footer from '@/components/Footer';
import ArrowBackIcon from '@mui/icons-material/ArrowBack';
import Link from 'next/link';

const NewWeeklyShamanPage = () => {
    const router = useRouter();
    const [item, setItem]: any = useState({});
    const [loading, setLoading] = useState(true);
    const [selectedImage, setSelectedImage] = useState(null);
    const [selectedImage2, setSelectedImage2] = useState(null);
    const [imageLoading, setImageLoading] = useState(false);
    const [dropdownConcerts, setDropdownConcerts] = useState([]);
    const dropdownItems = {
        product: "Ürün",
        hollyPoints: "Holly Puan",
        ticket: "Bilet"
    };

    const loadConcerts = async () => {
        const url = `/api/concerts`;

        try {
            const res = await fetch(url);

            if (res.status === 200) {
                const json = await res.json();
                setDropdownConcerts(json.results);
            } else {
                Swal.fire({
                    icon: 'error',
                    title: 'Hata',
                    text: 'Bir sorun meydana geldi!',
                });
            }
        } catch (error) {
            console.error(error);
        } finally {
            setLoading(false);
        }
    };

    useEffect(() => {
        loadConcerts();
    }, []);

    const handleImageChange = (event: React.ChangeEvent<HTMLInputElement>) => {
        const file = event.target.files?.[0];

        if (file) {
            if (file.type.startsWith('image/')) {
                setSelectedImage(URL.createObjectURL(file));
                setItem({ ...item, prizeImage: file });
            } else {
                Swal.fire({
                    icon: 'error',
                    title: 'Hata',
                    text: 'Seçilen format desteklenmiyor!',
                });
                return;
            }
        }
    };

    const handleImageChange2 = (event: React.ChangeEvent<HTMLInputElement>) => {
        const file = event.target.files?.[0];

        if (file) {
            if (file.type.startsWith('image/')) {
                setSelectedImage2(URL.createObjectURL(file));
                setItem({ ...item, prizeSponsorImage: file });
            } else {
                Swal.fire({
                    icon: 'error',
                    title: 'Hata',
                    text: 'Seçilen format desteklenmiyor!',
                });
                return;
            }
        }
    };

    const handleInsert = async (event: React.FormEvent) => {
        event.preventDefault();
        try {
            if (!item.prizeImage) {
                Swal.fire({
                    icon: 'error',
                    title: 'Hata',
                    text: 'Lütfen bir ödül resim seçin!',
                });
                return;
            }

            if (!item.prizeSponsorImage) {
                Swal.fire({
                    icon: 'error',
                    title: 'Hata',
                    text: 'Lütfen bir sponsor resim seçin!',
                });
                return;
            }

            const response = await fetch(`/api/monthly-shaman/insert`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify(item),
            });

            if (!response.ok) {
                throw new Error('Bir sorun meydana geldi!');
            }

            const data = await response.json();
            const id = data.createdItem.id;

            setImageLoading(true);

            const formData = new FormData();
            formData.append('image', item.prizeImage);
            formData.append('fileName', `shaman/p${id}`);

            try {
                const response = await fetch(`https://api.hollystone.com.tr/api/functions/upload`, {
                    method: 'POST',
                    headers: {
                        'Authorization': `Bearer ${localStorage.getItem('token')}`
                    },
                    body: formData,
                });

                if (!response.ok) {
                    throw new Error('Bir sorun meydana geldi!');
                }

                const data = await response.json();

                if (data.type === "success") {
                    const formData2 = new FormData();
                    formData2.append('image', item.prizeSponsorImage);
                    formData2.append('fileName', `shaman/s${id}`);

                    try {
                        const response = await fetch(`https://api.hollystone.com.tr/api/functions/upload`, {
                            method: 'POST',
                            headers: {
                                'Authorization': `Bearer ${localStorage.getItem('token')}`
                            },
                            body: formData2,
                        });

                        if (!response.ok) {
                            throw new Error('Bir sorun meydana geldi!');
                        }

                        const data = await response.json();

                        if (data.type === "success") {
                            Swal.fire({
                                icon: 'success',
                                title: 'Başarılı',
                                text: 'Kayıt başarılı!',
                            }).then(() => {
                                router.push('/monthly-shaman');
                            });
                        } else {
                            Swal.fire({
                                icon: 'error',
                                title: 'Hata',
                                text: data.error,
                            });
                        }
                    } catch (error) {
                        console.error('An error occurred:', error);
                        Swal.fire({
                            icon: 'error',
                            title: 'Hata',
                            text: 'Bir sorun meydana geldi!',
                        });
                    }
                } else {
                    Swal.fire({
                        icon: 'error',
                        title: 'Hata',
                        text: data.error,
                    });
                }
            } catch (error) {
                console.error('An error occurred:', error);
                Swal.fire({
                    icon: 'error',
                    title: 'Hata',
                    text: 'Bir sorun meydana geldi!',
                });
            }
        } finally {
            setImageLoading(false);
        }
    };

    if (loading) {
        return "Loading";
    }

    return (
        <>
            <Head>
                <title>Şaman Aylık Ödül - Oluştur</title>
            </Head>
            <PageTitleWrapper>
                <Grid container alignItems="center">
                    <Grid item>
                        <Typography variant="h3" component="h3" gutterBottom>
                            Şaman Aylık Ödül Oluştur
                        </Typography>
                    </Grid>
                </Grid>
            </PageTitleWrapper>
            <Container maxWidth="lg">
                <Grid
                    container
                    direction="row"
                    justifyContent="center"
                    alignItems="stretch"
                    spacing={4}
                >
                    <Grid item xs={12}>
                        <Card
                            isBlurred
                            className="border-none bg-white dark:bg-default-100/50"
                            shadow="sm"
                        >
                            <form onSubmit={handleInsert}>
                                <CardHeader className="flex justify-between gap-3">
                                    <div>
                                        <Link href="/monthly-shaman">
                                            <Button type="button" color="default" startContent={<ArrowBackIcon />}>
                                                Aylık Şaman
                                            </Button>
                                        </Link>
                                    </div>
                                    <div>Ödül Bilgileri</div>
                                    <div className="flex-end">
                                        <Button type="submit" color="primary">
                                            Kaydet
                                        </Button>
                                    </div>
                                </CardHeader>

                                <Divider />

                                <CardBody>
                                    <div className="flex">
                                        <div className="relative w-1/2" style={{
                                            minWidth: 400
                                        }}>
                                            <Accordion defaultExpandedKeys={["0"]}>
                                                <AccordionItem
                                                    key="0"
                                                    aria-label="Ürün Resim"
                                                    title="Ürün Resim"
                                                >
                                                    <label className="block cursor-pointer">
                                                        {imageLoading ? (
                                                            <CircularProgress
                                                                size={48}
                                                                style={{
                                                                    position: 'absolute',
                                                                    top: '40%',
                                                                    left: '40%',
                                                                    transform: 'translate(-40%, -40%)',
                                                                    zIndex: 999,
                                                                }}
                                                            />
                                                        ) : selectedImage ? (
                                                            <img
                                                                alt="Selected cover"
                                                                className="object-cover"
                                                                width={400}
                                                                src={selectedImage}
                                                                height="100%"
                                                            />
                                                        ) : (
                                                            <Image
                                                                width={400}
                                                                height={400}
                                                                radius="none"
                                                                loading="eager"
                                                                style={{
                                                                    height: "100%"
                                                                }}
                                                                src={`https://api.hollystone.com.tr/resources/images/${item.prizeImage}`}
                                                                fallbackSrc="https://via.placeholder.com/400x400.png?text="
                                                            />
                                                        )}
                                                        <Input
                                                            type="file"
                                                            label="Resim"
                                                            name="image"
                                                            className="hidden"
                                                            onChange={handleImageChange}
                                                        />
                                                    </label>
                                                </AccordionItem>
                                                <AccordionItem
                                                    key="1"
                                                    aria-label="Sponsor Resim"
                                                    title="Sponsor Resim"
                                                >
                                                    <label className="block cursor-pointer">
                                                        {imageLoading ? (
                                                            <CircularProgress
                                                                size={48}
                                                                style={{
                                                                    position: 'absolute',
                                                                    top: '40%',
                                                                    left: '40%',
                                                                    transform: 'translate(-40%, -40%)',
                                                                    zIndex: 999,
                                                                }}
                                                            />
                                                        ) : selectedImage2 ? (
                                                            <img
                                                                alt="Selected cover"
                                                                className="object-cover"
                                                                width={400}
                                                                src={selectedImage2}
                                                                height="100%"
                                                            />
                                                        ) : (
                                                            <Image
                                                                width={400}
                                                                height={400}
                                                                radius="none"
                                                                loading="eager"
                                                                style={{
                                                                    height: "100%"
                                                                }}
                                                                src={`https://api.hollystone.com.tr/resources/images/${item.prizeSponsorImage}`}
                                                                fallbackSrc="https://via.placeholder.com/400x400.png?text="
                                                            />
                                                        )}
                                                        <Input
                                                            type="file"
                                                            label="Resim"
                                                            name="image"
                                                            className="hidden"
                                                            onChange={handleImageChange2}
                                                        />
                                                    </label>
                                                </AccordionItem>
                                            </Accordion>
                                        </div>
                                        <div className="p-4 w-full">
                                            <Select
                                                label="Ödül Tipi"
                                                placeholder="Bir seçim yapın..."
                                                defaultSelectedKeys={[item.prizeType]}
                                                onChange={(e: any) => setItem({ ...item, prizeType: e.target.value })}
                                                className="mb-2"
                                            >
                                                {Object.keys(dropdownItems).map((key) => (
                                                    <SelectItem key={key} value={key}>
                                                        {dropdownItems[key]}
                                                    </SelectItem>
                                                ))}
                                            </Select>

                                            {
                                                item.prizeType === 'ticket' ? (
                                                    <Select
                                                        label="Ödül"
                                                        placeholder="Bir seçim yapın..."
                                                        defaultSelectedKeys={[item.prize]}
                                                        onChange={(e: any) => setItem({ ...item, prize: parseInt(e.target.value) })}
                                                    >
                                                        {Object.keys(dropdownConcerts).map((key) => (
                                                            <SelectItem key={dropdownConcerts[key].id} value={key}>
                                                                {dropdownConcerts[key].name}
                                                            </SelectItem>
                                                        ))}
                                                    </Select>
                                                )
                                                    : (
                                                        <Input
                                                            type="text"
                                                            label="Ödül"
                                                            name="prize"
                                                            value={item.prize}
                                                            onChange={(e: any) => setItem({ ...item, prize: e.target.value })}
                                                            className="mb-2"
                                                        />
                                                    )
                                            }
                                            <div className="w-full mb-2 flex flex-row">
                                                <Input
                                                    type="date"
                                                    label="Başlangıç Tarihi"
                                                    name="startDate"
                                                    className="mr-2"
                                                    value={item.startDate ? item.startDate.toISOString().split('T')[0] : ''}
                                                    onChange={(e: React.ChangeEvent<HTMLInputElement>) => {
                                                        const selectedDate = new Date(e.target.value + 'T00:00:00Z');
                                                        setItem({ ...item, startDate: selectedDate });
                                                    }}
                                                />
                                                <Input
                                                    type="date"
                                                    label="Bitiş Tarihi"
                                                    name="endDate"
                                                    value={item.endDate ? item.endDate.toISOString().split('T')[0] : ''}
                                                    onChange={(e: React.ChangeEvent<HTMLInputElement>) => {
                                                        const selectedDate = new Date(e.target.value + 'T00:00:00Z');
                                                        setItem({ ...item, endDate: selectedDate });
                                                    }}
                                                />
                                            </div>
                                        </div>
                                    </div>
                                </CardBody>
                            </form>
                        </Card>
                    </Grid>
                </Grid>
            </Container>
            <Footer />
        </>
    );
};

NewWeeklyShamanPage.getLayout = (page: React.ReactNode) => <SidebarLayout>{page}</SidebarLayout>;

export const getServerSideProps = authMiddleware(async (_context: any) => {
    return {
        props: {},
    };
});

export default NewWeeklyShamanPage;
