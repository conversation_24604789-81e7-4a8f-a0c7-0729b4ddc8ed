import React, { useState, useMemo, useCallback, useEffect } from "react";
import Head from 'next/head';
import { authMiddleware } from '../../middleware';
import { getCookie } from 'cookies-next';
import SidebarLayout from '@/layouts/SidebarLayout';
import PageTitleWrapper from '@/components/PageTitleWrapper';
import { Container, Grid, Typography } from '@mui/material';
import {
    Image,
    Card,
    CardBody,
    CardHeader,
    Divider,
    Table,
    TableHeader,
    TableColumn,
    TableBody,
    TableRow,
    TableCell,
    Input,
    DropdownTrigger,
    Dropdown,
    DropdownMenu,
    DropdownItem,
    Button,
    Pagination,
    Spinner,
    getKeyValue,
    User,
} from "@nextui-org/react";
import { Select, SelectItem } from "@nextui-org/select";
import { SearchIcon } from "../../src/components/SearchIcon";
import { PlusIcon } from "../../src/components/PlusIcon";
import { VerticalDotsIcon } from "../../src/components/VerticalDotsIcon";
import Footer from '@/components/Footer';
import { useRouter } from 'next/router';
import Swal from 'sweetalert2';
import ConfirmationNumberIcon from '@mui/icons-material/ConfirmationNumber';
import CalendarMonthIcon from '@mui/icons-material/CalendarMonth';
import HighlightOffIcon from '@mui/icons-material/HighlightOff';
import PointOfSaleOutlinedIcon from '@mui/icons-material/PointOfSaleOutlined';
import DriveFileRenameOutlineIcon from '@mui/icons-material/DriveFileRenameOutline';
import BusinessIcon from '@mui/icons-material/Business';
import dayjs from "dayjs";
import ArrowBackIcon from '@mui/icons-material/ArrowBack';
import Link from 'next/link';

const TicketsListPage = () => {
    const token = getCookie("token")
    const router = useRouter();
    const { id } = router.query;
    const [concert, setConcert]: any = useState({});
    const [totalSum, setTotalSum] = useState(0);
    const [totalRefund, setTotalRefund] = useState(0);
    const [totalRefundCount, setTotalRefundCount] = useState(0);
    const [totalRevenue, setTotalRevenue] = useState(0);
    const [totalRevenueCount, setTotalRevenueCount] = useState(0);
    const [vendor, setVendor] = useState(0);
    const [filterValue, setFilterValue] = useState("");
    const [page, setPage] = useState(1);
    const [total, setTotal] = useState(0);
    const [invoiceStatus, setInvoiceStatus] = useState(false);
    const [isLoading, setIsLoading] = useState(true);
    const [loading, setLoading] = useState(false);
    const [items, setItems]: any = useState([]);
    const [status, setStatus] = useState("1");
    const [selectedKeys, setSelectedKeys] = React.useState(new Set([]));

    const rowsPerPage = 10;

    const loadList = async (pageInput: number, filter: string) => {
        let url = `/api/upcoming-tickets/list?page=${pageInput}&pageSize=${rowsPerPage}&filter=${filter}&id=${id}&status=${status}`;

        try {
            const res = await fetch(url);

            if (res.status === 200) {
                const json = await res.json();
                setTotal(json.totalCount);
                setInvoiceStatus(json.invoiceStatus);
                setTotalSum(json.totalSum);
                setTotalRefundCount(json.totalRefundCount);
                setTotalRefund(json.totalRefund);
                setTotalRevenue(json.totalRevenue);
                setTotalRevenueCount(json.totalRevenueCount);
                setVendor(json.vendor);
                setIsLoading(false);
                setItems(json.results);
            } else {
                setIsLoading(false);
                setItems([]);
            }
        } catch (error) {
            setIsLoading(false);
            setItems([]);
            console.error(error);
            throw error;
        }
    };

    const loadConcert = async () => {
        const url = `/api/concerts?id=${id}`;

        try {
            const res = await fetch(url);

            if (res.status === 200) {
                const json = await res.json();
                setConcert(json.results[0]);
            } else {
                Swal.fire({
                    icon: 'error',
                    title: 'Hata',
                    text: 'Bir sorun meydana geldi!',
                });
            }
        } catch (error) {
            console.error(error);
        }
    };

    const handleRefund = async (rowId) => {
        let question = 'Bu bileti iade etmek istediğine gerçekten emin misin?';
        if (rowId === -1) {
            question = 'Şeçili bilet(ler)i iade etmek istediğine gerçekten emin misin?';
        }
        let selectedKeysArray = Array.from(selectedKeys);
        if (selectedKeys?.toString() == "all") {
            selectedKeysArray = items.map(item => item.id);
        }
        const shouldDelete = await Swal.fire({
            title: 'Dikkat!',
            text: question,
            icon: 'warning',
            showCancelButton: true,
            confirmButtonText: 'Evet, eminim!',
            cancelButtonText: 'Vazgeç',
        });

        if (shouldDelete.isConfirmed) {
            setLoading(true);
            try {
                const response = await fetch('https://api.hollystone.com.tr/api/payment/refund-ticket', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'Authorization': `Bearer ${token}`
                    },
                    body: JSON.stringify({
                        ticketId: rowId,
                        tickets: selectedKeysArray
                    })
                });

                if (response.status === 200) {
                    const json = await response.json();
                    if (json.type == "success") {
                        loadList(page, filterValue);
                        Swal.fire({
                            icon: 'success',
                            title: 'Başarılı',
                            text: 'Bilet başarılı bir şekilde iade edildi',
                        });
                    } else {
                        Swal.fire({
                            icon: 'error',
                            title: 'Hata',
                            text: json.error,
                        });
                    }
                    setLoading(false);
                } else {
                    Swal.fire({
                        icon: 'error',
                        title: 'Hata',
                        text: 'Bir sorun meydana geldi!',
                    });
                    setLoading(false);
                }
            } catch (error) {
                Swal.fire({
                    icon: 'error',
                    title: 'Hata',
                    text: 'Bir sorun meydana geldi!',
                });
                setLoading(false);
            }
        }
    };

    const handleInvoice = async () => {
        const shouldDelete = await Swal.fire({
            title: 'Dikkat!',
            text: `${concert.name} konserine ait olan biletleri ${dayjs(new Date()).format("DD.MM.YYYY")} tarihinde faturalandırmak istediğine emin misin?`,
            icon: 'warning',
            showCancelButton: true,
            confirmButtonText: 'Evet, eminim!',
            cancelButtonText: 'Vazgeç',
        });

        if (shouldDelete.isConfirmed) {
            setLoading(true);
            try {
                const response = await fetch('/api/upcoming-tickets/invoice', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        id
                    })
                });

                if (response.status === 200) {
                    loadList(page, filterValue);
                    Swal.fire({
                        icon: 'success',
                        title: 'Başarılı',
                        text: 'Biletler başarılı bir şekilde fatura edildi',
                    });
                    setLoading(false);
                } else {
                    Swal.fire({
                        icon: 'error',
                        title: 'Hata',
                        text: 'Bir sorun meydana geldi!',
                    });
                    setLoading(false);
                }
            } catch (error) {
                Swal.fire({
                    icon: 'error',
                    title: 'Hata',
                    text: 'Bir sorun meydana geldi!',
                });
                setLoading(false);
            }
        }
    };

    const handleNew = () => {
        router.push(`/upcoming-tickets/new?id=${id}`);
    };

    const sendMail = async (id) => {
        try {
            const res = await fetch('https://api.hollystone.com.tr/api/send-mail', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'Authorization': `Bearer ${localStorage.getItem('token')}`
                },
                body: JSON.stringify({
                    id
                })
            });

            if (res.status === 200) {
                const data = await res.json();
                if (data.type == "success") {
                    Swal.fire({
                        icon: 'success',
                        title: 'Başarılı',
                        text: 'Mail gönderildi!',
                    })
                } else {
                    Swal.fire({
                        icon: 'error',
                        title: 'Hata',
                        text: data.error,
                    });
                }
            } else {
                Swal.fire({
                    icon: 'error',
                    title: 'Hata',
                    text: 'Bir sorun meydana geldi!',
                });
            }
        } catch (error) {
            console.error(error);
        }
    }

    const sendSms = async (id) => {
        try {
            const res = await fetch('https://api.hollystone.com.tr/api/send-sms', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'Authorization': `Bearer ${localStorage.getItem('token')}`
                },
                body: JSON.stringify({
                    id
                })
            });

            if (res.status === 200) {
                const data = await res.json();
                if (data.type == "success") {
                    Swal.fire({
                        icon: 'success',
                        title: 'Başarılı',
                        text: 'SMS gönderildi!',
                    })
                } else {
                    Swal.fire({
                        icon: 'error',
                        title: 'Hata',
                        text: data.error,
                    });
                }
            } else {
                Swal.fire({
                    icon: 'error',
                    title: 'Hata',
                    text: 'Bir sorun meydana geldi!',
                });
            }
        } catch (error) {
            console.error(error);
        }
    }

    useEffect(() => {
        loadList(1, filterValue);
        loadConcert();
    }, []);

    useEffect(() => {
        loadList(page, filterValue);
    }, [status]);

    const pages = Math.ceil(total / rowsPerPage);

    const onPaginationChange = useCallback(
        async (pageInput) => {
            setIsLoading(true);
            setPage(pageInput);
            await loadList(pageInput, filterValue);
        },
        []
    );

    const onSearchChange = useCallback(
        (value: string) => {
            if (value) {
                setIsLoading(true);
                setFilterValue(value);
                loadList(page, value);
                setPage(1);
            } else {
                setFilterValue("");
                loadList(page, "");
            }
        },
        []
    );

    const onClear = useCallback(() => {
        setFilterValue("");
        setPage(1);
    }, []);

    const topContent = useMemo(() => {
        return (
            <div className="flex flex-col gap-4">
                <div className="flex justify-between gap-3 items-end">
                    <Input
                        isClearable
                        className="w-full sm:max-w-[44%]"
                        placeholder="Arayın..."
                        startContent={<SearchIcon />}
                        value={filterValue}
                        onClear={() => onClear()}
                        onValueChange={onSearchChange}
                    />
                    <div className="flex gap-3">
                        <Select
                            placeholder="Bir seçim yapın..."
                            labelPlacement="outside"
                            defaultSelectedKeys={[status]}
                            onSelectionChange={(e: any) => {
                                setStatus(e.currentKey);
                            }}
                            style={{
                                minWidth: "150px"
                            }}
                            className="mb-2 h-5"
                        >
                            <SelectItem key="1" value="1">Aktif Biletler</SelectItem>
                            <SelectItem key="2" value="2">Kullanılan Biletler</SelectItem>
                            <SelectItem key="3" value="3">İptal Edilenler</SelectItem>
                            <SelectItem key="4" value="4">İade Edilenler</SelectItem>
                            <SelectItem key="0" value="0">Başarısız İşlemler</SelectItem>
                        </Select>
                        {
                            selectedKeys.size === 0 ?
                                (
                                    <Button color="primary" onClick={handleNew} endContent={<PlusIcon width={undefined} height={undefined} />}>
                                        Yeni
                                    </Button>
                                )
                                :
                                (
                                    <Button color="danger" onClick={() => { handleRefund(-1) }}>
                                        İade Et
                                    </Button>
                                )
                        }

                    </div>
                </div>
            </div>
        );
    }, [
        filterValue,
        onSearchChange,
        onClear,
        selectedKeys
    ]);

    const renderCell = useCallback((cellValue: any, columnKey: any, rowId: any, item: any) => {
        const user = item.user;
        switch (columnKey) {
            case "qrCode":
                return (
                    <div className="flex gap-2">
                        <a target="_blank" rel="noreferrer" href={`https://api.qrserver.com/v1/create-qr-code/?data=${cellValue}`}>
                            <User name=""
                                avatarProps={{ radius: "none", src: `https://api.qrserver.com/v1/create-qr-code/?data=${cellValue}` }}
                            >
                            </User>
                        </a>
                        {
                            item.boundedQr ? (
                                <a target="_blank" rel="noreferrer" href={`https://api.qrserver.com/v1/create-qr-code/?data=${item.boundedQr}`}>
                                    <User name=""
                                        avatarProps={{ radius: "none", src: `https://api.qrserver.com/v1/create-qr-code/?data=${item.boundedQr}` }}
                                    >
                                    </User>
                                </a>
                            ) : ""
                        }
                    </div>
                );
            case "ticket":
                return (
                    cellValue.title
                );
            case "user.name":
                return (
                    `${user.firstName} ${user.lastName}`
                );
            case "user.email":
                return (
                    user.email
                );
            case "user.phoneNumber":
                return (
                    user.phoneNumber
                );
            case "actions":
                return (
                    <div className="relative flex justify-end items-center gap-2">
                        <Dropdown>
                            <DropdownTrigger>
                                <Button isIconOnly size="sm" variant="light">
                                    <VerticalDotsIcon className="text-default-300" width={undefined} height={undefined} />
                                </Button>
                            </DropdownTrigger>
                            <DropdownMenu>
                                <DropdownItem onClick={() => sendSms(rowId)}>SMS Gönder</DropdownItem>
                                <DropdownItem onClick={() => sendMail(rowId)}>Mail Gönder</DropdownItem>
                                {
                                    item.status !== 3 && (
                                        <DropdownItem onClick={() => handleRefund(rowId)}>İade Et</DropdownItem>
                                    )
                                }
                                {
                                    item.status !== 4 && (
                                        <DropdownItem onClick={() => handleRefund(rowId)}>İptal Et</DropdownItem>
                                    )
                                }
                                {
                                    [2, 3, 4].includes(item.status) && (
                                        <DropdownItem onClick={() => handleRefund(rowId)}>Kullanıma Aç</DropdownItem>
                                    )
                                }
                            </DropdownMenu>
                        </Dropdown>
                    </div>
                );
            default:
                return cellValue;
        }
    }, []);

    return (
        <>
            <Head>
                <title>Biletler</title>
            </Head>
            <PageTitleWrapper>
                <Grid container alignItems="center">
                    <Grid item>
                        <Typography variant="h3" component="h3" gutterBottom>
                            Bilet Listesi
                        </Typography>
                    </Grid>
                </Grid>
            </PageTitleWrapper>
            <Container maxWidth="lg">
                <Grid
                    container
                    direction="row"
                    justifyContent="center"
                    alignItems="stretch"
                    spacing={4}
                >
                    <Grid item xs={12}>
                        <Card
                            isBlurred
                            className="border-none bg-white dark:bg-default-100/50 max-w-[610px]"
                            shadow="sm"
                        >
                            <CardHeader className="flex justify-between gap-3">
                                <div>
                                    <Link href="/upcoming-tickets">
                                        <Button type="button" color="default" startContent={<ArrowBackIcon />}>
                                            Konserler
                                        </Button>
                                    </Link>
                                </div>
                                <div>Konser Bilgileri</div>
                                <div className="flex-end">
                                    <Button type="button" onClick={handleInvoice} color={invoiceStatus ? "default" : "primary"} isDisabled={invoiceStatus ? true : false}>
                                        Faturalandır
                                    </Button>
                                </div>
                            </CardHeader>

                            <Divider />

                            <CardBody>
                                <div className="flex">
                                    <div className="relative w-1/2">
                                        <Image
                                            width={400}
                                            height={400}
                                            radius="none"
                                            loading="eager"
                                            style={{
                                                height: "100%"
                                            }}
                                            src={`https://api.hollystone.com.tr/resources/images/${concert.image}`}
                                            fallbackSrc="https://via.placeholder.com/400x400.png?text="
                                        />
                                    </div>

                                    <div className="px-4 w-full">
                                        <div className="w-full mb-2 flex flex-row justify-between">
                                            <Card shadow="sm" className="mr-2" fullWidth>
                                                <CardBody className="overflow-visible p-2">
                                                    <div className="flex">
                                                        <div className="relative w-1/2 flex items-center p-4 rounded-md bg-warning">
                                                            <ConfirmationNumberIcon style={{ color: "white" }} />
                                                        </div>
                                                        <div className="p-4 w-full">
                                                            <b>Satış Tutar</b>
                                                            <p>{totalSum ?? 0} ₺</p>
                                                        </div>
                                                    </div>
                                                </CardBody>
                                            </Card>
                                            <Card shadow="sm" className="mr-2" fullWidth>
                                                <CardBody className="overflow-visible p-2">
                                                    <div className="flex">
                                                        <div className="relative w-1/2 flex items-center p-4 rounded-md bg-danger">
                                                            <HighlightOffIcon style={{ color: "white" }} />
                                                        </div>
                                                        <div className="p-4 w-full">
                                                            <b>İptal Tutar</b>
                                                            <p>{totalRefund ?? 0} ₺</p>
                                                        </div>
                                                    </div>
                                                </CardBody>
                                            </Card>
                                            <Card shadow="sm" fullWidth>
                                                <CardBody className="overflow-visible p-2">
                                                    <div className="flex">
                                                        <div className="relative w-1/2 flex items-center p-4 rounded-md bg-success">
                                                            <PointOfSaleOutlinedIcon style={{ color: "white" }} />
                                                        </div>
                                                        <div className="p-4 w-full">
                                                            <b>Toplam Gelir</b>
                                                            <p>{totalRevenue ?? 0} ₺</p>
                                                        </div>
                                                    </div>
                                                </CardBody>
                                            </Card>
                                        </div>
                                        <div className="w-full mb-2 flex flex-row justify-between">
                                            <Card shadow="sm" className="mr-2" fullWidth>
                                                <CardBody className="overflow-visible p-2">
                                                    <div className="flex">
                                                        <div className="relative w-1/2 flex items-center p-4 rounded-md bg-warning">
                                                            <ConfirmationNumberIcon style={{ color: "white" }} />
                                                        </div>
                                                        <div className="p-4 w-full">
                                                            <b>Toplam Satış</b>
                                                            <p>{totalRevenueCount}</p>
                                                        </div>
                                                    </div>
                                                </CardBody>
                                            </Card>
                                            <Card shadow="sm" className="mr-2" fullWidth>
                                                <CardBody className="overflow-visible p-2">
                                                    <div className="flex">
                                                        <div className="relative w-1/2 flex items-center p-4 rounded-md bg-danger">
                                                            <HighlightOffIcon style={{ color: "white" }} />
                                                        </div>
                                                        <div className="p-4 w-full">
                                                            <b>Toplam İptal</b>
                                                            <p>{totalRefundCount}</p>
                                                        </div>
                                                    </div>
                                                </CardBody>
                                            </Card>
                                            <Card shadow="sm" fullWidth>
                                                <CardBody className="overflow-visible p-2">
                                                    <div className="flex">
                                                        <div className="relative w-1/2 flex items-center p-4 rounded-md bg-primary">
                                                            <CalendarMonthIcon style={{ color: "white" }} />
                                                        </div>
                                                        <div className="p-4 w-full">
                                                            <b>Etkinlik Tarihi</b>
                                                            <p>{dayjs(concert.date).format("DD.MM.YYYY")}</p>
                                                        </div>
                                                    </div>
                                                </CardBody>
                                            </Card>
                                        </div>
                                        <div className="w-full mb-2 flex flex-row justify-between">
                                            <Card shadow="sm" className="mr-2" fullWidth>
                                                <CardBody className="overflow-visible p-2">
                                                    <div className="flex">
                                                        <div className="relative w-1/2 flex items-center p-4 rounded-md bg-primary">
                                                            <DriveFileRenameOutlineIcon style={{ color: "white" }} />
                                                        </div>
                                                        <div className="p-4 w-full">
                                                            <b>Etkinlik Adı</b>
                                                            <p>{concert.name}</p>
                                                        </div>
                                                    </div>
                                                </CardBody>
                                            </Card>
                                            <Card shadow="sm" fullWidth>
                                                <CardBody className="overflow-visible p-2">
                                                    <div className="flex">
                                                        <div className="relative w-1/2 flex items-center p-4 rounded-md bg-primary">
                                                            <BusinessIcon style={{ color: "white" }} />
                                                        </div>
                                                        <div className="p-4 w-full">
                                                            <b>İşletme</b>
                                                            <p>{vendor}</p>
                                                        </div>
                                                    </div>
                                                </CardBody>
                                            </Card>
                                        </div>
                                    </div>
                                </div>
                            </CardBody>
                        </Card>
                    </Grid>
                    <Grid item xs={12}>
                        {
                            loading ? <Spinner /> : (
                                <Table
                                    aria-label="Example table with client async pagination"
                                    topContent={topContent}
                                    color="primary"
                                    selectionMode="multiple"
                                    selectedKeys={selectedKeys}
                                    onSelectionChange={() => { setSelectedKeys }}
                                    topContentPlacement="inside"
                                    bottomContent={
                                        pages > 0 ? (
                                            <div className="flex w-full justify-center">
                                                <Pagination
                                                    isCompact
                                                    showControls
                                                    showShadow
                                                    color="primary"
                                                    page={page}
                                                    total={pages}
                                                    onChange={onPaginationChange}
                                                />
                                            </div>
                                        ) : null
                                    }
                                    classNames={{
                                        table: "min-h-[400px]",
                                    }}
                                >
                                    <TableHeader>
                                        <TableColumn key="qrCode">QR Kod</TableColumn>
                                        <TableColumn key="user.name">Kullanıcı</TableColumn>
                                        <TableColumn key="user.email">E-mail</TableColumn>
                                        <TableColumn key="user.phoneNumber">Telefon</TableColumn>
                                        <TableColumn key="ticket">Bilet</TableColumn>
                                        <TableColumn key="paidPrice">Fiyat</TableColumn>
                                        <TableColumn key="actions">İşlemler</TableColumn>
                                    </TableHeader>
                                    <TableBody
                                        isLoading={isLoading && !items.length}
                                        items={items}
                                        loadingContent={<Spinner />}
                                        emptyContent={"Kayıt bulunamadı!"}
                                    >
                                        {(item: any) => (
                                            <TableRow key={item.id}>
                                                {(columnKey) => (
                                                    <TableCell key={columnKey + item.id}>{renderCell(getKeyValue(item, columnKey), columnKey, item.id, item)}</TableCell>
                                                )}
                                            </TableRow>
                                        )}
                                    </TableBody>
                                </Table>
                            )
                        }

                    </Grid>
                </Grid>
            </Container>
            <Footer />
        </>
    );
}

TicketsListPage.getLayout = (page: any) => <SidebarLayout>{page}</SidebarLayout>;

export const getServerSideProps = authMiddleware(async (_context: any) => {
    // Your page-specific logic...
    return {
        props: {}, // You can add any props you need for the page
    };
});

export default TicketsListPage
