import React, { useState, useEffect } from 'react';
import Head from 'next/head';
import { authMiddleware } from '../../middleware';
import SidebarLayout from '@/layouts/SidebarLayout';
import PageTitleWrapper from '@/components/PageTitleWrapper';
import { Typography, Container, Grid, CircularProgress } from '@mui/material';
import { Image, Card, CardBody, CardHeader, Button, Input, Textarea, Divider, Accordion, AccordionItem } from '@nextui-org/react';
import { Select, SelectItem } from "@nextui-org/select";
import Footer from '@/components/Footer';
import { useRouter } from 'next/router';
import Swal from 'sweetalert2';
import DeleteForeverIcon from '@mui/icons-material/DeleteForever';
import ArrowBackIcon from '@mui/icons-material/ArrowBack';
import Link from 'next/link';
import AddTicketModal from './AddTicketModal';

const ConcertEditPage = () => {
    const router = useRouter();
    const { id } = router.query;

    const [item, setItem]: any = useState({});
    const [stages, setStages] = useState([]);
    const [vendors, setVendors] = useState([]);
    const [loading, setLoading] = useState(true);
    const [currentTimestamp, setCurrentTimestamp] = useState(new Date().getTime());
    const [imageLoading, setImageLoading] = useState(false);
    const [tickets, setTickets] = useState([]);
    const [isModalVisible, setModalVisible] = useState(false);


    const handleAddTicket = async (newTicket) => {
        newTicket.concertId = parseInt(Array.isArray(id) ? id[0] : id);
    
        try {
            const response = await fetch(`/api/concert-tickets/insert`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify(newTicket),
            });
    
            if (response.ok) {
                Swal.fire({
                    icon: 'success',
                    title: 'Başarılı',
                    text: 'Bilet eklendi!',
                });
                setTickets([...tickets, newTicket]);
            } else {
                Swal.fire({
                    icon: 'error',
                    title: 'Hata',
                    text: 'Bilet eklenirken bir sorun meydana geldi!',
                });
            }
        } catch (error) {
            Swal.fire({
                icon: 'error',
                title: 'Hata',
                text: 'Bir sorun meydana geldi!',
            });
        }
    };


    const loadList = async () => {
        const url = `/api/concerts?id=${id}`;

        try {
            const res = await fetch(url);

            if (res.status === 200) {
                const json = await res.json();
                json.results[0].gateDate = new Date(json.results[0]?.gateDate);
                json.results[0].date = new Date(json.results[0]?.date);
                setItem(json.results[0]);
            } else {
                Swal.fire({
                    icon: 'error',
                    title: 'Hata',
                    text: 'Bir sorun meydana geldi!',
                });
            }
        } catch (error) {
            console.error(error);
        } finally {
            setLoading(false);
        }
    };

    const loadTickets = async () => {
        const url = `/api/concert-tickets?id=${id}`;

        try {
            const res = await fetch(url);

            if (res.status === 200) {
                const json = await res.json();
                setTickets(json.results);
            } else {
                Swal.fire({
                    icon: 'error',
                    title: 'Hata',
                    text: 'Bir sorun meydana geldi!',
                });
            }
        } catch (error) {
            console.error(error);
        } finally {
            setLoading(false);
        }
    };

    const loadStages = async () => {
        const url = `/api/stages`;

        try {
            const res = await fetch(url);

            if (res.status === 200) {
                const json = await res.json();
                setStages(json.results);
            } else {
                Swal.fire({
                    icon: 'error',
                    title: 'Hata',
                    text: 'Bir sorun meydana geldi!',
                });
            }
        } catch (error) {
            console.error(error);
        } finally {
            setLoading(false);
        }
    };

    const loadVendors = async () => {
        const url = `/api/vendors`;

        try {
            const res = await fetch(url);

            if (res.status === 200) {
                const json = await res.json();
                setVendors(json.results);
            } else {
                Swal.fire({
                    icon: 'error',
                    title: 'Hata',
                    text: 'Bir sorun meydana geldi!',
                });
            }
        } catch (error) {
            console.error(error);
        } finally {
            setLoading(false);
        }
    };

    const loadContents = async () => {
        setLoading(true);
        await loadList();
        await loadTickets();
        await loadStages();
        await loadVendors();
        setLoading(false);
    }

    useEffect(() => {
        loadContents();
    }, []);

    const handleImageChange = async (event) => {
        const file = event.target.files[0];

        if (file) {
            if (!file.type.startsWith('image/')) {
                Swal.fire({
                    icon: 'error',
                    title: 'Hata',
                    text: 'Seçilen format desteklenmiyor!',
                });
                return;
            }
            setImageLoading(true);
            const formData = new FormData();
            formData.append('image', file);
            formData.append('fileName', `concerts/${item.id}`);

            try {
                const response = await fetch(`https://api.hollystone.com.tr/api/functions/upload`, {
                    method: 'POST',
                    body: formData,
                });

                if (response.ok) {
                    const data = await response.json();

                    if (data.type == "success") {
                        Swal.fire({
                            icon: 'success',
                            title: 'Başarılı',
                            text: 'Resim güncellendi!',
                        });
                        setCurrentTimestamp(new Date().getTime());
                    } else {
                        Swal.fire({
                            icon: 'error',
                            title: 'Hata',
                            text: data.error,
                        });
                    }
                } else {
                    Swal.fire({
                        icon: 'error',
                        title: 'Hata',
                        text: 'Bir sorun meydana geldi!',
                    });
                }
            } catch (error) {
                Swal.fire({
                    icon: 'error',
                    title: 'Hata',
                    text: 'Bir sorun meydana geldi!',
                });
            }
            setImageLoading(false);
        }
    };

    const handleDelete = async () => {
        const shouldDelete = await Swal.fire({
            title: 'Dikkat!',
            text: 'Bu kaydı silmek istediğine gerçekten emin misin?',
            icon: 'warning',
            showCancelButton: true,
            confirmButtonText: 'Evet, sil!',
            cancelButtonText: 'Vazgeç',
        });

        if (shouldDelete.isConfirmed) {
            try {
                const response = await fetch(`/api/concerts/delete`, {
                    method: 'DELETE',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({ id: id }),
                });

                if (response.ok) {
                    Swal.fire({
                        icon: 'success',
                        title: 'Başarılı',
                        text: 'Kayıt silindi!',
                    });
                    router.push('/concerts');
                } else {
                    Swal.fire({
                        icon: 'error',
                        title: 'Hata',
                        text: 'Bir sorun meydana geldi!',
                    });
                }
            } catch (error) {
                Swal.fire({
                    icon: 'error',
                    title: 'Hata',
                    text: 'Bir sorun meydana geldi!',
                });
            }
        }
    };

    const handleUpdate = async (event) => {
        event.preventDefault();
        const updatedData = {
            id: id,
            stageId: item.stageId,
            name: item.name,
            description: item.description,
            redirect: item.redirect,
            date: item.date,
            gateDate: item.gateDate
        }
        try {
            const response = await fetch(`/api/concerts/update`, {
                method: 'PUT',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify(updatedData),
            });

            if (response.ok) {
                await loadList();
                Swal.fire({
                    icon: 'success',
                    title: 'Başarılı',
                    text: 'Kayıt güncellendi!',
                });
            } else {
                Swal.fire({
                    icon: 'error',
                    title: 'Hata',
                    text: 'Bir sorun meydana geldi!',
                });
            }
        } catch (error) {
            Swal.fire({
                icon: 'error',
                title: 'Hata',
                text: 'Bir sorun meydana geldi!',
            });
        }
    }

    const handleDeleteTicket = async (key) => {
        const shouldDelete = await Swal.fire({
            title: 'Dikkat!',
            text: 'Bu bileti silmek istediğine gerçekten emin misin?',
            icon: 'warning',
            showCancelButton: true,
            confirmButtonText: 'Evet, sil!',
            cancelButtonText: 'Vazgeç',
        });

        if (shouldDelete.isConfirmed) {
            const updatedTickets = [...tickets];

            const indexToDelete = updatedTickets.findIndex(ticket => ticket.id === key);
            if (indexToDelete !== -1) {
                updatedTickets.splice(indexToDelete, 1);
                try {
                    const response = await fetch(`/api/concert-tickets/delete`, {
                        method: 'DELETE',
                        headers: {
                            'Content-Type': 'application/json',
                        },
                        body: JSON.stringify({ id: key }),
                    });

                    if (response.ok) {
                        Swal.fire({
                            icon: 'success',
                            title: 'Başarılı',
                            text: 'Bilet silindi!',
                        });
                        setTickets(updatedTickets);
                    } else {
                        Swal.fire({
                            icon: 'error',
                            title: 'Hata',
                            text: 'Bir sorun meydana geldi!',
                        });
                    }
                } catch (error) {
                    Swal.fire({
                        icon: 'error',
                        title: 'Hata',
                        text: 'Bir sorun meydana geldi!',
                    });
                }
            } else {
                Swal.fire({
                    icon: 'error',
                    title: 'Hata',
                    text: 'Bir sorun meydana geldi!',
                });
            }
        }
    }

    const handleUpdateTicket = async (key) => {
        try {
            const response = await fetch(`/api/concert-tickets/update`, {
                method: 'PUT',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify(tickets[key]),
            });

            if (response.ok) {
                Swal.fire({
                    icon: 'success',
                    title: 'Başarılı',
                    text: 'Bilet güncellendi!',
                });
            } else {
                Swal.fire({
                    icon: 'error',
                    title: 'Hata',
                    text: 'Bir sorun meydana geldi!',
                });
            }
        } catch (error) {
            Swal.fire({
                icon: 'error',
                title: 'Hata',
                text: 'Bir sorun meydana geldi!',
            });
        }
    }

    const handleNewTicket = async () => {
        const newTicket = {
            concertId: parseInt(Array.isArray(id) ? id[0] : id),
            title: '',
            hollyPoints: 0,
            price: '',
            quota: 0,
            type: false
        };
        const newTickets = [...tickets, newTicket];
        try {
            const response = await fetch(`/api/concert-tickets/insert`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify(newTicket),
            });

            if (response.ok) {
                Swal.fire({
                    icon: 'success',
                    title: 'Başarılı',
                    text: 'Bilet eklendi!',
                });
                setTickets(newTickets);
            } else {
                Swal.fire({
                    icon: 'error',
                    title: 'Hata',
                    text: 'Bir sorun meydana geldi!',
                });
            }
        } catch (error) {
            Swal.fire({
                icon: 'error',
                title: 'Hata',
                text: 'Bir sorun meydana geldi!',
            });
        }
    }

    const renderContent = () => {
        if (loading) {
            return <p>Loading...</p>;
        }

        return (
            <>
                <Head>
                    <title>Konserler - Düzenle</title>
                </Head>
                <PageTitleWrapper>
                    <Grid container alignItems="center">
                        <Grid item>
                            <Typography variant="h3" component="h3" gutterBottom>
                                Konser Düzenle
                            </Typography>
                        </Grid>
                    </Grid>
                </PageTitleWrapper>
                <Container >
                    <Grid
                        container
                        direction="row"
                        justifyContent="center"
                        alignItems="stretch"
                        spacing={4}
                        style={{
                            marginBottom: 20
                        }}
                    >
                        <Grid item xs={12}>
                            <Card
                                isBlurred
                                className="border-none bg-white dark:bg-default-100/50"
                                shadow="sm"
                            >
                                <form onSubmit={handleUpdate}>
                                    <CardHeader className="flex justify-between gap-3">
                                        <div>
                                            <Link href="/concerts">
                                                <Button type="button" color="default" startContent={<ArrowBackIcon />}>
                                                    Konserler
                                                </Button>
                                            </Link>
                                        </div>
                                        <div>Konser Bilgileri</div>
                                        <div className="flex-end">
                                            <Button type="button" onClick={handleDelete} color="danger" className="mr-2">
                                                Sil
                                            </Button>
                                            <Button type="submit" color="primary">
                                                Güncelle
                                            </Button>
                                        </div>
                                    </CardHeader>

                                    <Divider />

                                    <CardBody>
                                        <div className="flex">
                                            <div className="relative w-1/2">
                                                <label className="block cursor-pointer">
                                                    <Image
                                                        width={400}
                                                        height={400}
                                                        radius="none"
                                                        loading="eager"
                                                        style={{
                                                            height: "100%"
                                                        }}
                                                        src={`https://api.hollystone.com.tr/resources/images/${item.image}?t=${currentTimestamp}`}
                                                        fallbackSrc="https://via.placeholder.com/400x400.png?text="
                                                    />
                                                    {imageLoading ? (
                                                        <CircularProgress
                                                            size={48}
                                                            style={{
                                                                position: 'absolute',
                                                                top: '40%',
                                                                left: '40%',
                                                                transform: 'translate(-40%, -40%)',
                                                                zIndex: 999,
                                                            }}
                                                        />
                                                    ) : null}
                                                    <Input
                                                        type="file"
                                                        label="Resim"
                                                        name="image"
                                                        className="hidden"
                                                        onChange={handleImageChange}
                                                    />
                                                </label>
                                            </div>

                                            <div className="p-4 w-full">
                                                <div className="w-full mb-2 flex flex-row">
                                                    <Input
                                                        type="text"
                                                        label="Başlık"
                                                        name="name"
                                                        value={item.name}
                                                        className="mr-2"
                                                        onChange={(e: any) => setItem({ ...item, name: e.target.value })}
                                                    />
                                                    <Select
                                                        label="Sahne"
                                                        placeholder="Bir seçim yapın..."
                                                        defaultSelectedKeys={[item.stageId?.toString()]}
                                                        className="mr-2"
                                                        onChange={(e: any) => setItem({ ...item, stageId: parseInt(e.target.value) })}
                                                    >
                                                        {stages.map((stage, index) => {
                                                            return (
                                                                <SelectItem key={stage.id} value={index}>
                                                                    {stage.name}
                                                                </SelectItem>
                                                            )
                                                        })}
                                                    </Select>
                                                    <Select
                                                        label="Bayi"
                                                        placeholder="Bir seçim yapın..."
                                                        defaultSelectedKeys={[item.vendorId?.toString()]}
                                                        onChange={(e: any) => setItem({ ...item, vendorId: parseInt(e.target.value) })}
                                                    >
                                                        {vendors.map((vendor, index) => {
                                                            return (
                                                                <SelectItem key={vendor.id} value={index}>
                                                                    {vendor.name}
                                                                </SelectItem>
                                                            )
                                                        })}
                                                    </Select>
                                                </div>
                                                <div className="w-full mb-2 flex flex-row">
                                                    <Input
                                                        type="time"
                                                        label="Kapı Açılış Saati"
                                                        name="gateDate"
                                                        value={item.gateDate?.toISOString().substr(11, 5)}
                                                        className="mr-2"
                                                        onChange={(e: any) => {
                                                            const [hours, minutes] = e.target.value.split(':');
                                                            const newGateDate = new Date(item.gateDate);
                                                            newGateDate.setUTCHours(Number(hours));
                                                            newGateDate.setUTCMinutes(Number(minutes));
                                                            setItem({ ...item, gateDate: newGateDate });
                                                        }}
                                                    />
                                                    <Input
                                                        type="datetime-local"
                                                        label="Konser Tarihi"
                                                        name="date"
                                                        value={item.date?.toISOString().slice(0, 16)}
                                                        onChange={(e: any) => setItem({ ...item, date: new Date(e.target.value + 'Z') })}
                                                    />
                                                </div>
                                                <Textarea
                                                    label="Açıklama"
                                                    name="description"
                                                    value={item.description}
                                                    className="mb-2"
                                                    onChange={(e: any) => setItem({ ...item, description: e.target.value })}
                                                />
                                                <Input
                                                    type="text"
                                                    label="Yönlendirme"
                                                    name="redirect"
                                                    value={item.redirect}
                                                    className="mb-2"
                                                    onChange={(e: any) => setItem({ ...item, redirect: e.target.value })}
                                                />
                                            </div>
                                        </div>
                                    </CardBody>
                                </form>

                            </Card>
                        </Grid>
                    </Grid>
                    <Grid
                        container
                        direction="row"
                        justifyContent="center"
                        alignItems="stretch"
                        spacing={4}
                    >
                        <Grid item xs={12}>
                            <Card
                                isBlurred
                                className="border-none bg-white dark:bg-default-100/50 max-w-[610px]"
                                shadow="sm"
                            >
                                <CardHeader className="flex justify-between gap-3">
                                    <div></div>
                                    <div>Bilet Bilgileri</div>
                                    <div className="flex-end">
                                    <button
        className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700"
        onClick={() => setModalVisible(true)}
    >
        Yeni Bilet Ekle
    </button>
   

                                    </div>
                                </CardHeader>


                                <Divider />

                                <CardBody>
                                    <div className="flex">
                                        <div className="p-4 w-full">
                                            <Accordion defaultExpandedKeys={["0"]}>
                                                {
                                                    loading ? (
                                                        <CircularProgress />
                                                    )
                                                        : (

                                                            tickets.map((ticket, index) => {
                                                                return (
                                                                    <AccordionItem
                                                                        key={index}
                                                                        aria-label={ticket.title}
                                                                        title={ticket.title}
                                                                        startContent={
                                                                            (
                                                                                <div className="w-full mb-2 flex flex-row">
                                                                                    <Button color="danger" onClick={() => { handleDeleteTicket(ticket.id) }} className="min-w-unit-10 mr-2">
                                                                                        <DeleteForeverIcon />
                                                                                    </Button>
                                                                                </div>
                                                                            )
                                                                        }>
                                                                        <div className="w-full mb-2 flex flex-row">
                                                                            <Input
                                                                                type="title"
                                                                                label="Başlık"
                                                                                placeholder="Başlık"
                                                                                name="title"
                                                                                value={ticket.title}
                                                                                className="mr-2"
                                                                                onChange={(e: any) => {
                                                                                    const newTickets = [...tickets];
                                                                                    newTickets[index] = { ...ticket, title: e.target.value };
                                                                                    setTickets(newTickets);
                                                                                }}
                                                                            />
                                                                            <Input
                                                                                type="number"
                                                                                label="Fiyat"
                                                                                placeholder="Fiyat"
                                                                                name="price"
                                                                                min={0}
                                                                                value={ticket.price}
                                                                                className="mr-2"
                                                                                onChange={(e: any) => {
                                                                                    const newTickets = [...tickets];
                                                                                    newTickets[index] = { ...ticket, price: e.target.value };
                                                                                    setTickets(newTickets);
                                                                                }}
                                                                            />
                                                                        </div>
                                                                        <div className="w-full mb-2 flex flex-row">
                                                                            <Input
                                                                                type="number"
                                                                                label="Kontenjan"
                                                                                placeholder="Kontenjan"
                                                                                name="quota"
                                                                                min={0}
                                                                                value={ticket.quota}
                                                                                className="mr-2"
                                                                                onChange={(e: any) => {
                                                                                    const newTickets = [...tickets];
                                                                                    newTickets[index] = { ...ticket, quota: parseInt(e.target.value) };
                                                                                    setTickets(newTickets);
                                                                                }}
                                                                            />
                                                                            <Input
                                                                                type="number"
                                                                                label="Holly Puan"
                                                                                placeholder="Holly Puan"
                                                                                name="hollyPoints"
                                                                                value={ticket.hollyPoints}
                                                                                min={0}
                                                                                className="mr-2"
                                                                                onChange={(e: any) => {
                                                                                    const newTickets = [...tickets];
                                                                                    newTickets[index] = { ...ticket, hollyPoints: parseInt(e.target.value) };
                                                                                    setTickets(newTickets);
                                                                                }}
                                                                            />
                                                                            <Select
                                                                                label="Bilet Türü"
                                                                                placeholder="Bir seçim yapın..."
                                                                                defaultSelectedKeys={[ticket.type?.toString()]}
                                                                                onChange={(e: any) => {
                                                                                    const newTickets = [...tickets];
                                                                                    newTickets[index] = { ...ticket, type: (/true/i).test(e.target.value) };
                                                                                    setTickets(newTickets);
                                                                                }}
                                                                            >
                                                                                <SelectItem key="true" value="true">
                                                                                    1+1
                                                                                </SelectItem>
                                                                                <SelectItem key="false" value="false">
                                                                                    Normal
                                                                                </SelectItem>
                                                                            </Select>
                                                                        </div>
                                                                        <div className="flex justify-between gap-3">
                                                                            <div></div>
                                                                            <div className="flex-end">
                                                                                <Button type="button" color="primary" onClick={() => { handleUpdateTicket(index) }}>
                                                                                    Güncelle
                                                                                </Button>
                                                                            </div>
                                                                        </div>
                                                                    </AccordionItem>
                                                                )
                                                            })
                                                        )
                                                }
                                            </Accordion>
                                        </div>
                                    </div>
                                </CardBody>
                            </Card>
                        </Grid>
                        
                    </Grid>
                    <AddTicketModal
        visible={isModalVisible}
        onClose={() => setModalVisible(false)}
        onAdd={handleAddTicket}
    />

                </Container>
                <Footer />
            </>
        );
    };

    return renderContent();
};

ConcertEditPage.getLayout = (page: React.ReactNode) => <SidebarLayout>{page}</SidebarLayout>;

export const getServerSideProps = authMiddleware(async (_context: any) => {
    return {
        props: {},
    };
});

export default ConcertEditPage;
