import { PrismaClient } from '@prisma/client';

const prisma = new PrismaClient();

export default async function handler(req, res) {
    if (req.method === 'POST') {
        const { hollyPoints, activityType, activityId, images } = req.body;

        try {
            const createdSnap = await prisma.hollySnap.create({
                data: {
                    activityId,
                    activityType,
                    hollyPoints,
                    images: images.join(","),
                    isDeleted: false
                },
            });

            res.status(201).json({ type: 'success', message: 'Snap eklendi!', createdItem: createdSnap });
        } catch (error) {
            console.error(error);
            res.status(500).json({ type: 'error', message: 'Internal server error', error });
        }
    } else {
        res.status(405).json({ message: 'Method not allowed' });
    }
}
