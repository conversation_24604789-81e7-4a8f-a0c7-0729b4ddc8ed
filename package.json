{"name": "hollystone-panel", "version": "0.1.1", "title": "<PERSON> Admin Panel", "author": {"name": "Yılgın Yazılım B.T."}, "private": false, "dependencies": {"@ckeditor/ckeditor5-build-classic": "^39.0.1", "@ckeditor/ckeditor5-react": "^6.1.0", "@emotion/cache": "11.7.1", "@emotion/react": "11.9.0", "@emotion/server": "11.4.0", "@emotion/styled": "11.8.1", "@internationalized/date": "^3.6.0", "@mui/core": "5.0.0-alpha.54", "@mui/icons-material": "5.8.2", "@mui/lab": "5.0.0-alpha.84", "@mui/material": "5.8.2", "@mui/styles": "5.8.0", "@nextui-org/listbox": "^2.1.7", "@nextui-org/react": "^2.2.9", "@nextui-org/select": "^2.1.8", "@nextui-org/slider": "^2.2.5", "@prisma/client": "^5.6.0", "@react-stately/data": "^3.10.1", "@tinymce/tinymce-react": "^4.3.0", "@types/node": "17.0.39", "@types/nprogress": "0.2.0", "@types/numeral": "2.0.2", "@types/react": "17.0.40", "@types/react-dom": "17.0.13", "@types/react-qr-reader": "^2.1.6", "apexcharts": "3.35.3", "axios": "^1.7.8", "bcrypt": "^5.1.1", "clsx": "1.1.1", "context-api": "0.0.2", "cookie": "^0.5.0", "cookies-next": "^4.0.0", "date-fns": "2.28.0", "dayjs": "^1.11.10", "framer-motion": "^10.16.0", "jsonwebtoken": "^9.0.1", "jspdf": "^2.5.1", "jspdf-autotable": "^3.7.1", "next": "12.1.6", "next-images": "1.8.4", "nodemailer": "^6.9.7", "nprogress": "0.2.0", "numeral": "2.0.6", "react": "^18.2.0", "react-apexcharts": "1.4.0", "react-custom-scrollbars-2": "4.4.0", "react-dom": "^18.2.0", "react-qr-reader": "^3.0.0-beta-1", "react-slick": "^0.29.0", "react-youtube": "^10.1.0", "slate": "^0.94.1", "slate-react": "^0.98.3", "slick-carousel": "^1.8.1", "sweetalert2": "^11.7.27", "tinymce": "^6.7.0", "typescript": "^5.2.2"}, "scripts": {"dev": "next -p 8000", "build": "next build", "start": "next start -p 80", "export": "next export", "lint": "next lint", "lint-fix": "next lint --fix", "format": "prettier --write \"./**/*.{ts,tsx,js,jsx,json}\" --config ./.prettierrc"}, "devDependencies": {"autoprefixer": "^10.4.15", "eslint": "8.17.0", "eslint-config-next": "12.1.6", "eslint-config-prettier": "8.5.0", "eslint-plugin-prettier": "4.0.0", "next-transpile-modules": "9.0.0", "postcss": "^8.4.28", "prettier": "2.6.2", "prisma": "^5.1.1", "tailwindcss": "^3.4.16"}}