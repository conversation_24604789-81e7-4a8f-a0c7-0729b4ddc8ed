import { PrismaClient } from '@prisma/client';

const prisma = new PrismaClient();

export default async function handler(req, res) {
    if (req.method === 'PUT') {
        const { id, title, hollyPoints, price, quota, type } = req.body;

        try {
            const updatedTicket = await prisma.concertTicket.update({
                where: {
                    id: parseInt(id),
                },
                data: {
                    title,
                    hollyPoints,
                    price,
                    quota,
                    type,
                },
            });

            res.status(200).json({ message: 'Bilet güncellendi!', updatedItem: updatedTicket });
        } catch (error) {
            console.error(error);
            res.status(500).json({ message: 'Internal server error', error });
        }
    } else {
        res.status(405).json({ message: 'Method not allowed' });
    }
}
