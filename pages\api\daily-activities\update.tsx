import { PrismaClient } from '@prisma/client';

const prisma = new PrismaClient();

export default async function handler(req, res) {
    if (req.method === 'PUT') {
        const { id, dayOfWeek, stageId, name, description, gateDate, vendorId, date } = req.body;

        try {
            const updatedDailyActivity = await prisma.dailyActivity.update({
                where: {
                    id: parseInt(id),
                },
                data: {
                    vendorId,
                    dayOfWeek,
                    stageId,
                    name,
                    description,
                    gateDate,
                    date,
                },
            });

            res.status(200).json({ message: 'Günlük etkinlik güncellendi!', updatedItem: updatedDailyActivity });
        } catch (error) {
            console.error(error);
            res.status(500).json({ message: 'Internal server error', error });
        }
    } else {
        res.status(405).json({ message: 'Method not allowed' });
    }
}
