import React, { useState, useEffect } from 'react';
import Head from 'next/head';
import { useRouter } from 'next/router';
import { authMiddleware } from 'middleware';
import Swal from 'sweetalert2';
import { Container, Grid, Typography } from '@mui/material';
import { Card, CardBody, CardHeader, Button, Input, Divider } from '@nextui-org/react';
import { Select, SelectItem } from "@nextui-org/select";
import SidebarLayout from '@/layouts/SidebarLayout';
import PageTitleWrapper from '@/components/PageTitleWrapper';
import Footer from '@/components/Footer';
import ArrowBackIcon from '@mui/icons-material/ArrowBack';
import Link from 'next/link';

const EditStagePage = () => {
    const router = useRouter();
    const [item, setItem]: any = useState({});
    const [vendors, setVendors] = useState([]);
    const { id } = router.query;
    const [loading, setLoading] = useState(true);

    const loadList = async () => {
        const url = `/api/admins?id=${id}`;

        try {
            const res: any = await fetch(url);

            if (res.status === 200) {
                const json = await res.json();
                setItem(json.results[0]);
            } else {
                Swal.fire({
                    icon: 'error',
                    title: 'Hata',
                    text: res.message,
                });
            }
        } catch (error) {
            Swal.fire({
                icon: 'error',
                title: 'Hata',
                text: 'Bir sorun meydana geldi!',
            });
            console.error(error);
        } finally {
            setLoading(false);
        }
    };

    const loadVendors = async () => {
        const url = `/api/vendors`;

        try {
            const res = await fetch(url);

            if (res.status === 200) {
                const json = await res.json();
                setVendors(json.results);
            } else {
                Swal.fire({
                    icon: 'error',
                    title: 'Hata',
                    text: 'Bir sorun meydana geldi!',
                });
            }
        } catch (error) {
            console.error(error);
        } finally {
            setLoading(false);
        }
    };

    useEffect(() => {
        loadList();
        loadVendors();
    }, []);

    const handleDelete = async () => {
        const shouldDelete = await Swal.fire({
            title: 'Dikkat!',
            text: 'Bu kaydı silmek istediğine gerçekten emin misin?',
            icon: 'warning',
            showCancelButton: true,
            confirmButtonText: 'Evet, sil!',
            cancelButtonText: 'Vazgeç',
        });

        if (shouldDelete.isConfirmed) {
            try {
                const response = await fetch(`/api/admins/delete`, {
                    method: 'DELETE',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({ id: id }),
                });

                if (response.ok) {
                    router.push('/admins');
                    Swal.fire({
                        icon: 'success',
                        title: 'Başarılı',
                        text: 'Kayıt silindi!',
                    });
                } else {
                    Swal.fire({
                        icon: 'error',
                        title: 'Hata',
                        text: 'Bir sorun meydana geldi!',
                    });
                }
            } catch (error) {
                console.error('An error occurred:', error);
            }
        }
    };

    const handleUpdate = async (event) => {
        event.preventDefault();
        if (!item.username ||
            !item.email ||
            !item.phone ||
            !item.identityNumber ||
            !item.vendorId ||
            !item.authorityLevel
            ) {
            Swal.fire({
                icon: 'error',
                title: 'Hata',
                text: 'Lütfen alanları doldurun!',
            });
            return;
        }
        try {
            const response = await fetch(`/api/admins/update`, {
                method: 'PUT',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify(item),
            });

            if (response.ok) {
                await loadList();
                Swal.fire({
                    icon: 'success',
                    title: 'Başarılı',
                    text: 'Kayıt güncellendi!',
                });
            } else {
                const json = await response.json();
                Swal.fire({
                    icon: 'error',
                    title: 'Hata',
                    text: json.message,
                });
            }
        } catch (error) {
            console.error('An error occurred:', error);
        }
    }

    if (loading) {
        return "Loading";
    }

    return (
        <>
            <Head>
                <title>Yönetici - Düzenle</title>
            </Head>
            <PageTitleWrapper>
                <Grid container alignItems="center">
                    <Grid item>
                        <Typography variant="h3" component="h3" gutterBottom>
                            Yönetici Düzenle
                        </Typography>
                    </Grid>
                </Grid>
            </PageTitleWrapper>
            <Container maxWidth="lg">
                <Grid
                    container
                    direction="row"
                    justifyContent="center"
                    alignItems="stretch"
                    spacing={4}
                >
                    <Grid item xs={12}>
                        <Card
                            isBlurred
                            className="border-none bg-white dark:bg-default-100/50 max-w-[610px]"
                            shadow="sm"
                        >
                            <form onSubmit={handleUpdate}>
                                <CardHeader className="flex justify-between gap-3">
                                    <div>
                                        <Link href="/admins">
                                            <Button type="button" color="default" startContent={<ArrowBackIcon />}>
                                                Adminler
                                            </Button>
                                        </Link>
                                    </div>
                                    <div>Yönetici Bilgileri</div>
                                    <div className="flex-end">
                                        <Button type="button" onClick={handleDelete} color="danger" className="mr-2">
                                            Sil
                                        </Button>
                                        <Button type="submit" color="primary">
                                            Güncelle
                                        </Button>
                                    </div>
                                </CardHeader>

                                <Divider />

                                <CardBody>
                                    <div className="flex">
                                        <div className="p-4 w-full">
                                            <div className="w-full mb-2 flex flex-row">
                                                <Input
                                                    type="text"
                                                    label="Kullanıcı Adı"
                                                    name="username"
                                                    value={item.username}
                                                    className="mr-2"
                                                    onChange={(e: any) => setItem({ ...item, username: e.target.value })}
                                                />
                                                <Input
                                                    type="text"
                                                    label="E-mail"
                                                    name="email"
                                                    value={item.email}
                                                    className="mr-2"
                                                    onChange={(e: any) => setItem({ ...item, email: e.target.value })}
                                                />
                                                <Input
                                                    type="text"
                                                    label="Şifre"
                                                    placeholder="Değişmeyecekse boş bırakın..."
                                                    name="newPassword"
                                                    value={item.newPassword}
                                                    onChange={(e: any) => setItem({ ...item, newPassword: e.target.value })}
                                                />
                                            </div>
                                            <div className="w-full mb-2 flex flex-row">
                                                <Input
                                                    type="text"
                                                    label="Telefon Numarası"
                                                    name="phone"
                                                    value={item.phone}
                                                    className="mr-2"
                                                    onChange={(e: any) => setItem({ ...item, phone: e.target.value })}
                                                />
                                                <Input
                                                    type="text"
                                                    label="Kimlik Numarası"
                                                    name="identityNumber"
                                                    value={item.identityNumber}
                                                    onChange={(e: any) => setItem({ ...item, identityNumber: e.target.value })}
                                                />
                                            </div>
                                            <div className="w-full mb-2 flex flex-row">
                                                <Select
                                                    label="Bayi"
                                                    placeholder="Bir seçim yapın..."
                                                    className="mr-2"
                                                    defaultSelectedKeys={[item.vendorId?.toString()]}
                                                    onChange={(e: any) => {
                                                        setItem({ ...item, vendorId: parseInt(e.target.value) });
                                                    }}
                                                >
                                                    {
                                                        vendors.map((innerItem: any, index: number) => {
                                                            return (
                                                                <SelectItem key={innerItem.id} value={index}>{innerItem.name}</SelectItem>
                                                            )
                                                        })
                                                    }
                                                </Select>
                                                <Select
                                                    label="Rol"
                                                    placeholder="Bir seçim yapın..."
                                                    defaultSelectedKeys={item.authorityLevel ? [item.authorityLevel?.toString()] : []}
                                                    onChange={(e: any) => setItem({ ...item, authorityLevel: parseInt(e.target.value) })}
                                                >
                                                    <SelectItem key="1" value="1">
                                                        Admin
                                                    </SelectItem>
                                                    <SelectItem key="2" value="2">
                                                        Bayi Yöneticisi
                                                    </SelectItem>
                                                    <SelectItem key="3" value="3">
                                                        Personel
                                                    </SelectItem>
                                                </Select>
                                            </div>
                                        </div>
                                    </div>
                                </CardBody>
                            </form>
                        </Card>
                    </Grid>
                </Grid>
            </Container>
            <Footer />
        </>
    );
};

EditStagePage.getLayout = (page: React.ReactNode) => <SidebarLayout>{page}</SidebarLayout>;

export const getServerSideProps = authMiddleware(async (_context: any) => {
    return {
        props: {},
    };
});

export default EditStagePage;
