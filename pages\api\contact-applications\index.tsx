import { PrismaClient } from '@prisma/client';

const prisma = new PrismaClient();

export default async function handler(req, res) {
    if (req.method !== 'GET') {
        return res.status(405).json({ message: 'Method not allowed' });
    }

    const { id, page = 1, pageSize = 10, filter = "" } = req.query;

    let where = {};

    if (id) {
        where = {
            id: parseInt(id),
        };
        
        try {
            await prisma.contactApplication.update({
                where: {
                    id: parseInt(id),
                },
                data: {
                    isRead: true,
                },
            });
        } catch (error) {
            console.error(error);
            res.status(500).json({ message: 'Internal server error', error });
        }
    } else {
        where = {
            AND: [
                {
                    OR: [
                        { nameLastName: { contains: filter } },
                        { phoneNumber: { contains: filter } },
                    ],
                },
                { isDeleted: false },
            ],
        };
    }

    try {
        const contactApplications = await prisma.contactApplication.findMany({
            where,
            orderBy: {
                createdAt: 'desc'
            },
            skip: (parseInt(page) - 1) * parseInt(pageSize),
            take: parseInt(pageSize),
            include: {
                city: {
                    select: {
                        title: true,
                    },
                },
            },
        });

        const totalCount = await prisma.contactApplication.count({ where });

        const totalCountUnRead = await prisma.contactApplication.count({ where: { isRead: false, isDeleted: false } });

        const formattedContactApplications = contactApplications.map(contactApplication => ({
            ...contactApplication,
            cityName: contactApplication.city.title,
        }));
        // Close the Prisma client connection
        await prisma.$disconnect();

        res.status(200).json({ results: formattedContactApplications, totalCount, totalCountUnRead });
    } catch (error) {
        console.error(error);
        // Close the Prisma client connection
        await prisma.$disconnect();
        res.status(500).json({ message: 'Internal server error' });
    }
}
