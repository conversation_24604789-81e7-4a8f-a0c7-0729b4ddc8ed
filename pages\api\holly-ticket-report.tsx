import { PrismaClient } from '@prisma/client';

const prisma = new PrismaClient();

export default async function handler(req, res) {
    if (req.method !== 'GET') {
        return res.status(405).json({ message: 'Method not allowed' });
    }

    const { vendorId } = req.query;

    try {
        const concerts = await prisma.concert.findMany({
            where: {
                vendorId: parseInt(vendorId),
                isDeleted: false
            }
        });

        // Close the Prisma client connection
        await prisma.$disconnect();

        res.status(200).json({ results: concerts });
    } catch (error) {
        console.error(error);
        // Close the Prisma client connection
        await prisma.$disconnect();
        res.status(500).json({ message: 'Internal server error' });
    }
}
