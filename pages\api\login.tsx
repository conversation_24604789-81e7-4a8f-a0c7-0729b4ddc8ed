import { PrismaClient } from '@prisma/client';
import bcrypt from 'bcrypt';
import jwt from 'jsonwebtoken';

const prisma = new PrismaClient();
const JWT_SECRET = process.env.SECRET_KEY;

export default async function handler(req, res) {
    if (req.method !== 'POST') {
        return res.status(405).json({ message: 'Method not allowed' });
    }

    const { username, password } = req.body;

    try {
        const user = await prisma.admin.findFirst({
            where: {
                username: username,
                isDeleted: false
            },
            include: {
                vendor: true
            }
        });

        if (user && await bcrypt.compare(password, user.password)) {
            const token = jwt.sign({ userId: user.id }, JWT_SECRET, { algorithm: 'HS256', expiresIn: '4h' });

            return res.status(200).json({ status: 'success', token, id: user.id, userName: user.username, email: user.email, vendorId: user.vendorId, authorityLevel: user.authorityLevel, modules: user.vendor.modules });
        } else {
            return res.status(401).json({ status: 'error', message: 'Invalid credentials' });
        }
    } catch (error) {
        console.error(error);
        res.status(500).json({ message: 'Internal server error' });
    }
}
