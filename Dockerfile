FROM node:18-alpine

WORKDIR /app

# Alpine'de Prisma için gerekli bağımlılıkları yükle
RUN apk add --no-cache openssl

# package.json dosyasını kopyala
COPY package.json package-lock.json* ./

# Bağımlılıkları yükle
RUN npm install --force

# Prisma şemasını kopyala ve generate et
COPY prisma ./prisma/
RUN npx prisma generate

# Geri kalan dosyaları kopyala
COPY . .

# Next.js uygulamasını build et
RUN npm run build

# Uygulamayı çalıştır
EXPOSE 80
CMD ["npm", "start"]



