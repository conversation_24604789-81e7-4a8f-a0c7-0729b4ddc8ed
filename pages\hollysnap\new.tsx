import React, { useEffect, useState } from 'react';
import Head from 'next/head';
import { useRouter } from 'next/router';
import { authMiddleware } from 'middleware';
import Swal from 'sweetalert2';
import { Container, Grid, Typography, CircularProgress } from '@mui/material';
import { Image, Card, CardBody, CardHeader, Button, Input, Divider, Accordion, AccordionItem } from '@nextui-org/react';
import { Select, SelectItem } from "@nextui-org/select";
import SidebarLayout from '@/layouts/SidebarLayout';
import PageTitleWrapper from '@/components/PageTitleWrapper';
import Footer from '@/components/Footer';
import { PlusIcon } from '@/components/PlusIcon';
import DeleteForeverIcon from '@mui/icons-material/DeleteForever';
import ArrowBackIcon from '@mui/icons-material/ArrowBack';
import Link from 'next/link';

const NewSnapPage = () => {
    const router = useRouter();
    const [item, setItem]: any = useState({ images: [] });
    const [imageLoading, setImageLoading] = useState(false);
    const [id, setId] = useState(0);
    const [currentTimestamp, setCurrentTimestamp] = useState(new Date().getTime());
    const [dropdownItems, setDropdownItems] = useState([]);


    const getNextId = async () => {
        const url = `/api/hollysnap/next`;

        try {
            const res = await fetch(url);

            if (res.status === 200) {
                const json = await res.json();
                setItem({ ...item, images: [`holly_snap/${json.nextId}-0.webp`] })
                setId(json.nextId);
            } else {
                Swal.fire({
                    icon: 'error',
                    title: 'Hata',
                    text: 'Bir sorun meydana geldi!',
                });
            }
        } catch (error) {
            console.error(error);
        }
    };

    const loadConcerts = async () => {
        const url = `/api/concerts`;

        try {
            const res = await fetch(url);

            if (res.status === 200) {
                console.log(res)
                const json = await res.json();
                setDropdownItems(json.results);
            } else {
                Swal.fire({
                    icon: 'error',
                    title: 'Hata',
                    text: 'Bir sorun meydana geldi!',
                });
            }
        } catch (error) {
            console.error(error);
        }
    };

    const loadDailyActivities = async () => {
        const url = `/api/daily-activities`;

        try {
            const res = await fetch(url);

            if (res.status === 200) {
                const json = await res.json();
                setDropdownItems(json.results);
            } else {
                Swal.fire({
                    icon: 'error',
                    title: 'Hata',
                    text: 'Bir sorun meydana geldi!',
                });
            }
        } catch (error) {
            console.error(error);
        }
    };

    const fetchData = async () => {
        await getNextId();
        if (item.activityType === false) {
            await loadConcerts();
        } else {
            await loadDailyActivities();
        }
    };

    useEffect(() => {
        fetchData();
    }, []);

    const handleDeleteImage = async (key) => {
        const shouldDelete = await Swal.fire({
            title: 'Dikkat!',
            text: 'Bu resmi silmek istediğine gerçekten emin misin?',
            icon: 'warning',
            showCancelButton: true,
            confirmButtonText: 'Evet, sil!',
            cancelButtonText: 'Vazgeç',
        });

        if (shouldDelete.isConfirmed) {
            let newImages = item.images;
            const filePath = newImages[key];

            try {
                const response = await fetch(`https://api.hollystone.com.tr/api/functions/delete`, {
                    method: 'POST',
                    headers: {
                        'Authorization': `Bearer ${localStorage.getItem('token')}`,
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({ fileName: filePath })
                });

                if (response.ok) {
                    const data = await response.json();

                    if (data.type == "success") {
                        newImages.splice(key, 1);
                        setItem({ ...item, images: newImages })
                        Swal.fire({
                            icon: 'success',
                            title: 'Başarılı',
                            text: 'Resim güncellendi!',
                        });
                    } else {
                        Swal.fire({
                            icon: 'error',
                            title: 'Hata',
                            text: data.error,
                        });
                    }
                } else {
                    Swal.fire({
                        icon: 'error',
                        title: 'Hata',
                        text: 'Bir sorun meydana geldi!',
                    });
                }
            } catch (error) {
                Swal.fire({
                    icon: 'error',
                    title: 'Hata',
                    text: 'Bir sorun meydana geldi!',
                });
            }
        }
    }

    const handleImageChange = async (event, image) => {
        const file = event.target.files[0];
        const filePath = image.split('.');

        if (file) {
            if (!file.type.startsWith('image/')) {
                Swal.fire({
                    icon: 'error',
                    title: 'Hata',
                    text: 'Seçilen format desteklenmiyor!',
                });
                return;
            }
            setImageLoading(true);
            const formData = new FormData();
            formData.append('image', file);
            formData.append('fileName', filePath[0]);

            try {
                const response = await fetch(`https://api.hollystone.com.tr/api/functions/upload`, {
                    method: 'POST',
                    headers: {
                        'Authorization': `Bearer ${localStorage.getItem('token')}`
                    },
                    body: formData,
                });

                if (response.ok) {
                    const data = await response.json();

                    if (data.type == "success") {
                        Swal.fire({
                            icon: 'success',
                            title: 'Başarılı',
                            text: 'Resim güncellendi!',
                        });
                        setCurrentTimestamp(new Date().getTime());
                    } else {
                        Swal.fire({
                            icon: 'error',
                            title: 'Hata',
                            text: data.error,
                        });
                    }
                } else {
                    Swal.fire({
                        icon: 'error',
                        title: 'Hata',
                        text: 'Bir sorun meydana geldi!',
                    });
                }
            } catch (error) {
                Swal.fire({
                    icon: 'error',
                    title: 'Hata',
                    text: 'Bir sorun meydana geldi!',
                });
            }
            setImageLoading(false);
        }
    };

    const handleNewImage = () => {
        if (item.images.length > 0) {
            const latestImage = item.images[item.images.length - 1];
            const lastImageParts = latestImage.split('-');
            const lastImageNumber = parseInt(lastImageParts[1].replace(".webp", ""));
            let newImages = item.images;
            newImages.push(`holly_snap/${id}-${lastImageNumber + 1}.webp`);
            setItem({ ...item, images: newImages })
        } else {
            setItem({ ...item, images: [`holly_snap/${id}-0.webp`] })
        }
    }

    const handleInsert = async (event: React.FormEvent) => {
        event.preventDefault();
        if (item.activityId == 0 || item.activityType == null) {
            Swal.fire({
                icon: 'error',
                title: 'Hata',
                titleText: 'Lütfen alanları doldurun!',
            });
            return;
        }
        if (item.images.length < 1) {
            Swal.fire({
                icon: 'error',
                title: 'Hata',
                text: 'Lütfen bir resim seçin!',
            });
            return;
        }
        try {
            const response = await fetch(`/api/hollysnap/insert`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify(item),
            });

            if (response.ok) {
                const data = await response.json();

                if (data.type == "success") {
                    Swal.fire({
                        icon: 'success',
                        title: 'Başarılı',
                        text: 'Kayıt başarılı!',
                    }).then(() => {
                        router.push('/hollysnap');
                    });
                } else {
                    Swal.fire({
                        icon: 'error',
                        title: 'Hata',
                        text: data.error,
                    });
                }
            } else {
                Swal.fire({
                    icon: 'error',
                    title: 'Hata',
                    text: 'Bir sorun meydana geldi!',
                });
            }
        } catch (error) {
            console.error('An error occurred:', error);
        }
        setImageLoading(false);
    };

    if (!item.images || item.images.length === 0) {
        return <p>Yükleniyor...</p>;
      }
      

    return (
        <>
            <Head>
                <title>HollySnap - Oluştur</title>
            </Head>
            <PageTitleWrapper>
                <Grid container alignItems="center">
                    <Grid item>
                        <Typography variant="h3" component="h3" gutterBottom>
                            HollySnap Oluştur
                        </Typography>
                    </Grid>
                </Grid>
            </PageTitleWrapper>
            <Container maxWidth="lg">
                <Grid
                    container
                    direction="row"
                    justifyContent="center"
                    alignItems="stretch"
                    spacing={4}
                >
                    <Grid item xs={12}>
                        <Card
                            isBlurred
                            className="border-none bg-white dark:bg-default-100/50"
                            shadow="sm"
                        >
                            <form onSubmit={handleInsert}>
                                <CardHeader className="flex justify-between gap-3">
                                    <div>
                                        <Link href="/hollysnap">
                                            <Button type="button" color="default" startContent={<ArrowBackIcon />}>
                                                Snap Liste
                                            </Button>
                                        </Link>
                                    </div>
                                    <div className="flex-end">
                                        <Button type="submit" color="primary">
                                            Oluştur
                                        </Button>
                                    </div>
                                </CardHeader>

                                <Divider />

                                <CardBody>
                                    <div className="flex">
                                        <div className="relative w-1/2" style={{
                                            minWidth: 400
                                        }}>
                                            <Button color="primary" onClick={handleNewImage} endContent={<PlusIcon width={undefined} height={undefined} />}>
                                                Foroğraf Ekle
                                            </Button>
                                            <Accordion defaultExpandedKeys={["0"]}>
  {Array.isArray(item.images) && item.images.length > 0 ? (
    item.images.map((image: any, index: number) => (
      <AccordionItem
        key={index}
        aria-label={`Resim ${index + 1}`}
        title={`Resim ${index + 1}`}
        startContent={
          <div className="w-full mb-2 flex flex-row">
            <Button
              color="danger"
              onClick={() => handleDeleteImage(index)}
              className="min-w-unit-10 mr-2"
            >
              <DeleteForeverIcon />
            </Button>
            <Image
              radius="none"
              loading="eager"
              className="h-unit-10"
              src={`https://api.hollystone.com.tr/resources/images/${image}?t=${currentTimestamp}`}
              fallbackSrc="https://via.placeholder.com/50x50.png?text=No+Image"
            />
          </div>
        }
      >
        <label className="block cursor-pointer">
          <div className="relative">
            <Image
              width={400}
              height={400}
              radius="none"
              loading="eager"
              style={{ height: "100%" }}
              src={`https://api.hollystone.com.tr/resources/images/${image}?t=${currentTimestamp}`}
              fallbackSrc="https://via.placeholder.com/400x400.png?text=No+Image"
            />
            {imageLoading && (
              <CircularProgress
                size={48}
                style={{
                  position: "absolute",
                  top: "50%",
                  left: "50%",
                  transform: "translate(-50%, -50%)",
                  zIndex: 999,
                }}
              />
            )}
          </div>
          <Input
            type="file"
            label="Resim"
            name="image"
            className="hidden"
            accept="image/*"
            onChange={(e: any) => handleImageChange(e, image)}
          />
        </label>
      </AccordionItem>
    ))
  ) : (
    <Typography>No images found</Typography>
  )}
</Accordion>

                                        </div>

                                        <div className="p-4 w-full">
                                        <Select
    label="Etkinlik Türü"
    placeholder="Bir seçim yapın..."
    selectedKeys={[String(item.activityType)]}
    onSelectionChange={(keys) => {
        const selectedKey = Array.from(keys)[0]; // Seçili değeri al
        const isConcert = selectedKey === "false"; // Konser mi?

        setItem({ ...item, activityType: !isConcert, activityId: null }); // Seçili ID'yi sıfırla
        setDropdownItems([]); // Eski dropdown verilerini temizle

        if (isConcert) {
            loadConcerts(); // Konserleri getir
        } else {
            loadDailyActivities(); // Günlük etkinlikleri getir
        }
    }}
    className="mb-2"
>
    <SelectItem key="false" value="false">
        Konser
    </SelectItem>
    <SelectItem key="true" value="true">
        Günlük Etkinlik
    </SelectItem>
</Select>

<Select
    label={item.activityType === false ? "Konser" : "Günlük Etkinlik"}
    placeholder="Bir seçim yapın..."
    selectedKeys={[String(item.activityId)]}
    onSelectionChange={(keys) => {
        const selectedKey = Array.from(keys)[0]; // Seçili değeri al
        setItem({ ...item, activityId: Number(selectedKey) }); // Seçili ID'yi güncelle
    }}
    className="mb-2"
>
    {dropdownItems.map((dItem) => (
        <SelectItem key={dItem.id} value={String(dItem.id)}>
            {dItem.name}
        </SelectItem>
    ))}
</Select>

                                            <Input
                                                type="number"
                                                label="Holly Puan"
                                                placeholder="Holly Puan"
                                                name="hollyPoints"
                                                min={0}
                                                value={item.hollyPoints}
                                                className="mb-2"
                                                onChange={(e: any) => setItem({ ...item, hollyPoints: parseInt(e.target.value) })}
                                            />
                                        </div>
                                    </div>
                                </CardBody>
                            </form>
                        </Card>
                    </Grid>
                </Grid>
            </Container>
            <Footer />
        </>
    );
};

NewSnapPage.getLayout = (page: React.ReactNode) => <SidebarLayout>{page}</SidebarLayout>;

export const getServerSideProps = authMiddleware(async (_context: any) => {
    return {
        props: {},
    };
});

export default NewSnapPage;
