import React, { useState, useMemo, useCallback, useEffect } from "react";
import {
    Table,
    TableHeader,
    TableColumn,
    TableBody,
    TableRow,
    TableCell,
    Input,
    Button,
    Pagination,
    Spinner,
    getKeyValue,
    User,
} from "@nextui-org/react";
import { getCookie } from 'cookies-next';
import { SearchIcon } from "../../src/components/SearchIcon";
import { withRouter } from 'next/router';
import Swal from 'sweetalert2';
import EditIcon from '@mui/icons-material/Edit';

function ShopOrdersTable({ router }) {
    const vendorId = getCookie("vendorId");
    const [filterValue, setFilterValue] = useState("");
    const [page, setPage] = useState(1);
    const [total, setTotal] = useState(0);
    const [isLoading, setIsLoading] = useState(true);
    const [items, setItems] = useState([]);
    const [dropdownItems, setDropdownItems] = useState([]);

    const rowsPerPage = 10;

    const loadList = async (pageInput: number, filter: string) => {
        const url = `/api/shop-orders?page=${pageInput}&pageSize=${rowsPerPage}&filter=${filter}&vendorId=${vendorId}`;

        try {
            const res = await fetch(url);

            if (res.status === 200) {
                const json = await res.json();

                setTotal(json.totalCount);
                setIsLoading(false);
                setItems(json.results);
            } else {
                setIsLoading(false);
                setItems([]);
            }
        } catch (error) {
            setIsLoading(false);
            setItems([]);
            console.error(error);
            throw error;
        }
    };

    const loadCategories = async () => {
        const url = `/api/shop-categories`;

        try {
            const res = await fetch(url);

            if (res.status === 200) {
                const json = await res.json();
                setDropdownItems(json.results);
            } else {
                Swal.fire({
                    icon: 'error',
                    title: 'Hata',
                    text: 'Bir sorun meydana geldi!',
                });
            }
        } catch (error) {
            console.error(error);
        } finally {
            setIsLoading(false);
        }
    };

    useEffect(() => {
        const fetchData = async () => {
            await loadList(1, "");
            await loadCategories();
        };

        fetchData();
    }, []);

    const handleEdit = (rowId) => {
        router.push('/shop-orders/edit?id=' + rowId);
    };

    const pages = Math.ceil(total / rowsPerPage);

    const onPaginationChange = useCallback(
        async (pageInput) => {
            setIsLoading(true);
            setPage(pageInput);
            await loadList(pageInput, filterValue);
        },
        []
    );

    const onSearchChange = useCallback(
        (value: string) => {
            if (value) {
                setIsLoading(true);
                setFilterValue(value);
                loadList(page, value);
                setPage(1);
            } else {
                setFilterValue("");
                loadList(page, "");
            }
        },
        []
    );

    const onClear = useCallback(() => {
        setFilterValue("");
        setPage(1);
    }, []);

    const topContent = useMemo(() => {
        return (
            <div className="flex flex-col gap-4">
                <div className="flex justify-between gap-3 items-end">
                    <Input
                        isClearable
                        className="w-full sm:max-w-[44%]"
                        placeholder="Arayın..."
                        startContent={<SearchIcon />}
                        value={filterValue}
                        onClear={() => onClear()}
                        onValueChange={onSearchChange}
                        style={{
                            minWidth: "150px"
                        }}
                    />
                </div>
            </div>
        );
    }, [
        filterValue,
        onSearchChange,
        onClear,
        dropdownItems
    ]);

    const renderCell = useCallback((cellValue: any, columnKey: any, rowId: any) => {
        switch (columnKey) {
            case "images":
                const image = cellValue.split(',');
                return (
                    <User name=""
                        avatarProps={{ radius: "lg", src: `https://api.hollystone.com.tr/resources/images/${image[0]}` }}
                    >
                    </User>
                );
            case "actions":
                return (
                    <Button
                        onClick={() => handleEdit(rowId)}
                        color="primary"
                        startContent={<EditIcon />}
                    >
                        Düzenle
                    </Button>
                );
            default:
                return cellValue;
        }
    }, []);

    return (
        <Table
            aria-label="Example table with client async pagination"
            topContent={topContent}
            topContentPlacement="inside"
            bottomContent={
                pages > 0 ? (
                    <div className="flex w-full justify-center">
                        <Pagination
                            isCompact
                            showControls
                            showShadow
                            color="primary"
                            page={page}
                            total={pages}
                            onChange={onPaginationChange}
                        />
                    </div>
                ) : null
            }
            classNames={{
                table: "min-h-[400px]",
            }}
        >
            <TableHeader>
                <TableColumn key="userName">Müşteri</TableColumn>
                <TableColumn key="trackingNumber">Sipariş Numarası</TableColumn>
                <TableColumn key="status">Durum</TableColumn>
                <TableColumn key="actions">İşlemler</TableColumn>
            </TableHeader>
            <TableBody
                isLoading={isLoading && !items.length}
                items={items}
                loadingContent={<Spinner />}
                emptyContent={"Kayıt bulunamadı!"}
            >
                {(item) => (
                    <TableRow key={item.id}>
                        {(columnKey) => (
                            <TableCell key={columnKey+item.id}>{renderCell(getKeyValue(item, columnKey), columnKey, item.id)}</TableCell>
                        )}
                    </TableRow>
                )}
            </TableBody>
        </Table>
    );
}

export default withRouter(ShopOrdersTable);
