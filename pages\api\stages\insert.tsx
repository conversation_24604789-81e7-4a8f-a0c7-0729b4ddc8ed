import { PrismaClient } from '@prisma/client';

const prisma = new PrismaClient();

export default async function handler(req, res) {
    if (req.method === 'POST') {
        const { name } = req.body;

        try {
            const createdStage = await prisma.stage.create({
                data: {
                    name,
                    isDeleted: false
                },
            });

            res.status(201).json({ message: '<PERSON><PERSON>e eklendi!', createdItem: createdStage });
        } catch (error) {
            console.error(error);
            res.status(500).json({ message: 'Internal server error', error });
        }
    } else {
        res.status(405).json({ message: 'Method not allowed' });
    }
}
