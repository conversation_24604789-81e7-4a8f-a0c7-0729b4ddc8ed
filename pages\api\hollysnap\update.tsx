import { PrismaClient } from '@prisma/client';

const prisma = new PrismaClient();

export default async function handler(req, res) {
    if (req.method === 'PUT') {
        const { id, hollyPoints, activityId, activityType } = req.body;

        try {
            const updatedSnap = await prisma.hollySnap.update({
                where: {
                    id: parseInt(id),
                },
                data: {
                    activityId: activityId,
                    activityType: activityType,
                    hollyPoints: hollyPoints,
                },
            });

            res.status(200).json({ message: 'Snap güncellendi!', updatedItem: updatedSnap });
        } catch (error) {
            console.error(error);
            res.status(500).json({ message: 'Internal server error', error });
        }
    } else {
        if (req.method === 'POST') {
            const { id, images } = req.body;
    
            try {
                const updatedSnap = await prisma.hollySnap.update({
                    where: {
                        id: parseInt(id),
                    },
                    data: {
                        images: images,
                    },
                });
    
                res.status(200).json({ message: 'Snap güncellendi!', updatedItem: updatedSnap });
            } catch (error) {
                console.error(error);
                res.status(500).json({ message: 'Internal server error', error });
            }
        } else {
            res.status(405).json({ message: 'Method not allowed' });
        }
    }
}
