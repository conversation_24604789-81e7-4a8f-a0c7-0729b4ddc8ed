import { PrismaClient } from '@prisma/client';

const prisma = new PrismaClient();

export default async function handler(req, res) {
  if (req.method !== 'GET') {
    return res.status(405).json({ message: 'Method not allowed' });
  }

  const { id, page = 1, pageSize = 10, filter = "" } = req.query;

  let where = {};

  if (id) {
    where = {
      id: parseInt(id),
    };
  } else {
    where = {
      AND: [
        {
          OR: [
            { name: { contains: filter } },
            { description: { contains: filter } },
          ],
        },
        { isDeleted: false },
      ],
    };
  }

  try {
    const concerts = await prisma.concert.findMany({
      where: {
        isDeleted: false,
        AND: [
          { name: { contains: filter } },
          { description: { contains: filter } },
        ],
      },
      orderBy : {
        date : 'desc'
      },
      take: parseInt(pageSize),
      skip: (parseInt(page) - 1) * parseInt(pageSize),
    });

    const concertIds = concerts.map((concert) => concert.id);

    const totalPayment = await prisma.paymentTransfer.findMany({
      where: {
        userTicket: {
          ticket: {
            concertId: {
              in: concertIds,
            },
          },
          payment: true,
          status: {
            in: [1, 2],
          },
        },
      },
      include: {
        userTicket: {
          include: {
            ticket: true,
          },
        }
      }
    });

    const totalTickets = await prisma.userTicket.findMany({
      where: {
        ticket: {
          concertId: {
            in: concertIds,
          },
        },
        payment: true,
        status: {
          in: [1, 2],
        },
      },
      include: {
        ticket: true
      }
    });

    const formattedConcerts = concerts.map((concert) => {
      const totalPaymentCount = totalPayment.filter((paymentTransfer) => paymentTransfer.userTicket.ticket.concertId === concert.id).length;

      const totalTicketsObj = totalTickets.filter((userTicket) => userTicket.ticket.concertId === concert.id && userTicket.paidPrice != 0);
      const totalTicketsCount = totalTicketsObj.length;

      const guestTicketsObj = totalTickets.filter((userTicket) => userTicket.paidPrice === 0);
      const guestTicketsCount = guestTicketsObj.length;

      const totalTicketsPrice = totalTicketsObj.reduce((acc, curr) => acc + curr.ticket.price, 0);

      return {
        ...concert,
        totalPayment: totalPaymentCount,
        totalTickets: totalTicketsCount,
        guestTicketsCount,
        totalTicketsPrice,
      };
    });

    const totalCount = await prisma.concert.count({ where });
    // Close the Prisma client connection
    await prisma.$disconnect();

    res.status(200).json({ results: formattedConcerts, totalCount });
  } catch (error) {
    console.error(error);
    // Close the Prisma client connection
    await prisma.$disconnect();
    res.status(500).json({ message: 'Internal server error' });
  }
}
