import React, { useState, useEffect } from 'react';
import Head from 'next/head';
import { useRouter } from 'next/router';
import { authMiddleware } from 'middleware';
import Swal from 'sweetalert2';
import Link from 'next/link';
import { Container, Grid, Typography, CircularProgress } from '@mui/material';
import { Image, Card, CardBody, CardHeader, Dropdown, DropdownTrigger, DropdownMenu, DropdownItem, Button, Input, Divider, Accordion, AccordionItem } from '@nextui-org/react';
import { Select, SelectItem } from "@nextui-org/select";
import SidebarLayout from '@/layouts/SidebarLayout';
import PageTitleWrapper from '@/components/PageTitleWrapper';
import Footer from '@/components/Footer';
import ArrowBackIcon from '@mui/icons-material/ArrowBack';

const EditSnapPage = () => {
    const router = useRouter();
    const [item, setItem]: any = useState({});
    const [addresses, setAddresses]: any = useState([]);
    const [cities, setCities]: any = useState([]);
    const [orders, setOrders]: any = useState([]);
    const [tickets, setTickets]: any = useState([]);
    const dropdownItems = {
        true: "Engelli Değil",
        false: "Engelli"
    };
    const { id } = router.query;
    const [loading, setLoading] = useState(true);
    const [selectedImage, setSelectedImage] = useState<string | null>(null);
    const [imageLoading, setImageLoading] = useState(false);

    const loadList = async () => {
        const url = `/api/users?id=${id}`;

        try {
            const res = await fetch(url);

            if (res.status === 200) {
                const json = await res.json();
                json.results[0].dateOfBirth = new Date(json.results[0]?.dateOfBirth);
                setItem(json.results[0]);
            } else {
                console.log("API call failed");
            }
        } catch (error) {
            console.error(error);
        } finally {
            setLoading(false);
        }
    };

    const loadCities = async () => {
        const url = `/api/cities`;

        try {
            const res = await fetch(url);

            if (res.status === 200) {
                const json = await res.json();
                setCities(json.results);
            } else {
                console.log("API call failed");
            }
        } catch (error) {
            console.error(error);
        }
    }

    const loadUserAddresses = async () => {
        const url = `/api/user-addresses?id=${id}`;

        try {
            const res = await fetch(url);

            if (res.status === 200) {
                const json = await res.json();
                setAddresses(json.results);
            } else {
                console.log("API call failed");
            }
        } catch (error) {
            console.error(error);
        } finally {
            setLoading(false);
        }
    }

    const loadUserOrders = async () => {
        const url = `/api/user-orders?id=${id}`;

        try {
            const res = await fetch(url);

            if (res.status === 200) {
                const json = await res.json();
                setOrders(json.results);
            } else {
                console.log("API call failed");
            }
        } catch (error) {
            console.error(error);
        } finally {
            setLoading(false);
        }
    }

    const loadUserTickets = async () => {
        const url = `/api/user-tickets?id=${id}`;

        try {
            const res = await fetch(url);

            if (res.status === 200) {
                const json = await res.json();
                setTickets(json.results);
            } else {
                console.log("API call failed");
            }
        } catch (error) {
            console.error(error);
        } finally {
            setLoading(false);
        }
    }

    useEffect(() => {
        loadList();
        loadCities();
        loadUserAddresses();
        loadUserOrders();
        loadUserTickets();
    }, []);

    const handleImageChange = async (event) => {
        const file = event.target.files[0];

        if (file) {
            if (!file.type.startsWith('image/')) {
                Swal.fire({
                    icon: 'error',
                    title: 'Hata',
                    text: 'Seçilen format desteklenmiyor!',
                });
                return;
            }
            setImageLoading(true);
            const formData = new FormData();
            formData.append('image', file);
            formData.append('fileName', `users/${item.id}`);

            try {
                const response = await fetch(`https://api.hollystone.com.tr/api/functions/upload`, {
                    method: 'POST',
                    headers: {
                        'Authorization': `Bearer ${localStorage.getItem('token')}`
                    },
                    body: formData,
                });

                if (response.ok) {
                    const data = await response.json();

                    if (data.type == "success") {
                        Swal.fire({
                            icon: 'success',
                            title: 'Başarılı',
                            text: 'Resim güncellendi!',
                        });
                        setSelectedImage(URL.createObjectURL(file));
                    } else {
                        Swal.fire({
                            icon: 'error',
                            title: 'Hata',
                            text: data.error,
                        });
                    }
                } else {
                    Swal.fire({
                        icon: 'error',
                        title: 'Hata',
                        text: 'Bir sorun meydana geldi!',
                    });
                }
            } catch (error) {
                Swal.fire({
                    icon: 'error',
                    title: 'Hata',
                    text: 'Bir sorun meydana geldi!',
                });
            }
            setImageLoading(false);
        }
    };

    const handleUpdate = async (event) => {
        event.preventDefault();
        try {
            const response = await fetch(`/api/users/update`, {
                method: 'PUT',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify(item),
            });

            if (response.ok) {
                await loadList();
                Swal.fire({
                    icon: 'success',
                    title: 'Başarılı',
                    text: 'Kayıt güncellendi!',
                });
            } else {
                Swal.fire({
                    icon: 'error',
                    title: 'Hata',
                    text: 'Bir sorun meydana geldi!',
                });
            }
        } catch (error) {
            console.error('An error occurred:', error);
        }
    }

    if (loading) {
        return "Loading";
    }

    return (
        <>
            <Head>
                <title>Kullanıcı - Düzenle</title>
            </Head>
            <PageTitleWrapper>
                <Grid container alignItems="center">
                    <Grid item>
                        <Typography variant="h3" component="h3" gutterBottom>
                            Kullanıcı Düzenle
                        </Typography>
                    </Grid>
                </Grid>
            </PageTitleWrapper>
            <Container maxWidth="lg">

                <Grid
                    container
                    direction="row"
                    justifyContent="center"
                    alignItems="stretch"
                    spacing={4}
                    style={{
                        marginBottom: 20
                    }}
                >
                    <Grid item xs={12}>
                        <Card className="p-4">
                            <div className="w-full mb-2 flex justify-between flex-row px-4">
                                <div className="mr-2">
                                    <Typography
                                        sx={{
                                            pb: 3
                                        }}
                                        variant="h4"
                                    >
                                        {`Biletler`}
                                    </Typography>
                                    <Typography
                                        variant="h1"
                                        gutterBottom
                                        sx={{
                                            color: "green"
                                        }}>
                                        {tickets.length ?? 0}
                                    </Typography>
                                </div>
                                <div className="ml-2 mr-2">
                                    <Typography
                                        sx={{
                                            pb: 3
                                        }}
                                        variant="h4"
                                    >
                                        {`Siparişler`}
                                    </Typography>
                                    <Typography
                                        variant="h1"
                                        gutterBottom
                                        sx={{
                                            color: "blue"
                                        }}>
                                        {orders.length ?? 0}
                                    </Typography>
                                </div>
                                <div className="ml-2">
                                    <Typography
                                        sx={{
                                            pb: 3
                                        }}
                                        variant="h4"
                                    >
                                        {`Holly Puan`}
                                    </Typography>
                                    <Typography
                                        variant="h1"
                                        gutterBottom
                                        sx={{
                                            color: "blue"
                                        }}>
                                        {item.hollyPoints ?? 0}
                                    </Typography>
                                </div>
                            </div>
                        </Card>
                    </Grid>
                </Grid>
                <Grid
                    container
                    direction="row"
                    justifyContent="center"
                    alignItems="stretch"
                    spacing={4}
                    style={{
                        marginBottom: 20
                    }}
                >
                    <Grid item xs={12}>
                        <Card
                            isBlurred
                            className="border-none bg-white dark:bg-default-100/50 max-w-[610px]"
                            shadow="sm"
                        >
                            <form onSubmit={handleUpdate}>
                                <CardHeader className="flex justify-between gap-3">
                                    <div>
                                        <Link href="/users">
                                            <Button type="button" color="default" startContent={<ArrowBackIcon />}>
                                                Kullanıcılar
                                            </Button>
                                        </Link>
                                    </div>
                                    <div>Kullanıcı Bilgileri</div>
                                    <div className="flex-end">
                                        <Dropdown>
                                            <DropdownTrigger>
                                                <Button
                                                    variant="bordered"
                                                    className={`text-white capitalize ${item.status ? "bg-success" : "bg-danger"}`}
                                                >
                                                    {dropdownItems[item.status?.toString()]}
                                                </Button>
                                            </DropdownTrigger>
                                            <DropdownMenu
                                                aria-label="Single selection example"
                                                variant="flat"
                                                disallowEmptySelection
                                                selectionMode="single"
                                                selectedKeys={[item.status?.toString()]}
                                                onSelectionChange={(selected: any) => {
                                                    setItem({ ...item, status: (/true/i).test(selected.currentKey) })
                                                }}
                                            >
                                                {Object.keys(dropdownItems).map((key) => (
                                                    <DropdownItem key={key}>{dropdownItems[key]}</DropdownItem>
                                                ))}
                                            </DropdownMenu>
                                        </Dropdown>
                                        <Button type="submit" color="primary">
                                            Güncelle
                                        </Button>
                                    </div>
                                </CardHeader>

                                <Divider />

                                <CardBody>
                                    <div className="flex">
                                        <div className="relative w-1/2">
                                            <label className="block cursor-pointer">
                                                <div className="relative">
                                                    {selectedImage ? (
                                                        <img
                                                            alt="Selected cover"
                                                            className="object-cover"
                                                            width={400}
                                                            src={selectedImage}
                                                            height="100%"
                                                        />
                                                    ) : (
                                                        <Image
                                                            alt="Album cover"
                                                            className="object-cover"
                                                            width={400}
                                                            shadow="md"
                                                            src={`https://api.hollystone.com.tr/resources/images/${item.image}`}
                                                            height="100%"
                                                        />
                                                    )}
                                                    {imageLoading ? (
                                                        <CircularProgress
                                                            size={48}
                                                            style={{
                                                                position: 'absolute',
                                                                top: '40%',
                                                                left: '40%',
                                                                transform: 'translate(-40%, -40%)',
                                                                zIndex: 999,
                                                            }}
                                                        />
                                                    ) : null}
                                                </div>
                                                <Input
                                                    type="file"
                                                    label="Resim"
                                                    name="image"
                                                    className="hidden"
                                                    accept="image/*"
                                                    onChange={handleImageChange}
                                                />
                                            </label>
                                        </div>

                                        <div className="p-4 w-full">
                                            <div className="w-full mb-2 flex flex-row">
                                                <Input
                                                    type="text"
                                                    label="İsim"
                                                    name="firstName"
                                                    value={item.firstName}
                                                    className="mr-2"
                                                    onChange={(e: any) => setItem({ ...item, firstName: e.target.value })}
                                                />
                                                <Input
                                                    type="text"
                                                    label="Soyisim"
                                                    name="lastName"
                                                    value={item.lastName}
                                                    onChange={(e: any) => setItem({ ...item, lastName: e.target.value })}
                                                />
                                            </div>
                                            <div className="w-full mb-2 flex flex-row">
                                                <Input
                                                    type="text"
                                                    label="Telefon"
                                                    name="phoneNumber"
                                                    value={item.phoneNumber}
                                                    className="mr-2"
                                                    onChange={(e: any) => setItem({ ...item, phoneNumber: e.target.value })}
                                                />
                                                <Input
                                                    type="text"
                                                    label="E-mail"
                                                    name="email"
                                                    value={item.email}
                                                    onChange={(e: any) => setItem({ ...item, email: e.target.value })}
                                                />
                                            </div>
                                            <Input
                                                type="date"
                                                label="Doğum Tarihi"
                                                name="dateOfBirth"
                                                value={item?.dateOfBirth?.toISOString()?.split('T')[0]} // Format as "YYYY-MM-DD"
                                                className="mb-2"
                                                onChange={(e: any) => setItem({ ...item, dateOfBirth: new Date(e.target.value) })}
                                            />
                                            <div className="w-full mb-2 flex flex-row">
                                                <Select
                                                    label="Şehir"
                                                    placeholder="Bir seçim yapın..."
                                                    className="mr-2"
                                                    defaultSelectedKeys={[item?.cityId?.toString()]}
                                                    onChange={(e: any) => {
                                                        setItem({ ...item, cityId: parseInt(e.target.value) });
                                                    }}
                                                >
                                                    {
                                                        cities.map((innerItem: any, index: number) => {
                                                            return (
                                                                <SelectItem key={innerItem.id} value={index}>{innerItem.title}</SelectItem>
                                                            )
                                                        })
                                                    }
                                                </Select>
                                                <Input
                                                    type="number"
                                                    label="Holly Puan"
                                                    placeholder="Holly Puan"
                                                    min={0}
                                                    name="hollyPoints"
                                                    value={item.hollyPoints}
                                                    onChange={(e: any) => setItem({ ...item, hollyPoints: e.target.value })}
                                                />
                                            </div>
                                        </div>
                                    </div>
                                </CardBody>
                            </form>
                        </Card>
                    </Grid>
                </Grid>
                <Grid
                    container
                    direction="row"
                    justifyContent="center"
                    alignItems="stretch"
                    spacing={4}
                    style={{
                        marginBottom: 20
                    }}
                >
                    <Grid item xs={12}>

                        <Card
                            isBlurred
                            className="border-none bg-white dark:bg-default-100/50 max-w-[610px]"
                            shadow="sm"
                        >
                            <CardHeader className="flex justify-between gap-3">
                                <div></div>
                                <div>Adres Bilgileri</div>
                                <div className="flex-end">
                                </div>
                            </CardHeader>

                            <Divider />

                            <CardBody>
                                <div className="flex">
                                    <div className="p-4 w-full">
                                        {
                                            addresses.map((item: any, index: number) => {
                                                return (
                                                    <Card key={index} className="max-w-[400px]">
                                                        <CardHeader className="flex gap-3">
                                                            <div className="flex flex-col">
                                                                <p className="text-md">{item.title}</p>
                                                            </div>
                                                        </CardHeader>
                                                        <Divider />
                                                        <CardBody>
                                                            {item.fullAddress} <br />
                                                            {item.neighborhood.title} MAH. {item.district.title}/{item.city.title}
                                                        </CardBody>
                                                    </Card>
                                                );
                                            })
                                        }

                                    </div>
                                </div>
                            </CardBody>
                        </Card>
                    </Grid>
                </Grid>
                <Grid
                    container
                    direction="row"
                    justifyContent="center"
                    alignItems="stretch"
                    spacing={4}
                    style={{
                        marginBottom: 20
                    }}
                >
                    <Grid item xs={12}>

                        <Card
                            isBlurred
                            className="border-none bg-white dark:bg-default-100/50 max-w-[610px]"
                            shadow="sm"
                        >
                            <CardHeader className="flex justify-between gap-3">
                                <div></div>
                                <div>Siparişler</div>
                                <div className="flex-end">
                                </div>
                            </CardHeader>

                            <Divider />

                            <CardBody>
                                <div className="flex">
                                    <div className="p-4 w-full">
                                        <Accordion defaultExpandedKeys={["0"]}>
                                            {
                                                orders.map((order, index) => {
                                                    return (
                                                        <AccordionItem
                                                            key={index}
                                                            aria-label={order.trackingNumber}
                                                            title={order.trackingNumber}
                                                            startContent={
                                                                (
                                                                    <div className="w-full mb-2 flex flex-row">
                                                                        <Link href={`/shop-orders/edit?id=${order.id}`} passHref>
                                                                            <Button type="button" color="primary">
                                                                                Detay
                                                                            </Button>
                                                                        </Link>
                                                                    </div>
                                                                )
                                                            }>
                                                            <div className="flex">
                                                                <div className="relative w-1/2" style={{
                                                                    minWidth: 400
                                                                }}>
                                                                    <Image
                                                                        width={400}
                                                                        height={400}
                                                                        radius="none"
                                                                        loading="eager"
                                                                        style={{
                                                                            height: "100%"
                                                                        }}
                                                                        src={`https://api.hollystone.com.tr/resources/images/${order.OrderItem[0]?.product?.images?.split(",")[0]}`}
                                                                        fallbackSrc="https://via.placeholder.com/400x400.png?text=Resim+Bulunamad%C4%B1"
                                                                    />
                                                                </div>
                                                                <div className="p-4 w-full">
                                                                    <div className="w-full mb-2 flex flex-row">
                                                                        <Input
                                                                            isReadOnly
                                                                            type="text"
                                                                            label="Kargo Firması"
                                                                            placeholder="Kargo Firması"
                                                                            name="carrier"
                                                                            value={order.carrier}
                                                                            className="mr-2"
                                                                        />
                                                                        <Input
                                                                            isReadOnly
                                                                            type="text"
                                                                            label="Kargo Takip No"
                                                                            placeholder="Kargo Takip No"
                                                                            name="deliveryTrackingNumber"
                                                                            value={order.deliveryTrackingNumber}
                                                                            className="mr-2"
                                                                        />
                                                                        <Input
                                                                            isReadOnly
                                                                            type="datetime-local"
                                                                            label="Tahmini Teslim Tarihi"
                                                                            placeholder="Tahmini Teslim Tarihi"
                                                                            name="estimatedDeliveryDate"
                                                                            value={order?.estimatedDeliveryDate?.toISOString()?.slice(0, 16)}
                                                                        />
                                                                    </div>
                                                                    <div className="w-full mb-2 flex flex-row">
                                                                        <Input
                                                                            isReadOnly
                                                                            type="text"
                                                                            label="Toplam Tutar"
                                                                            placeholder="Toplam Tutar"
                                                                            name="price"
                                                                            value={order.price}
                                                                            className="mr-2"
                                                                        />
                                                                        <Input
                                                                            isReadOnly
                                                                            type="text"
                                                                            label="Kullanılan Holly Puanlar"
                                                                            placeholder="Kullanılan Holly Puanlar"
                                                                            name="hollyPoints"
                                                                            value={order.hollyPoints}
                                                                            className="mr-2"
                                                                        />
                                                                        <Input
                                                                            isReadOnly
                                                                            type="text"
                                                                            label="Durum"
                                                                            placeholder="Durum"
                                                                            name="price"
                                                                            value={
                                                                                order.status == 1 ? "Hazırlanıyor"
                                                                                    : order.status == 2 ? "Kargoda"
                                                                                        : order.status == 3 ? "Teslim Edildi"
                                                                                            : order.status == 4 ? "İptal Edildi"
                                                                                                : "Bilinmiyor"
                                                                            }
                                                                        />
                                                                    </div>
                                                                </div>
                                                            </div>
                                                        </AccordionItem>
                                                    );
                                                })
                                            }
                                        </Accordion>
                                    </div>
                                </div>
                            </CardBody>
                        </Card>
                    </Grid >
                </Grid >
                <Grid
                    container
                    direction="row"
                    justifyContent="center"
                    alignItems="stretch"
                    spacing={4}
                >
                    <Grid item xs={12}>

                        <Card
                            isBlurred
                            className="border-none bg-white dark:bg-default-100/50 max-w-[610px]"
                            shadow="sm"
                        >
                            <CardHeader className="flex justify-between gap-3">
                                <div></div>
                                <div>Biletler</div>
                                <div className="flex-end">
                                </div>
                            </CardHeader>

                            <Divider />

                            <CardBody>
                                <div className="flex">
                                    <div className="p-4 w-full">
                                        <Accordion defaultExpandedKeys={["0"]}>
                                            {
                                                tickets.map((ticketItem, index) => {
                                                    return (
                                                        <AccordionItem key={index} aria-label={`${ticketItem.ticket?.concert?.name}-${index + 1}`} title={`${ticketItem.ticket?.concert?.name}-${index + 1}`}>
                                                            <div className="flex">
                                                                <div className="relative w-1/2" style={{
                                                                    minWidth: 200
                                                                }}>
                                                                    <Image
                                                                        width={200}
                                                                        height={200}
                                                                        radius="none"
                                                                        loading="eager"
                                                                        style={{
                                                                            height: "100%"
                                                                        }}
                                                                        src={`https://api.hollystone.com.tr/resources/images/${ticketItem.ticket?.concert?.image}`}
                                                                        fallbackSrc="https://via.placeholder.com/400x400.png?text="
                                                                    />
                                                                </div>
                                                                <div className="p-4 w-full">
                                                                    <div className="w-full mb-2 flex flex-row">
                                                                        <Input
                                                                            isReadOnly
                                                                            type="text"
                                                                            label="Konser"
                                                                            placeholder="Konser"
                                                                            name="carrier"
                                                                            value={ticketItem.ticket?.concert?.name}
                                                                            className="mr-2"
                                                                        />
                                                                        <Input
                                                                            isReadOnly
                                                                            type="datetime-local"
                                                                            label="Konser Tarihi"
                                                                            placeholder="Konser Tarihi"
                                                                            name="estimatedDeliveryDate"
                                                                            value={ticketItem.ticket?.concert?.date?.slice(0, 16)}
                                                                        />
                                                                    </div>
                                                                    <div className="w-full mb-2 flex flex-row">
                                                                        <Input
                                                                            isReadOnly
                                                                            type="text"
                                                                            label="Fiyat"
                                                                            placeholder="Fiyat"
                                                                            name="carrier"
                                                                            value={ticketItem.paidPrice}
                                                                            className="mr-2"
                                                                        />
                                                                        <Input
                                                                            isReadOnly
                                                                            type="text"
                                                                            label="Durum"
                                                                            placeholder="Durum"
                                                                            name="carrier"
                                                                            value={
                                                                                ticketItem.status == 1 ? "Aktif"
                                                                                    : ticketItem.status == 2 ? "Kullanılmış"
                                                                                        : ticketItem.status == 3 ? "İptal Edilmiş"
                                                                                            : ticketItem.status == 4 ? "İade Edilmiş"
                                                                                                : "Bilinmiyor"
                                                                            }
                                                                            className="mr-2"
                                                                        />
                                                                    </div>
                                                                </div>
                                                            </div>
                                                        </AccordionItem>
                                                    );
                                                })
                                            }
                                        </Accordion>
                                    </div>
                                </div>
                            </CardBody>
                        </Card>
                    </Grid >
                </Grid >
            </Container >
            <Footer />
        </>
    );
};

EditSnapPage.getLayout = (page: React.ReactNode) => <SidebarLayout>{page}</SidebarLayout>;

export const getServerSideProps = authMiddleware(async (_context: any) => {
    return {
        props: {},
    };
});

export default EditSnapPage;
