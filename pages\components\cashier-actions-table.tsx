import React, { useState, useMemo, useCallback, useEffect } from "react";
import {
    Table,
    TableHeader,
    TableColumn,
    TableBody,
    TableRow,
    TableCell,
    Input,
    Button,
    Pagination,
    Spinner,
    getKeyValue
} from "@nextui-org/react";
import { Select, SelectItem } from "@nextui-org/select";
import { SearchIcon } from "../../src/components/SearchIcon";
import { withRouter } from 'next/router';
import BackspaceIcon from '@mui/icons-material/Backspace';
import Swal from 'sweetalert2';

function CashierActionsTable() {
    const [filterValue, setFilterValue] = useState("");
    const [page, setPage] = useState(1);
    const [total, setTotal] = useState(0);
    const [vendorId, setVendorId] = useState(0);
    const [isLoading, setIsLoading] = useState(true);
    const [items, setItems] = useState([]);
    const [vendors, setVendors] = useState([]);
    const [dateRange, setDateRange] = useState(0);
    const dropdownItems = [
        "Yıllık",
        "Aylı<PERSON>",
        "Haftalık"
    ];

    const rowsPerPage = 10;

    const loadList = async (pageInput: number, filter: string) => {
        const url = `/api/casier-actions?page=${pageInput}&pageSize=${rowsPerPage}&filter=${filter}&dateRange=${dateRange}`;

        try {
            const res = await fetch(url);

            if (res.status === 200) {
                const json = await res.json();

                setTotal(json.totalCount);
                setIsLoading(false);
                setItems(json.results);
            } else {
                setIsLoading(false);
                setItems([]);
            }
        } catch (error) {
            setIsLoading(false);
            setItems([]);
            console.error(error);
            throw error;
        }
    };

    const loadVendors = async () => {
        const url = `/api/vendors`;

        try {
            const res = await fetch(url);

            if (res.status === 200) {
                const json = await res.json();
                setVendors(json.results);
            } else {
                Swal.fire({
                    icon: 'error',
                    title: 'Hata',
                    text: 'Bir sorun meydana geldi!',
                });
            }
        } catch (error) {
            console.error(error);
        }
    };

    useEffect(() => {
        loadVendors();
    }, []);

    useEffect(() => {
        loadList(1, filterValue);
    }, [dateRange]);

    const undo = (id) => {
        console.log(id);
    }

    const pages = Math.ceil(total / rowsPerPage);

    const onPaginationChange = useCallback(
        async (pageInput) => {
            setIsLoading(true);
            setPage(pageInput);
            await loadList(pageInput, filterValue);
        },
        []
    );

    const onSearchChange = useCallback(
        (value: string) => {
            if (value) {
                setIsLoading(true);
                setFilterValue(value);
                loadList(page, value);
                setPage(1);
            } else {
                setFilterValue("");
                loadList(page, "");
            }
        },
        []
    );

    const onClear = useCallback(() => {
        setFilterValue("");
        setPage(1);
    }, []);

    const topContent = useMemo(() => {
        return (
            <div className="flex flex-row gap-4">
                <Input
                    isClearable
                    className="w-full sm:max-w-[44%]"
                    placeholder="Arayın..."
                    startContent={<SearchIcon />}
                    value={filterValue}
                    onClear={() => onClear()}
                    onValueChange={onSearchChange}
                />
                <div className="flex gap-2 items-center justify-end">
                    <Select
                        placeholder="Bir seçim yapın..."
                        labelPlacement="outside"
                        defaultSelectedKeys={[dateRange?.toString()]}
                        onSelectionChange={(e: any) => {
                            setDateRange(parseInt(e.currentKey));
                        }}
                        style={{
                            minWidth: "150px"
                        }}
                        className="mr-2"
                    >
                        {dropdownItems.map((dItem: any, dIndex: any) => {
                            return (
                                <SelectItem key={dIndex} value={dIndex}>
                                    {dItem}
                                </SelectItem>
                            )
                        })}
                    </Select>
                    <Select
                        placeholder="Bir seçim yapın..."
                        labelPlacement="outside"
                        defaultSelectedKeys={[vendorId?.toString()]}
                        onSelectionChange={(e: any) => {
                            setVendorId(parseInt(e.target.value));
                        }}
                        style={{
                            minWidth: "150px"
                        }}
                    >
                        {vendors.map((innerItem: any, index: number) => {
                            return (
                                <SelectItem key={innerItem.id} value={index}>{innerItem.name}</SelectItem>
                            )
                        })}
                    </Select>
                </div>
            </div>
        );
    }, [
        filterValue,
        onSearchChange,
        onClear,
        vendors
    ]);

    const renderCell = useCallback((cellValue: any, columnKey: any, rowId: any) => {
        const cell = items.find(item => item.id === rowId);

        if (!cell) {
            // Handle the loading state while waiting for the cell data to be available.
            return <div>Loading...</div>;
        }

        switch (columnKey) {
            case "admin":
                return cellValue?.username;
            case "user":
                return `${cellValue?.firstName} ${cellValue?.lastName}`;
            case "vendor":
                return cell?.admin?.vendor?.name;
            case "actions":
                return (
                    <div className="relative flex justify-end items-center gap-2">
                        <Button
                            onClick={() => undo(rowId)}
                            color="primary"
                            endContent={<BackspaceIcon />}
                        >
                            Geri Al
                        </Button>
                    </div>
                );
            default:
                return cellValue;
        }
    }, [items]);

    return (
        <Table
            aria-label="Example table with client async pagination"
            topContent={topContent}
            topContentPlacement="inside"
            bottomContent={
                pages > 0 ? (
                    <div className="flex w-full justify-center">
                        <Pagination
                            isCompact
                            showControls
                            showShadow
                            color="primary"
                            page={page}
                            total={pages}
                            onChange={onPaginationChange}
                        />
                    </div>
                ) : null
            }
            classNames={{
                table: "min-h-[400px]",
            }}
        >
            <TableHeader>
                <TableColumn key="vendor">Bayi</TableColumn>
                <TableColumn key="admin">Kasiyer</TableColumn>
                <TableColumn key="user">Müşteri</TableColumn>
                <TableColumn key="type">İşlem Tipi</TableColumn>
                <TableColumn key="amount">Miktar</TableColumn>
                <TableColumn key="actions">İşlemler</TableColumn>
            </TableHeader>
            <TableBody
                isLoading={isLoading && !items.length}
                items={items}
                loadingContent={<Spinner />}
                emptyContent={"Kayıt bulunamadı!"}
            >
                {(item) => (
                    <TableRow key={item.id}>
                        {(columnKey) => (
                            <TableCell key={columnKey + item.id}>{renderCell(getKeyValue(item, columnKey), columnKey, item.id)}</TableCell>
                        )}
                    </TableRow>
                )}
            </TableBody>
        </Table>
    );
}

export default withRouter(CashierActionsTable);
