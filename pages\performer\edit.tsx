import React, { useState, useEffect } from 'react';
import Head from 'next/head';
import { useRouter } from 'next/router';
import { authMiddleware } from 'middleware';
import Swal from 'sweetalert2';
import { Container, Grid, Typography } from '@mui/material';
import {
    Card,
    CardBody,
    CardHeader,
    Button,
    Input,
    Divider,
    Accordion,
    AccordionItem,
    Textarea,
} from '@nextui-org/react';
import YouTube from 'react-youtube';

import SidebarLayout from '@/layouts/SidebarLayout';
import PageTitleWrapper from '@/components/PageTitleWrapper';
import Footer from '@/components/Footer';
import ArrowBackIcon from '@mui/icons-material/ArrowBack';
import Link from 'next/link';

const EditPerformerPage = () => {
    const router = useRouter();
    const [item, setItem]: any = useState({});
    const { id } = router.query;
    const [loading, setLoading] = useState(true);

    const loadList = async () => {
        const url = `/api/performer?id=${id}`;

        try {
            const res = await fetch(url);

            if (res.status === 200) {
                const json = await res.json();
                json.results[0].videos = json.results[0]?.videos.split(',');
                setItem(json.results[0]);
            } else {
                Swal.fire({
                    icon: 'error',
                    title: 'Hata',
                    text: 'Bir sorun meydana geldi!',
                });
            }
        } catch (error) {
            console.error(error);
        } finally {
            setLoading(false);
        }
    };

    const fetchList = async () => {
        await loadList();
    };

    useEffect(() => {
        fetchList();
    }, []);

    const handleDelete = async () => {
        const shouldDelete = await Swal.fire({
            title: 'Dikkat!',
            text: 'Bu kaydı silmek istediğine gerçekten emin misin?',
            icon: 'warning',
            showCancelButton: true,
            confirmButtonText: 'Evet, sil!',
            cancelButtonText: 'Vazgeç',
        });

        if (shouldDelete.isConfirmed) {
            try {
                const response = await fetch(`/api/performer/delete`, {
                    method: 'DELETE',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({ id: id }),
                });

                if (response.ok) {
                    router.push('/performer');
                    Swal.fire({
                        icon: 'success',
                        title: 'Başarılı',
                        text: 'Kayıt silindi!',
                    });
                } else {
                    Swal.fire({
                        icon: 'error',
                        title: 'Hata',
                        text: 'Bir sorun meydana geldi!',
                    });
                }
            } catch (error) {
                console.error('An error occurred:', error);
            }
        }
    };

    const handleAnswer = (rowId) => {
        router.push('/performer/answer?id=' + rowId);
    };

    if (loading) {
        return 'Loading';
    }

    return (
        <>
            <Head>
                <title>Sanatçı Başvuru - Detay</title>
            </Head>
            <PageTitleWrapper>
                <Grid container alignItems="center">
                    <Grid item>
                        <Typography variant="h3" component="h3" gutterBottom>
                            Sanatçı Başvuru Detay
                        </Typography>
                    </Grid>
                </Grid>
            </PageTitleWrapper>
            <Container maxWidth="lg">
                <Grid
                    container
                    direction="row"
                    justifyContent="center"
                    alignItems="stretch"
                    spacing={4}
                >
                    <Grid item xs={12}>
                        <Card
                            isBlurred
                            className="border-none bg-white dark:bg-default-100/50 max-w-[610px]"
                            shadow="sm"
                        >
                            <CardHeader className="flex justify-between gap-3">
                                <div>
                                    <Link href="/performer">
                                        <Button type="button" color="default" startContent={<ArrowBackIcon />}>
                                            Başvurular
                                        </Button>
                                    </Link>
                                </div>
                                <div>Sanatçı Başvuru Bilgileri</div>
                                <div className="flex-end">
                                    <Button
                                        type="button"
                                        onClick={handleDelete}
                                        color="danger"
                                        className="mr-2"
                                    >
                                        Sil
                                    </Button>
                                    <Button
                                        type="button"
                                        onClick={() => handleAnswer(item.id)}
                                        color="primary"
                                    >
                                        Cevapla
                                    </Button>
                                </div>
                            </CardHeader>

                            <Divider />

                            <CardBody>
                                <div className="flex">
                                    <div
                                        className="relative w-1/2"
                                        style={{
                                            minWidth: 400,
                                        }}
                                    >
                                        <Accordion defaultExpandedKeys={["0"]}>
                                            {item.videos.map((video, index) => {
                                                const match = video.match(
                                                    /(?:youtube\.com\/watch\?v=|youtu\.be\/)([^&]+)/
                                                );
                                                return (
                                                    <AccordionItem
                                                        key={index}
                                                        aria-label={`Video ${index + 1}`}
                                                        title={`Video ${index + 1}`}
                                                    >
                                                        {match ? (
                                                            <YouTube
                                                                videoId={match[1]}
                                                                opts={{ width: '100%' }}
                                                            />
                                                        ) : (
                                                            <Link href={video} passHref>
                                                                <a target="_blank" rel="noopener noreferrer">
                                                                    <Button color="primary">Videoyu aç</Button>
                                                                </a>
                                                            </Link>
                                                        )}
                                                    </AccordionItem>
                                                );
                                            })}
                                        </Accordion>
                                    </div>

                                    <div className="p-4 w-full">
                                        <div className="w-full mb-2 flex flex-row">
                                            <Input
                                                isReadOnly
                                                type="text"
                                                label="Ad Soyad"
                                                placeholder="Ad Soyad"
                                                name="nameLastName"
                                                value={item.nameLastName}
                                                className="mr-2"
                                            />
                                            <Input
                                                isReadOnly
                                                type="text"
                                                label="Telefon Numarası"
                                                placeholder="Telefon Numarası"
                                                name="phoneNumber"
                                                value={item.phoneNumber}
                                            />
                                        </div>
                                        <div className="w-full mb-2 flex flex-row">
                                            <Input
                                                isReadOnly
                                                type="text"
                                                label="Şehir"
                                                placeholder="Şehir"
                                                name="cityName"
                                                value={item.cityName}
                                            />
                                        </div>
                                        <Textarea
                                            isReadOnly
                                            label="Açıklama"
                                            placeholder="Açıklama"
                                            name="description"
                                            className="mb-2"
                                            value={item.description}
                                        ></Textarea>
                                    </div>
                                </div>
                            </CardBody>
                        </Card>
                    </Grid>
                </Grid>
            </Container>
            <Footer />
        </>
    );
};

EditPerformerPage.getLayout = (page: React.ReactNode) => (
    <SidebarLayout>{page}</SidebarLayout>
);

export const getServerSideProps = authMiddleware(async (_context: any) => {
    return {
        props: {},
    };
});

export default EditPerformerPage;
