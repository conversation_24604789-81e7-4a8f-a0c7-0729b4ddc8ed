import { PrismaClient } from '@prisma/client';

const prisma = new PrismaClient();

export default async function handler(req, res) {
    if (req.method !== 'GET') {
        return res.status(405).json({ message: 'Method not allowed' });
    }

    const { id } = req.query;

    try {
        const userOrders = await prisma.shopOrder.findMany({
            where: {
                userId: parseInt(id),
                payment: true
            },
            include: {
                OrderItem: {
                    include: {
                        product: true,
                    }
                },
                address: true
            },
            orderBy: {
                createdAt: 'desc'
            },
        });

        // Close the Prisma client connection
        await prisma.$disconnect();

        res.status(200).json({ results: userOrders });
    } catch (error) {
        console.error(error);
        // Close the Prisma client connection
        await prisma.$disconnect();
        res.status(500).json({ message: 'Internal server error' });
    }
}
