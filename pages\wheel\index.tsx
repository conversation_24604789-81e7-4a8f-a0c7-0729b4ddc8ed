import React, { useState, useEffect } from 'react';
import Head from 'next/head';
import { Container } from '@mui/material';
import { Card, CardBody, CardHeader, Dropdown, DropdownTrigger, DropdownMenu, DropdownItem, Button, Input, Image, Divider } from '@nextui-org/react';
import { CircularProgress } from '@mui/material';
import Swal from 'sweetalert2';
import SidebarLayout from '@/layouts/SidebarLayout';
import Footer from '@/components/Footer';

interface Prize {
    id: number;
    name: string;
    image: string;
    sponsorImage?: string; // Sponsor resmi için yeni alan
    type: 'product' | 'hollyPoints' | 'ticket';
    bigPrize: boolean;
    percentage: number;
    number?: number;
    remaining?: number;
}

const WheelPage = () => {
    const [prizes, setPrizes] = useState<Prize[]>([]);
    const [loading, setLoading] = useState(true);
    const [imageLoading, setImageLoading] = useState<number | null>(null);

    const dropdownItems = {
        product: "<PERSON>rün",
        hollyPoints: "Holly Puan",
        ticket: "Bilet"
    };

    const loadPrizes = async () => {
        setLoading(true);
        try {
            const res = await fetch('/api/wheel');
            if (!res.ok) throw new Error('API yanıtı başarısız');

            const data = await res.json();
            setPrizes(data.results || []);
        } catch (error) {
            console.error('Yükleme hatası:', error);
            Swal.fire({
                icon: 'error',
                title: 'Hata',
                text: 'Veriler yüklenirken bir sorun oluştu!'
            });
        } finally {
            setLoading(false);
        }
    };

    useEffect(() => {
        loadPrizes();
    }, []);

    const handleImageChange = async (event: React.ChangeEvent<HTMLInputElement>, prizeId: number) => {
        const file = event.target.files?.[0];
        if (!file) return;

        if (!file.type.startsWith('image/')) {
            Swal.fire({
                icon: 'error',
                title: 'Hata',
                text: 'Seçilen format desteklenmiyor!'
            });
            return;
        }

        setImageLoading(prizeId);
        const formData = new FormData();
        formData.append('image', file);
        formData.append('fileName', prizes.find(p => p.id === prizeId)?.image?.split('.')[0] || '');

        try {
            // 1. Resmi sunucuya yükle
            const response = await fetch('https://api.hollystone.com.tr/api/functions/upload', {
                method: 'POST',
                headers: {
                    'Authorization': `Bearer ${localStorage.getItem('token')}`
                },
                body: formData
            });

            const data = await response.json();
            if (data.type === "success") {
                // 2. Yerel state'i güncelle
                const updatedPrize = prizes.find(p => p.id === prizeId);
                if (!updatedPrize) throw new Error('Ödül bulunamadı');

                const updatedPrizes = prizes.map(p =>
                    p.id === prizeId ? { ...p, image: file.name } : p
                );

                setPrizes(updatedPrizes);

                

                // 3. Veritabanını güncelle
                const updateResponse = await fetch('/api/wheel/update', {
                    method: 'PUT',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        id: updatedPrize.id,
                        name: updatedPrize.name,
                        image: file.name,
                        sponsorImage: updatedPrize.sponsorImage,
                        type: updatedPrize.type,
                        bigPrize: updatedPrize.bigPrize,
                        percentage: updatedPrize.percentage,
                        number: updatedPrize.number || 0,
                        remaining: updatedPrize.remaining || 0
                    })
                });

                if (!updateResponse.ok) {
                    throw new Error('Veritabanı güncellenemedi');
                }

                Swal.fire({
                    icon: 'success',
                    title: 'Başarılı',
                    text: 'Resim güncellendi!'
                });
            } else {
                throw new Error(data.error);
            }
        } catch (error) {
            console.error('Resim yükleme hatası:', error);
            Swal.fire({
                icon: 'error',
                title: 'Hata',
                text: 'Resim yüklenirken bir sorun oluştu!'
            });
        } finally {
            setImageLoading(null);
        }
    };

    const handleUpdate = async () => {
        try {
            const totalPercentage = prizes.reduce((sum, prize) => sum + prize.percentage, 0);
            if (Math.abs(totalPercentage - 100) > 0.01) {
                Swal.fire({
                    icon: 'error',
                    title: 'Hata',
                    text: 'Yüzdelerin toplamı 100 olmalıdır!'
                });
                return;
            }

            const updatePromises = prizes.map(prize =>
                fetch('/api/wheel/update', {
                    method: 'PUT',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify(prize)
                })
            );

            const results = await Promise.all(updatePromises);
            const allSuccessful = results.every(res => res.ok);

            if (allSuccessful) {
                Swal.fire({
                    icon: 'success',
                    title: 'Başarılı',
                    text: 'Tüm ödüller güncellendi!'
                });
                await loadPrizes();
            } else {
                throw new Error('Bazı ödüller güncellenemedi');
            }
        } catch (error) {
            console.error('Güncelleme hatası:', error);
            Swal.fire({
                icon: 'error',
                title: 'Hata',
                text: 'Güncelleme sırasında bir sorun oluştu!'
            });
        }
    };

    const handleSponsorImageChange = async (event: React.ChangeEvent<HTMLInputElement>, prizeId: number) => {
        const file = event.target.files?.[0];
        if (!file) return;
    
        if (!file.type.startsWith('image/')) {
            Swal.fire({
                icon: 'error',
                title: 'Hata',
                text: 'Seçilen format desteklenmiyor!'
            });
            return;
        }
    
        setImageLoading(prizeId);
        const formData = new FormData();
        formData.append('image', file);
        
        // ÖNEMLİ DEĞİŞİKLİK: Dosya adını belirlerken .webp uzantısını ekle
        const fileName = `sponsor_${prizes.find(p => p.id === prizeId)?.id || ''}`;
        formData.append('fileName', fileName);
    
        try {
            // 1. Resmi sunucuya yükle
            const response = await fetch('https://api.hollystone.com.tr/api/functions/upload', {
                method: 'POST',
                headers: {
                    'Authorization': `Bearer ${localStorage.getItem('token')}`
                },
                body: formData
            });
    
            const data = await response.json();
            if (data.type === "success") {
                // 2. Yerel state'i güncelle - dosya adını .webp olarak kaydet
                const updatedPrize = prizes.find(p => p.id === prizeId);
                if (!updatedPrize) throw new Error('Ödül bulunamadı');
    
                // ÖNEMLİ DEĞİŞİKLİK: Veritabanına kaydedilen dosya adını .webp uzantılı olarak ayarla
                const updatedPrizes = prizes.map(p =>
                    p.id === prizeId ? { ...p, sponsorImage: `${fileName}.webp` } : p
                );
    
                setPrizes(updatedPrizes);
    
                // 3. Veritabanını güncelle
                const updateResponse = await fetch('/api/wheel/update', {
                    method: 'PUT',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        id: updatedPrize.id,
                        name: updatedPrize.name,
                        image: updatedPrize.image,
                        // ÖNEMLİ DEĞİŞİKLİK: .webp uzantılı dosya adını gönder
                        sponsorImage: `${fileName}.webp`,
                        type: updatedPrize.type,
                        bigPrize: updatedPrize.bigPrize,
                        percentage: updatedPrize.percentage,
                        number: updatedPrize.number || 0,
                        remaining: updatedPrize.remaining || 0
                    })
                });
    
                if (!updateResponse.ok) {
                    throw new Error('Veritabanı güncellenemedi');
                }
    
                Swal.fire({
                    icon: 'success',
                    title: 'Başarılı',
                    text: 'Sponsor resmi güncellendi!'
                });
            } else {
                throw new Error(data.error);
            }
        } catch (error) {
            console.error('Sponsor resmi yükleme hatası:', error);
            Swal.fire({
                icon: 'error',
                title: 'Hata',
                text: 'Resim yüklenirken bir sorun oluştu!'
            });
        } finally {
            setImageLoading(null);
        }
    };

    if (loading) {
        return (
            <div className="flex justify-center items-center h-screen">
                <CircularProgress />
            </div>
        );
    }

    return (
        <>
            <Head>
                <title>Ödül Çarkı - Düzenle</title>
            </Head>


            <Container maxWidth="lg">
                <div className="mb-4">
                    <Button
                        color="primary"
                        size="lg"
                        onClick={handleUpdate}
                    >
                        Tüm Değişiklikleri Kaydet
                    </Button>
                </div>

                <Card className="mb-6">
                    <CardHeader className="flex justify-between items-center px-6 py-4">
                        <div className="flex items-center">
                            <h3 className="text-xl font-semibold">Ödül Listesi</h3>
                            <span className="ml-2 text-gray-400">({prizes.length} ödül)</span>
                        </div>
                    </CardHeader>
                    <Divider />
                    <CardBody>
                        <div className="space-y-4">
                            {prizes.map((prize) => (
                                <Card key={prize.id} shadow="sm" className="border border-gray-200">
                                    <CardBody>
                                        <div className="flex gap-6">
                                            {/* Sol Taraf - Resimler */}
                                            <div className="flex flex-col gap-2">
                                                {/* Ödül Resmi */}
                                                <div className="w-24 h-24 flex-shrink-0">
                                                    <label className="block cursor-pointer relative">
                                                        <Image
                                                            src={`https://api.hollystone.com.tr/resources/images/${prize.image}`}
                                                            alt={prize.name}
                                                            className="w-24 h-24 object-cover rounded-lg"
                                                        />
                                                        {imageLoading === prize.id && (
                                                            <div className="absolute inset-0 flex items-center justify-center bg-black/50 rounded-lg">
                                                                <CircularProgress />
                                                            </div>
                                                        )}
                                                        <Input
                                                            type="file"
                                                            className="hidden"
                                                            accept="image/*"
                                                            onChange={(e) => handleImageChange(e, prize.id)}
                                                        />
                                                    </label>
                                                    <div className="text-xs text-center mt-1">Ödül Resmi</div>
                                                </div>

                                                {/* Sponsor Resmi */}
                                                <div className="w-24 h-24 flex-shrink-0">
                                                    <label className="block cursor-pointer relative">
                                                        <Image
                                                            src={prize.sponsorImage ? `https://api.hollystone.com.tr/resources/images/${prize.sponsorImage}` : '/static/images/placeholder.png'}
                                                            alt={`${prize.name} sponsor`}
                                                            className="w-24 h-24 object-cover rounded-lg"
                                                        />
                                                        {imageLoading === prize.id && (
                                                            <div className="absolute inset-0 flex items-center justify-center bg-black/50 rounded-lg">
                                                                <CircularProgress />
                                                            </div>
                                                        )}
                                                        <Input
                                                            type="file"
                                                            className="hidden"
                                                            accept="image/*"
                                                            onChange={(e) => handleSponsorImageChange(e, prize.id)}
                                                        />
                                                    </label>
                                                    <div className="text-xs text-center mt-1">Sponsor Resmi</div>
                                                </div>
                                            </div>

                                            {/* Sağ Taraf - Bilgiler */}
                                            <div className="flex-grow flex gap-4 items-center">
                                                <div className="w-1/4">
                                                    <Input
                                                        label="Ödül Adı"
                                                        value={prize.name}
                                                        className="w-full"
                                                        size="sm"
                                                        onChange={(e) => {
                                                            setPrizes(prizes.map(p =>
                                                                p.id === prize.id ? { ...p, name: e.target.value } : p
                                                            ));
                                                        }}
                                                    />
                                                </div>
                                                <div className="w-1/5">
                                                    <Dropdown>
                                                        <DropdownTrigger>
                                                            <Button variant="bordered" className="w-full" size="sm">
                                                                {dropdownItems[prize.type]}
                                                            </Button>
                                                        </DropdownTrigger>
                                                        <DropdownMenu
                                                            selectedKeys={[prize.type]}
                                                            onSelectionChange={(keys) => {
                                                                const type = Array.from(keys)[0] as Prize['type'];
                                                                setPrizes(prizes.map(p =>
                                                                    p.id === prize.id ? { ...p, type } : p
                                                                ));
                                                            }}
                                                        >
                                                            {Object.entries(dropdownItems).map(([key, value]) => (
                                                                <DropdownItem key={key}>{value}</DropdownItem>
                                                            ))}
                                                        </DropdownMenu>
                                                    </Dropdown>
                                                </div>
                                                <div className="w-1/6">
                                                    <Input
                                                        type="number"
                                                        label="Yüzde (%)"
                                                        min="0"
                                                        max="100"
                                                        value={prize.percentage?.toString() || ''}
                                                        className="w-full"
                                                        size="sm"
                                                        onChange={(e) => {
                                                            setPrizes(prizes.map(p =>
                                                                p.id === prize.id ? { ...p, percentage: Number(e.target.value) } : p
                                                            ));
                                                        }}
                                                    />
                                                </div>
                                                <div className="w-1/6">
                                                    <Input
                                                        type="number"
                                                        label="Kalan Adet"
                                                        min="0"
                                                        value={prize.remaining?.toString() || ''}
                                                        className="w-full"
                                                        size="sm"
                                                        onChange={(e) => {
                                                            setPrizes(prizes.map(p =>
                                                                p.id === prize.id ? { ...p, remaining: Number(e.target.value) } : p
                                                            ));
                                                        }}
                                                    />
                                                </div>
                                                <div className="w-1/6">
                                                    <Input
                                                        type="number"
                                                        label="Toplam Adet"
                                                        min="0"
                                                        value={prize.number?.toString() || ''}
                                                        className="w-full"
                                                        size="sm"
                                                        onChange={(e) => {
                                                            setPrizes(prizes.map(p =>
                                                                p.id === prize.id ? { ...p, number: Number(e.target.value) } : p
                                                            ));
                                                        }}
                                                    />
                                                </div>
                                            </div>
                                        </div>
                                    </CardBody>
                                </Card>
                            ))}
                        </div>
                    </CardBody>
                </Card>
            </Container>
            <Footer />
        </>
    );
};

WheelPage.getLayout = (page: React.ReactNode) => <SidebarLayout>{page}</SidebarLayout>;

export default WheelPage;

