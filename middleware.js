'use client'
import { verifyToken } from './auth';

export const authMiddleware = (handler) => async (context) => {
    const { req, res } = context;

    const moduleList = [
        "shop"
    ]
    
    const token = req.cookies.token;

    if (!token) {
        return {
            redirect: {
                destination: '/login',
                permanent: false,
            },
        };
    }

    const userId = verifyToken(token);

    if (!userId) {
        return {
            redirect: {
                destination: '/login',
                permanent: false,
            },
        };
    }

    const authorityLevel = req.cookies.authorityLevel;

    if (!authorityLevel) {
        return {
            redirect: {
                destination: '/login',
                permanent: false,
            },
        };
    }

    const modules = req.cookies.modules;
    const url = req.url;

    if (authorityLevel != 1) {
        const moduleIndex = moduleList.findIndex((module) => url.includes(module))

        if (moduleIndex == -1) {
            return await handler(context, userId);
        }

        if (!modules) {
            return {
                redirect: {
                    destination: '/not-authorized',
                    permanent: false,
                },
            };
        }

        const modulesArray = modules.split(",");

        const authorized = modulesArray.find((module) => module == moduleIndex)

        if (!authorized) {
            return {
                redirect: {
                    destination: '/not-authorized',
                    permanent: false,
                },
            };
        }
    }

    return await handler(context, userId);
};

export const authMiddlewareLogin = (handler) => async (context) => {
    const { req, res } = context;

    const token = req.cookies.token;

    if (!token) {
        return await handler(context)
    }

    const userId = verifyToken(token);

    if (!userId) {
        return await handler(context)
    }

    return {
        redirect: {
            destination: '/',
            permanent: false,
        },
    };
};