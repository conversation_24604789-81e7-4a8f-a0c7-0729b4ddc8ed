import { PrismaClient } from '@prisma/client';
import bcrypt from "bcrypt";

const prisma = new PrismaClient();

export default async function handler(req, res) {
    if (req.method === 'POST') {
        const { vendorId, username, newPassword, email, phone, identityNumber, authorityLevel } = req.body;

        try {
            let hashedPassword = undefined;

            if (newPassword) {
                if (newPassword.length < 8) {
                    return res.status(400).json({ message: 'Şifre en az 8 karakter olmalıdır!' });
                } else {
                    hashedPassword = await bcrypt.hash(newPassword, 10);
                }
            } else {
                return res.status(400).json({ message: '<PERSON><PERSON><PERSON> zorunludur!' });
            }
            const createdAdmin = await prisma.admin.create({
                data: {
                    vendorId,
                    username,
                    email,
                    phone,
                    identityNumber,
                    authorityLevel,
                    password: hashedPassword,
                    isDeleted: false
                },
            });

            res.status(201).json({ message: '<PERSON><PERSON> eklendi!', createdItem: createdAdmin });
        } catch (error) {
            console.error(error);
            res.status(500).json({ message: 'Internal server error', error });
        }
    } else {
        res.status(405).json({ message: 'Method not allowed' });
    }
}
