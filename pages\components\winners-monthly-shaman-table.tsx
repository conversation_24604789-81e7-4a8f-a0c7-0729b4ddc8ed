import React, { useState, useMemo, useCallback, useEffect } from "react";
import {
    Table,
    TableHeader,
    TableColumn,
    TableBody,
    TableRow,
    TableCell,
    Input,
    Button,
    Pagination,
    Spinner,
    getKeyValue,
    User,
} from "@nextui-org/react";
import { SearchIcon } from "../../src/components/SearchIcon";
import Swal from 'sweetalert2';
import { withRouter } from 'next/router';
import dayjs from "dayjs";

function WinnersWeeklyShamanTable() {
    const [filterValue, setFilterValue] = useState("");
    const [page, setPage] = useState(1);
    const [total, setTotal] = useState(0);
    const [isLoading, setIsLoading] = useState(true);
    const [items, setItems] = useState([]);

    const rowsPerPage = 10;

    const loadList = async (pageInput: number, filter: string) => {
        const url = `/api/winners-monthly-shaman?page=${pageInput}&pageSize=${rowsPerPage}&filter=${filter}`;

        try {
            const res = await fetch(url);

            if (res.status === 200) {
                const json = await res.json();

                setTotal(json.totalCount);
                setIsLoading(false);
                setItems(json.results);
            } else {
                setIsLoading(false);
                setItems([]);
            }
        } catch (error) {
            setIsLoading(false);
            setItems([]);
            console.error(error);
            throw error;
        }
    };

    useEffect(() => {
        loadList(1, filterValue);
    }, []);

    const deliver = async (id: any) => {
        try {
            const res = await fetch('/api/winners-monthly-shaman/deliver', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({ id })
            });
            if (res.status === 200) {
                Swal.fire({
                    icon: 'success',
                    title: 'Başarılı',
                    text: 'Hediye teslim edildi!',
                }).then(() => {
                    loadList(page, filterValue);
                })
            } else {
                Swal.fire({
                    icon: 'error',
                    title: 'Hata',
                    text: 'Bir sorun meydana geldi!',
                });
            }
        } catch (error) {
            console.error(error);
        }
    }

    const cancel = async (id: any) => {
        try {
            const res = await fetch('/api/winners-monthly-shaman/cancel', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({ id })
            });
            if (res.status === 200) {
                Swal.fire({
                    icon: 'success',
                    title: 'Başarılı',
                    text: 'Teslimat iptal edildi!',
                }).then(() => {
                    loadList(page, filterValue);
                })
            } else {
                Swal.fire({
                    icon: 'error',
                    title: 'Hata',
                    text: 'Bir sorun meydana geldi!',
                });
            }
        } catch (error) {
            console.error(error);
        }
    }

    const pages = Math.ceil(total / rowsPerPage);

    const onPaginationChange = useCallback(
        async (pageInput) => {
            setIsLoading(true);
            setPage(pageInput);
            await loadList(pageInput, filterValue);
        },
        []
    );

    const onSearchChange = useCallback(
        (value: string) => {
            if (value) {
                setIsLoading(true);
                setFilterValue(value);
                loadList(page, value);
                setPage(1);
            } else {
                setFilterValue("");
                loadList(page, "");
            }
        },
        []
    );

    const onClear = useCallback(() => {
        setFilterValue("");
        setPage(1);
    }, []);

    const topContent = useMemo(() => {
        return (
            <div className="flex flex-col gap-4">
                <div className="flex justify-between gap-3 items-end">
                    <Input
                        isClearable
                        className="w-full sm:max-w-[44%]"
                        placeholder="Arayın..."
                        startContent={<SearchIcon />}
                        value={filterValue}
                        onClear={() => onClear()}
                        onValueChange={onSearchChange}
                    />
                </div>
            </div>
        );
    }, [
        filterValue,
        onSearchChange,
        onClear,
    ]);

    const renderCell = useCallback((cellValue: any, columnKey: any, rowId: any) => {
        const cell = items.find(item => item.id === rowId);
        switch (columnKey) {
            case "image":
                return (
                    <User name=""
                        avatarProps={{ radius: "lg", src: `https://api.hollystone.com.tr/resources/images/${cell?.user?.image}` }}
                    >
                    </User>
                );
            case "firstName":
                return cell?.user?.firstName;
            case "lastName":
                return cell?.user?.lastName;
            case "phoneNumber":
                return cell?.user?.phoneNumber;
            case "email":
                return cell?.user?.email;
            case "prize":
                const prizeType = cell?.prizeType;
                return prizeType == 'product' ? cellValue : prizeType == 'hollyPoint' ? `${cellValue} Holly Puan` : 'Konser Bileti';
            case "delivery":
                return cellValue == 1 ? "Teslim edildi" : "Teslim edilmedi";
            case "actions":
                return (
                    cell?.delivery == 1 ?
                        <Button
                            color="primary"
                            onPress={() => { cancel(rowId) }}
                        >
                            İptal Et
                        </Button>
                        :
                        <Button
                            color="primary"
                            onPress={() => { deliver(rowId) }}
                        >
                            Teslim Et
                        </Button>
                );
            case "winnigDate":
                return (
                    dayjs(cell?.createdAt).format("DD/MM/YYYY")
                );
            default:
                return cellValue;
        }
    }, [items]);

    return (
        <Table
            aria-label="Example table with client async pagination"
            topContent={topContent}
            topContentPlacement="inside"
            bottomContent={
                pages > 0 ? (
                    <div className="flex w-full justify-center">
                        <Pagination
                            isCompact
                            showControls
                            showShadow
                            color="primary"
                            page={page}
                            total={pages}
                            onChange={onPaginationChange}
                        />
                    </div>
                ) : null
            }
            classNames={{
                table: "min-h-[400px]",
            }}
        >
            <TableHeader>
                <TableColumn key="image">Fotoğraf</TableColumn>
                <TableColumn key="firstName">İsim</TableColumn>
                <TableColumn key="lastName">Soyisim</TableColumn>
                <TableColumn key="phoneNumber">Telefon</TableColumn>
                <TableColumn key="email">E-mail</TableColumn>
                <TableColumn key="prize">Ödül</TableColumn>
                <TableColumn key="delivery">Teslim</TableColumn>
                <TableColumn key="winnigDate">Kazanma Tarihi</TableColumn>
                <TableColumn key="actions">İşlemler</TableColumn>
            </TableHeader>
            <TableBody
                isLoading={isLoading && !items.length}
                items={items}
                loadingContent={<Spinner />}
                emptyContent={"Kayıt bulunamadı!"}
            >
                {(item) => (
                    <TableRow key={item.id}>
                        {(columnKey) => (
                            <TableCell key={columnKey + item.id}>{renderCell(getKeyValue(item, columnKey), columnKey, item.id)}</TableCell>
                        )}
                    </TableRow>
                )}
            </TableBody>
        </Table>
    );
}

export default withRouter(WinnersWeeklyShamanTable);
