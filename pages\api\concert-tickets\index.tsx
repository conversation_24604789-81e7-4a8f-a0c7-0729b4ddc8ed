import { PrismaClient } from '@prisma/client';

const prisma = new PrismaClient();

export default async function handler(req, res) {
    if (req.method !== 'GET') {
        return res.status(405).json({ message: 'Method not allowed' });
    }

    const { id } = req.query;

    let where = {};

    if (id) {
        where = {
            concertId: parseInt(id),
        };
    } else {
        where = {
            isDeleted: false
        };
    }

    try {
        const concertTickets = await prisma.concertTicket.findMany({
            where,
            orderBy: {
                createdAt: 'desc'
            }
        });

        const totalCount = await prisma.concertTicket.count({ where });
        // Close the Prisma client connection
        await prisma.$disconnect();

        res.status(200).json({ results: concertTickets, totalCount });
    } catch (error) {
        console.error(error);
        // Close the Prisma client connection
        await prisma.$disconnect();
        res.status(500).json({ message: 'Internal server error' });
    }
}
