import { PrismaClient } from '@prisma/client';

const prisma = new PrismaClient();

export default async function handler(req, res) {
    if (req.method !== 'GET') {
        return res.status(405).json({ message: 'Method not allowed' });
    }

    const { vendorId } = req.query;

    try {
        const totalIncome = await prisma.$queryRaw`SELECT SUM(paidPrice) as total, COUNT(paidPrice) as totalCount FROM user_tickets ut INNER JOIN concert_tickets ct ON ct.id = ut.ticketId INNER JOIN concerts c ON c.id = ct.concertId WHERE DATE_FORMAT(c.date, '%Y-%m') = DATE_FORMAT(CURRENT_DATE, '%Y-%m') AND ut.payment = true AND ut.status IN (1, 2) AND c.vendorId = ${vendorId};`;
        const totalRefund = await prisma.$queryRaw`SELECT SUM(paidPrice) as total, COUNT(paidPrice) as totalCount FROM user_tickets ut INNER JOIN concert_tickets ct ON ct.id = ut.ticketId INNER JOIN concerts c ON c.id = ct.concertId WHERE DATE_FORMAT(c.date, '%Y-%m') = DATE_FORMAT(CURRENT_DATE, '%Y-%m') AND ut.payment = true AND ut.status IN (3, 4) AND c.vendorId = ${vendorId};`;
        const total = await prisma.$queryRaw`SELECT SUM(paidPrice) as total, COUNT(paidPrice) as totalCount FROM user_tickets ut INNER JOIN concert_tickets ct ON ct.id = ut.ticketId INNER JOIN concerts c ON c.id = ct.concertId WHERE DATE_FORMAT(c.date, '%Y-%m') = DATE_FORMAT(CURRENT_DATE, '%Y-%m') AND payment = true AND c.vendorId = ${vendorId};`;
        const bestSeller = await prisma.$queryRaw`SELECT c.name, c.image FROM user_tickets ut INNER JOIN concert_tickets ct ON ct.id = ut.ticketId INNER JOIN concerts c ON c.id = ct.concertId WHERE DATE_FORMAT(c.date, '%Y-%m') = DATE_FORMAT(CURRENT_DATE, '%Y-%m') AND ut.payment = true AND ut.status IN (1, 2) AND c.vendorId = ${vendorId} GROUP BY c.name ORDER BY COUNT(*) DESC LIMIT 1;`;

        // Close the Prisma client connection
        await prisma.$disconnect();

        res.status(200).json({ totalIncome: totalIncome[0].total ? totalIncome[0].total : 0, totalIncomeCount: parseInt(totalIncome[0].totalCount), totalRefund: totalRefund[0].total ? totalRefund[0].total : 0, totalRefundCount: parseInt(totalRefund[0].totalCount), total: total[0].total ? total[0].total : 0, totalCount: parseInt(total[0].totalCount), bestSeller: bestSeller[0] });
    } catch (error) {
        console.error(error);
        // Close the Prisma client connection
        await prisma.$disconnect();
        res.status(500).json({ message: 'Internal server error' });
    }
}
