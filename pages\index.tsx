import React from 'react';
import Head from 'next/head';
import { authMiddleware } from '../middleware';
import SidebarLayout from '@/layouts/SidebarLayout';
import PageHeader from '@/content/Dashboards/Crypto/PageHeader';
import PageTitleWrapper from '@/components/PageTitleWrapper';
import { Container, Grid } from '@mui/material';
import Footer from '@/components/Footer';
import HollyTicketSummary from '@/content/Dashboards/Crypto/HollyTicketSummary';
import HollyTicketReport from '@/content/Dashboards/Crypto/HollyTicketReport';
import HollyTicketVendor from '@/content/Dashboards/Crypto/HollyTicketVendor';
import HollyShopSummary from '@/content/Dashboards/Crypto/HollyShopSummary';
import HollyPointSummary from '@/content/Dashboards/Crypto/HollyPointSummary';
import Vendors from '@/content/Dashboards/Crypto/Vendors';
import { getCookie } from 'cookies-next';

const DashboardPage = () => {
  const authorityLevel = parseInt(getCookie("authorityLevel"));
  return (
    <>
      <Head>
        <title><PERSON> Stone Admin Panel</title>
      </Head>
      <PageTitleWrapper>
        <PageHeader />
      </PageTitleWrapper>
      <Container maxWidth="lg">
        <Grid
          container
          direction="row"
          justifyContent="center"
          alignItems="stretch"
          spacing={4}
        >
          {
            authorityLevel == 1 ? (
              <>
                <Grid item xs={12}>
                  <HollyTicketReport />
                </Grid>
                <Grid item xs={12}>
                  <HollyTicketSummary />
                </Grid>
                <Grid item xs={12}>
                  <HollyShopSummary />
                </Grid>
                <Grid item xs={12}>
                  <HollyPointSummary />
                </Grid>
                <Grid item xs={12}>
                  <Vendors />
                </Grid>
              </>
            ) : (
              <Grid item xs={12}>
                <HollyTicketVendor />
              </Grid>
            )
          }
        </Grid>
      </Container>
      <Footer />
    </>
  );
}

DashboardPage.getLayout = (page: any) => <SidebarLayout>{page}</SidebarLayout>;

export const getServerSideProps = authMiddleware(async (_context: any) => {
  // Your page-specific logic...
  return {
    props: {}, // You can add any props you need for the page
  };
});

export default DashboardPage;
