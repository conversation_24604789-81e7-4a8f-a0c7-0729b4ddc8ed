import React, { useState, useEffect } from 'react';
import Head from 'next/head';
import { useRouter } from 'next/router';
import { authMiddleware } from 'middleware';
import Swal from 'sweetalert2';
import { Container, Grid, Typography } from '@mui/material';
import { Image, Card, CardBody, CardHeader, Button, Input, Divider, Accordion, AccordionItem, Textarea } from '@nextui-org/react';
import SidebarLayout from '@/layouts/SidebarLayout';
import PageTitleWrapper from '@/components/PageTitleWrapper';
import Footer from '@/components/Footer';
import ArrowBackIcon from '@mui/icons-material/ArrowBack';
import Link from 'next/link';

const EditFranchisePage = () => {
    const router = useRouter();
    const [item, setItem]: any = useState({});
    const { id } = router.query;
    const [loading, setLoading] = useState(true);
    const loadList = async () => {
        const url = `/api/franchise?id=${id}`;

        try {
            const res = await fetch(url);

            if (res.status === 200) {
                const json = await res.json();
                json.results[0].images = json.results[0]?.images.split(',');
                json.results[0].birthDate = new Date(json.results[0]?.birthDate);
                setItem(json.results[0]);
            } else {
                Swal.fire({
                    icon: 'error',
                    title: 'Hata',
                    text: 'Bir sorun meydana geldi!',
                });
            }
        } catch (error) {
            console.error(error);
        } finally {
            setLoading(false);
        }
    };

    const fetchList = async () => {
        await loadList();
    };

    useEffect(() => {
        fetchList();
    }, []);

    const handleDelete = async () => {
        const shouldDelete = await Swal.fire({
            title: 'Dikkat!',
            text: 'Bu kaydı silmek istediğine gerçekten emin misin?',
            icon: 'warning',
            showCancelButton: true,
            confirmButtonText: 'Evet, sil!',
            cancelButtonText: 'Vazgeç',
        });

        if (shouldDelete.isConfirmed) {
            try {
                const response = await fetch(`/api/franchise/delete`, {
                    method: 'DELETE',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({ id: id }),
                });

                if (response.ok) {
                    router.push('/franchise');
                    Swal.fire({
                        icon: 'success',
                        title: 'Başarılı',
                        text: 'Kayıt silindi!',
                    });
                } else {
                    Swal.fire({
                        icon: 'error',
                        title: 'Hata',
                        text: 'Bir sorun meydana geldi!',
                    });
                }
            } catch (error) {
                console.error('An error occurred:', error);
            }
        }
    };

    const handleAnswer = (rowId) => {
        router.push('/franchise/answer?id=' + rowId);
    };

    if (loading) {
        return "Loading";
    }

    return (
        <>
            <Head>
                <title>Franchise - Detay</title>
            </Head>
            <PageTitleWrapper>
                <Grid container alignItems="center">
                    <Grid item>
                        <Typography variant="h3" component="h3" gutterBottom>
                            Franchise Detay
                        </Typography>
                    </Grid>
                </Grid>
            </PageTitleWrapper>
            <Container maxWidth="lg">
                <Grid
                    container
                    direction="row"
                    justifyContent="center"
                    alignItems="stretch"
                    spacing={4}
                >
                    <Grid item xs={12}>
                        <Card
                            isBlurred
                            className="border-none bg-white dark:bg-default-100/50 max-w-[610px]"
                            shadow="sm"
                        >
                            <CardHeader className="flex justify-between gap-3">
                                <div>
                                    <Link href="/franchise">
                                        <Button type="button" color="default" startContent={<ArrowBackIcon />}>
                                            Başvurular
                                        </Button>
                                    </Link>
                                </div>
                                <div>Franchise Bilgileri</div>
                                <div className="flex-end">
                                    <Button type="button" onClick={handleDelete} color="danger" className="mr-2">
                                        Sil
                                    </Button>
                                    <Button
                                        type="button"
                                        onClick={() => handleAnswer(item.id)}
                                        color="primary"
                                    >
                                        Cevapla
                                    </Button>
                                </div>
                            </CardHeader>

                            <Divider />

                            <CardBody>
                                <div className="flex">
                                    <div className="relative w-1/2" style={{
                                        minWidth: 400
                                    }}>
                                        <Accordion defaultExpandedKeys={["0"]}>
                                            {
                                                item.images.map((image: any, index: number) => {
                                                    return (
                                                        <AccordionItem key={index} aria-label={`Resim ${index + 1}`} title={`Resim ${index + 1}`}>
                                                            <Image
                                                                width={400}
                                                                height={400}
                                                                radius="none"
                                                                loading="eager"
                                                                style={{
                                                                    height: "100%"
                                                                }}
                                                                src={`https://api.hollystone.com.tr/resources/images/${image}`}
                                                                fallbackSrc="https://via.placeholder.com/400x400.png?text=Resim+Bulunamad%C4%B1"
                                                            />
                                                        </AccordionItem>
                                                    )
                                                })
                                            }
                                        </Accordion>
                                    </div>

                                    <div className="p-4 w-full">
                                        <div className="w-full mb-2 flex flex-row">
                                            <Input
                                                isReadOnly
                                                type="text"
                                                label="Franchise"
                                                placeholder="Franchise"
                                                name="franchise"
                                                value={item.franchise}
                                                className="mr-2"
                                            />
                                            <Input
                                                isReadOnly
                                                type="text"
                                                label="Yatırım Miktarı"
                                                placeholder="Yatırım Miktarı"
                                                name="investmentAmount"
                                                value={item.investmentAmount}
                                            />
                                        </div>
                                        <div className="w-full mb-2 flex flex-row">
                                            <Input
                                                isReadOnly
                                                type="text"
                                                label="Ad Soyad"
                                                placeholder="Ad Soyad"
                                                name="nameLastName"
                                                value={item.nameLastName}
                                                className="mr-2"
                                            />
                                            <Input
                                                isReadOnly
                                                type="date"
                                                label="Doğum Günü"
                                                placeholder="Doğum Günü"
                                                name="birthDate"
                                                value={item.birthDate?.toISOString().substr(0, 10)}
                                            />
                                        </div>
                                        <div className="w-full mb-2 flex flex-row">
                                            <Input
                                                isReadOnly
                                                type="text"
                                                label="Telefon Numarasi"
                                                placeholder="Telefon Numarasi"
                                                name="phoneNumber"
                                                value={item.phoneNumber}
                                                className="mr-2"
                                            />
                                            <Input
                                                isReadOnly
                                                type="text"
                                                label="Sabit Telefon"
                                                placeholder="Sabit Telefon"
                                                name="landPhoneNumber"
                                                value={item.landPhoneNumber}
                                                className="mr-2"
                                            />
                                            <Input
                                                isReadOnly
                                                type="text"
                                                label="E-mail"
                                                placeholder="E-mail"
                                                name="email"
                                                value={item.email}
                                            />
                                        </div>
                                        <Input
                                            isReadOnly
                                            type="text"
                                            label="Şuan Yaptığı İş"
                                            placeholder="Şuan Yaptığı İş"
                                            name="currentJob"
                                            className="mb-2"
                                            value={item.currentJob}
                                        />
                                        <Textarea
                                            isReadOnly
                                            label="Tecrübe"
                                            placeholder="Tecrübe"
                                            name="experience"
                                            className="mb-2"
                                            value={item.experience}
                                        ></Textarea>
                                        <Textarea
                                            isReadOnly
                                            label="Geçmiş veya Mevcut Franchise Sahipliğiniz Var Mı?"
                                            placeholder="Geçmiş veya Mevcut Franchise Sahipliğiniz Var Mı?"
                                            name="q1"
                                            className="mb-2"
                                            value={item.q1}
                                        ></Textarea>
                                        <Textarea
                                            isReadOnly
                                            label="Holly Stone Markasını Ne Kadar Süredir Tanıyorsunuz?"
                                            placeholder="Holly Stone Markasını Ne Kadar Süredir Tanıyorsunuz?"
                                            name="q2"
                                            className="mb-2"
                                            value={item.q2}
                                        ></Textarea>
                                        <div className="w-full mb-2 flex flex-row">
                                            <Input
                                                isReadOnly
                                                type="text"
                                                label="Mülk Durumu"
                                                placeholder="Mülk Durumu"
                                                name="property"
                                                className="mr-2"
                                                value={item.property}
                                            />
                                            <Input
                                                isReadOnly
                                                type="text"
                                                label="Alan"
                                                placeholder="Alan"
                                                name="areaSize"
                                                className="mr-2"
                                                value={item.areaSize}
                                            />
                                            <Input
                                                isReadOnly
                                                type="text"
                                                label="Kat Sayısı"
                                                placeholder="Kat Sayısı"
                                                name="floorCount"
                                                className="mr-2"
                                                value={item.floorCount}
                                            />
                                            <Input
                                                isReadOnly
                                                type="text"
                                                label="Çatı Yüksekliği"
                                                placeholder="Çatı Yüksekliği"
                                                name="roofHeight"
                                                className="mr-2"
                                                value={item.roofHeight}
                                            />
                                        </div>
                                        <Textarea
                                            isReadOnly
                                            label="Mülk Adresi"
                                            placeholder="Mülk Adresi"
                                            name="buildingAddress"
                                            className="mb-2"
                                            value={item.buildingAddress}
                                        ></Textarea>
                                    </div>
                                </div>
                            </CardBody>
                        </Card>
                    </Grid>
                </Grid>
            </Container>
            <Footer />
        </>
    );
};

EditFranchisePage.getLayout = (page: React.ReactNode) => <SidebarLayout>{page}</SidebarLayout>;

export const getServerSideProps = authMiddleware(async (_context: any) => {
    return {
        props: {},
    };
});

export default EditFranchisePage;
