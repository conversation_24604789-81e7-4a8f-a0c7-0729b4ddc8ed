import { PrismaClient } from '@prisma/client';

const prisma = new PrismaClient();

export default async function handler(req, res) {
    if (req.method === 'POST') {
        const { id } = req.body;

        try {
            const updatedDelivery = await prisma.shamanPrize.update({
                where: {
                    id: parseInt(id),
                },
                data: {
                    delivery: true
                },
            });

            res.status(200).json({ message: 'Teslimat güncellendi!', updatedItem: updatedDelivery });
        } catch (error) {
            console.error(error);
            res.status(500).json({ message: 'Internal server error', error });
        }
    } else {
        res.status(405).json({ message: 'Method not allowed' });
    }
}
