import React, { useEffect, useState } from 'react';
import Head from 'next/head';
import {
  Container,
  Grid,
  Typography,
  TextField,
  Button,
  Select,
  MenuItem,
  Box,
  Card,
  CardContent,
  CardHeader,
  Divider,
  Pagination,
} from '@mui/material';
import SidebarLayout from '@/layouts/SidebarLayout';
import Footer from '@/components/Footer';

const NotificationsPage = () => {
  const [notifications, setNotifications] = useState([]);
  const [users, setUsers] = useState([]);
  const [title, setTitle] = useState('');
  const [content, setContent] = useState('');
  const [type, setType] = useState('');
  const [selectedUsers, setSelectedUsers] = useState([]);
  const [page, setPage] = useState(1);
  const [totalCount, setTotalCount] = useState(0);

  const fetchNotificationsAndUsers = async () => {
    try {
      const res = await fetch(`/api/notifications?page=${page}`);
      if (res.ok) {
        const data = await res.json();
        setNotifications(data.notifications);
        setUsers(data.users);
        setTotalCount(data.totalCount);
      }
    } catch (error) {
      console.error('Bildirimler ve kullanıcılar alınamadı:', error);
    }
  };

  const handleSendNotification = async () => {
    if (!title || !content || !type) {
      alert('Başlık, içerik ve tür bilgisi zorunludur.');
      return;
    }
    try {
      const res = await fetch('/api/notifications', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          title,
          content,
          type,
          userIds: selectedUsers.length > 0 ? selectedUsers : null,
        }),
      });
      if (res.ok) {
        alert('Bildirim başarıyla gönderildi.');
        setTitle('');
        setContent('');
        setType('');
        setSelectedUsers([]);
        fetchNotificationsAndUsers();
      }
    } catch (error) {
      console.error('Bildirim gönderilemedi:', error);
    }
  };

  useEffect(() => {
    fetchNotificationsAndUsers();
  }, [page]);

  return (
    <>
      <Head>
        <title>Bildirim Yönetimi</title>
      </Head>
      <SidebarLayout>
        <Container maxWidth="lg">
          <Box my={4}>
            <Typography variant="h4" gutterBottom>
              Bildirim Yönetimi
            </Typography>
            <Typography variant="body1" color="textSecondary">
              Kullanıcılara bildirim gönderin veya geçmiş bildirimleri görüntüleyin.
            </Typography>
          </Box>

          <Grid container spacing={4}>
            {/* Bildirim Oluşturma Bölümü */}
            <Grid item xs={12} md={4}>
              <Card>
                <CardHeader
                  title="Bildirim Gönder"
                  subheader="Lütfen formu doldurarak bildirim gönderin."
                />
                <Divider />
                <CardContent>
                  <TextField
                    label="Başlık"
                    variant="outlined"
                    fullWidth
                    margin="normal"
                    value={title}
                    onChange={(e) => setTitle(e.target.value)}
                  />
                  <TextField
                    label="İçerik"
                    variant="outlined"
                    fullWidth
                    multiline
                    rows={3}
                    margin="normal"
                    value={content}
                    onChange={(e) => setContent(e.target.value)}
                  />
                  <Select
                    value={type}
                    onChange={(e) => setType(e.target.value)}
                    fullWidth
                    displayEmpty
                  >
                    <MenuItem value="" disabled>
                      Tür Seçin
                    </MenuItem>
                    <MenuItem value="info">Bilgilendirme</MenuItem>
                    <MenuItem value="warning">Uyarı</MenuItem>
                    <MenuItem value="error">Hata</MenuItem>
                  </Select>
                  <Select
                    multiple
                    fullWidth
                    value={selectedUsers}
                    onChange={(e) => setSelectedUsers(typeof e.target.value === 'string' ? e.target.value.split(',') : e.target.value)}
                    renderValue={(selected) =>
                      selected
                        .map((id) => users.find((user) => user.id === id)?.firstName)
                        .join(', ')
                    }
                    displayEmpty
                  >
                    <MenuItem value="" disabled>
                      Kullanıcıları Seç (Opsiyonel)
                    </MenuItem>
                    {users.map((user) => (
                      <MenuItem key={user.id} value={user.id}>
                        {user.firstName} {user.lastName}
                      </MenuItem>
                    ))}
                  </Select>
                  <Box mt={2}>
                    <Button
                      variant="contained"
                      color="primary"
                      fullWidth
                      onClick={handleSendNotification}
                    >
                      Bildirimi Gönder
                    </Button>
                  </Box>
                </CardContent>
              </Card>
            </Grid>

            {/* Bildirim Listesi Bölümü */}
            <Grid item xs={12} md={8}>
              <Card>
                <CardHeader
                  title="Bildirim Geçmişi"
                  subheader={`Toplam ${totalCount} bildirimden ${notifications.length} tanesi gösteriliyor.`}
                />
                <Divider />
                <CardContent>
                  {notifications.length > 0 ? (
                    notifications.map((notification) => (
                      <Box
                        key={notification.id}
                        p={2}
                        my={2}
                        sx={{ border: '1px solid #ddd', borderRadius: 1 }}
                      >
                        <Typography variant="subtitle1" gutterBottom>
                          {notification.title}
                        </Typography>
                        <Typography variant="body2">{notification.content}</Typography>
                        <Typography variant="caption" display="block" mt={1} color="textSecondary">
                          Gönderen: {notification.user?.firstName || 'Sistem'}{' '}
                          {notification.user?.lastName || ''} -{' '}
                          {new Date(notification.createdAt).toLocaleString('tr-TR')}
                        </Typography>
                      </Box>
                    ))
                  ) : (
                    <Typography>Bildirim bulunamadı.</Typography>
                  )}
                </CardContent>
                <Box display="flex" justifyContent="center" p={2}>
                  <Pagination
                    count={Math.ceil(totalCount / 10)}
                    page={page}
                    onChange={(_, value) => setPage(value)}
                  />
                </Box>
              </Card>
            </Grid>
          </Grid>
        </Container>
        <Footer />
      </SidebarLayout>
    </>
  );
};

export default NotificationsPage;
