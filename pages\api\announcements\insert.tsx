import { PrismaClient } from '@prisma/client';

const prisma = new PrismaClient();

export default async function handler(req, res) {
    if (req.method === 'POST') {
        const { header, content, detail, detailId, vendorId } = req.body;

        try {
            const createdAnnouncement = await prisma.announcement.create({
                data: {
                    vendorId,
                    header,
                    content,
                    detail,
                    detailId,
                    image: "",
                    isDeleted: false
                },
            });

            await prisma.announcement.update({
                where: {
                    id: createdAnnouncement.id,
                },
                data: {
                    image: `announcements/${createdAnnouncement.id}.webp`,
                },
            });

            res.status(201).json({ message: '<PERSON><PERSON>ru eklendi!', createdItem: createdAnnouncement });
        } catch (error) {
            console.error(error);
            res.status(500).json({ message: 'Internal server error', error });
        }
    } else {
        res.status(405).json({ message: 'Method not allowed' });
    }
}
