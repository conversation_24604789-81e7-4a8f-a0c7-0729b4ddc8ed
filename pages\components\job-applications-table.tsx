import React, { useState, useMemo, useCallback, useEffect } from "react";
import {
    Table,
    TableHeader,
    TableColumn,
    TableBody,
    TableRow,
    TableCell,
    Input,
    Button,
    DropdownTrigger,
    Dropdown,
    DropdownMenu,
    DropdownItem,
    Pagination,
    Spinner,
    getKeyValue
} from "@nextui-org/react";
import { SearchIcon } from "../../src/components/SearchIcon";
import { VerticalDotsIcon } from "../../src/components/VerticalDotsIcon";
import { withRouter } from 'next/router';
import Swal from 'sweetalert2';
import dayjs from "dayjs";

function JobApplicationTable({ router }) {
    const [filterValue, setFilterValue] = useState("");
    const [page, setPage] = useState(1);
    const [total, setTotal] = useState(0);
    const [isLoading, setIsLoading] = useState(true);
    const [items, setItems] = useState([]);
    const [selectedKeys, setSelectedKeys]: any = React.useState(new Set([]));

    const rowsPerPage = 10;

    const loadList = async (pageInput: number, filter: string) => {
        const url = `/api/job-applications?page=${pageInput}&pageSize=${rowsPerPage}&filter=${filter}`;

        try {
            const res = await fetch(url);

            if (res.status === 200) {
                const json = await res.json();

                const itemsWithAge = json.results.map((item) => {
                    const birthdate: Date = new Date(item.birthDate);
                    const currentDate: Date = new Date();

                    const birthYear = birthdate.getFullYear();
                    const currentYear = currentDate.getFullYear();
                    const age = currentYear - birthYear;

                    const birthMonth = birthdate.getMonth();
                    const currentMonth = currentDate.getMonth();
                    const birthDay = birthdate.getDate();
                    const currentDay = currentDate.getDate();

                    if (currentMonth < birthMonth || (currentMonth === birthMonth && currentDay < birthDay)) {
                        item.age = age - 1;
                    } else {
                        item.age = age;
                    }

                    return item;
                });

                setTotal(json.totalCount);
                setIsLoading(false);
                setItems(itemsWithAge);
            } else {
                setIsLoading(false);
                setItems([]);
            }
        } catch (error) {
            setIsLoading(false);
            setItems([]);
            console.error(error);
            throw error;
        }
    };

    const handleEdit = (rowId) => {
        router.push('/job-applications/edit?id=' + rowId);
    };

    const handleDelete = async (rowId) => {
        let question = 'Bu kaydı silmek istediğine gerçekten emin misin?';
        if (rowId === -1) {
            question = 'Seçili kayıtları silmek istediğine gerçekten emin misin?';
        }
        let selectedKeysArray = Array.from(selectedKeys);
        if (selectedKeys?.toString() == "all") {
            selectedKeysArray = items.map(item => item.id);
        }
        const shouldDelete = await Swal.fire({
            title: 'Dikkat!',
            text: question,
            icon: 'warning',
            showCancelButton: true,
            confirmButtonText: 'Evet, sil!',
            cancelButtonText: 'Vazgeç',
        });

        if (shouldDelete.isConfirmed) {
            try {
                const response = await fetch(`/api/job-applications/delete`, {
                    method: 'DELETE',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({ id: rowId, selectedKeysArray }),
                });

                if (response.ok) {
                    await loadList(1, filterValue);
                    Swal.fire({
                        icon: 'success',
                        title: 'Başarılı',
                        text: 'Kayıt silindi!',
                    });
                    setSelectedKeys(new Set([]));
                } else {
                    Swal.fire({
                        icon: 'error',
                        title: 'Hata',
                        text: 'Bir sorun meydana geldi!',
                    });
                }
            } catch (error) {
                console.error('An error occurred:', error);
            }
        }
    };


    useEffect(() => {
        loadList(1, filterValue);
    }, []);

    const pages = Math.ceil(total / rowsPerPage);

    const onPaginationChange = useCallback(
        async (pageInput) => {
            setIsLoading(true);
            setPage(pageInput);
            await loadList(pageInput, filterValue);
        },
        []
    );

    const onSearchChange = useCallback(
        (value: string) => {
            if (value) {
                setIsLoading(true);
                setFilterValue(value);
                loadList(page, value);
                setPage(1);
            } else {
                setFilterValue("");
                loadList(page, "");
            }
        },
        []
    );

    const onClear = useCallback(() => {
        setFilterValue("");
        setPage(1);
    }, []);

    const topContent = useMemo(() => {
        return (
            <div className="flex flex-col gap-4">
                <div className="flex justify-between gap-3 items-end">
                    <Input
                        isClearable
                        className="w-full sm:max-w-[44%]"
                        placeholder="Arayın..."
                        startContent={<SearchIcon />}
                        value={filterValue}
                        onClear={() => onClear()}
                        onValueChange={onSearchChange}
                    />
                    {
                        selectedKeys.size === 0 ?
                            (
                                <></>
                            )
                            :
                            (
                                <Button color="danger" onClick={() => { handleDelete(-1) }}>
                                    Sil
                                </Button>
                            )
                    }
                </div>
            </div>
        );
    }, [
        filterValue,
        onSearchChange,
        onClear,
        selectedKeys
    ]);

    const renderCell = useCallback((cellValue: any, columnKey: any, rowId: any) => {
        switch (columnKey) {
            case "status":
                return cellValue ? "aktif" : "pasif";
            case "createdAt":
                return dayjs(cellValue).format("DD/MM/YYYY HH:mm");
            case "actions":
                return (
                    <div className="relative flex justify-end items-center gap-2">
                        <Dropdown>
                            <DropdownTrigger>
                                <Button isIconOnly size="sm" variant="light">
                                    <VerticalDotsIcon className="text-default-300" width={undefined} height={undefined} />
                                </Button>
                            </DropdownTrigger>
                            <DropdownMenu>
                                <DropdownItem onClick={() => handleEdit(rowId)}>Görüntüle</DropdownItem>
                                <DropdownItem onClick={() => handleDelete(rowId)}>Sil</DropdownItem>
                            </DropdownMenu>
                        </Dropdown>
                    </div>
                );
            default:
                return cellValue;
        }
    }, []);

    return (
        <Table
            aria-label="Example table with client async pagination"
            topContent={topContent}
            topContentPlacement="inside"
            selectionMode="multiple"
            selectedKeys={selectedKeys}
            onSelectionChange={setSelectedKeys}
            bottomContent={
                pages > 0 ? (
                    <div className="flex w-full justify-center">
                        <Pagination
                            isCompact
                            showControls
                            showShadow
                            color="primary"
                            page={page}
                            total={pages}
                            onChange={onPaginationChange}
                        />
                    </div>
                ) : null
            }
            classNames={{
                table: "min-h-[400px]",
            }}
        >
            <TableHeader>
                <TableColumn key="nameLastName">Ad Soyad</TableColumn>
                <TableColumn key="cityName">Şehir</TableColumn>
                <TableColumn key="age">Yaş</TableColumn>
                <TableColumn key="phoneNumber">Telefon</TableColumn>
                <TableColumn key="email">E-mail</TableColumn>
                <TableColumn key="createdAt">Tarih</TableColumn>
                <TableColumn key="actions">İşlemler</TableColumn>
            </TableHeader>
            <TableBody
                isLoading={isLoading && !items.length}
                items={items}
                loadingContent={<Spinner />}
                emptyContent={"Kayıt bulunamadı!"}
            >
                {(item) => (
                    <TableRow key={item.name} className={item.isRead == false ? "font-bold" : ""}>
                        {(columnKey) => (
                            <TableCell className={item.isRead == false ? "font-bold" : ""}>{item.isRead == false && columnKey == 'nameLastName' ? (<span className="mr-2 text-primary">·</span>) : ""}{renderCell(getKeyValue(item, columnKey), columnKey, item.id)}</TableCell>
                        )}
                    </TableRow>
                )}
            </TableBody>
        </Table>
    );
}

export default withRouter(JobApplicationTable);
