import { PrismaClient } from '@prisma/client';

const prisma = new PrismaClient();

export default async function handler(req, res) {
    if (req.method === 'PUT') {
        const { id, cityId, invoiceToken, name, taxOffice, taxNumber, zipCode, address, email, phone, bankAccountName, iban, modules  } = req.body;

        try {
            const updatedVendor = await prisma.vendor.update({
                where: {
                    id: parseInt(id),
                },
                data: {
                    cityId,
                    invoiceToken,
                    name,
                    taxOffice,
                    taxNumber,
                    zipCode,
                    address,
                    email,
                    phone,
                    bankAccountName,
                    iban,
                    modules
                },
            });

            res.status(200).json({ message: '<PERSON><PERSON> g<PERSON>!', updatedItem: updatedVendor });
        } catch (error) {
            console.error(error);
            res.status(500).json({ message: 'Internal server error', error });
        }
    } else {
        res.status(405).json({ message: 'Method not allowed' });
    }
}
