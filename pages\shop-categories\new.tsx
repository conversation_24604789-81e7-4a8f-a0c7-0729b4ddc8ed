import React, { useState } from 'react';
import Head from 'next/head';
import { useRouter } from 'next/router';
import { authMiddleware } from 'middleware';
import Swal from 'sweetalert2';
import { Container, Grid, Typography, CircularProgress, Backdrop } from '@mui/material';
import { Dropdown, DropdownTrigger, DropdownItem, DropdownMenu, Card, CardBody, CardHeader, Button, Input, Divider } from '@nextui-org/react';
import SidebarLayout from '@/layouts/SidebarLayout';
import PageTitleWrapper from '@/components/PageTitleWrapper';
import Footer from '@/components/Footer';
import ArrowBackIcon from '@mui/icons-material/ArrowBack';
import Link from 'next/link';

const NewSnapPage = () => {
    const router = useRouter();
    const [item, setItem]: any = useState({});
    const dropdownItems = {
        true: "Aktif",
        false: "Pasif"
    };
    const [loading, setLoading] = useState(false);

    const handleInsert = async (event: React.FormEvent) => {
        event.preventDefault();
        if (!item.name) {
            Swal.fire({
                icon: 'error',
                title: '<PERSON>a',
                text: 'Lütfen alanları doldurun!',
            });
            return;
        }
        try {
            setLoading(true);
            const response = await fetch(`/api/shop-categories/insert`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify(item),
            });

            if (response.ok) {
                Swal.fire({
                    icon: 'success',
                    title: 'Başarılı',
                    text: 'Kayıt başarılı!',
                }).then(() => {
                    router.push('/shop-categories');
                });
            } else {
                Swal.fire({
                    icon: 'error',
                    title: 'Hata',
                    text: 'Bir sorun meydana geldi!',
                });
            }
        } catch (error) {
            console.error('An error occurred:', error);
        } finally {
            setLoading(false);
        }
    };

    return (
        <>
            <Head>
                <title>Kategori - Oluştur</title>
            </Head>
            <PageTitleWrapper>
                <Grid container alignItems="center">
                    <Grid item>
                        <Typography variant="h3" component="h3" gutterBottom>
                            Kategori Oluştur
                        </Typography>
                    </Grid>
                </Grid>
            </PageTitleWrapper>
            <Container maxWidth="lg">
                <Grid
                    container
                    direction="row"
                    justifyContent="center"
                    alignItems="stretch"
                    spacing={4}
                >
                    <Grid item xs={12}>
                        <Card
                            isBlurred
                            className="border-none bg-white dark:bg-default-100/50 max-w-[610px]"
                            shadow="sm"
                        >
                            <form onSubmit={handleInsert}>
                                <CardHeader className="flex justify-between gap-3">
                                    <div>
                                        <Link href="/shop-categories">
                                            <Button type="button" color="default" startContent={<ArrowBackIcon />}>
                                                Kategoriler
                                            </Button>
                                        </Link>
                                    </div>
                                    <div className="flex-end">
                                        <Button type="submit" color="primary">
                                            Kaydet
                                        </Button>
                                    </div>
                                </CardHeader>

                                <Divider />

                                <CardBody>
                                    <div className="flex">
                                        <div className="p-4 w-full">
                                            <Input
                                                type="text"
                                                label="Başlık"
                                                name="name"
                                                value={item.name}
                                                className="mb-2"
                                                onChange={(e: any) => setItem({ ...item, name: e.target.value })}
                                            />
                                            <div className="w-full mb-2 flex flex-row">
                                                <Input
                                                    type="number"
                                                    label="Sıra"
                                                    name="rank"
                                                    min={0}
                                                    value={item.rank}
                                                    className="mb-2 mr-2"
                                                    onChange={(e: any) => setItem({ ...item, rank: parseInt(e.target.value) })}
                                                />
                                                <Dropdown>
                                                    <DropdownTrigger>
                                                        <Button
                                                            variant="bordered"
                                                            className={`text-white capitalize h-14 ${item.status ? "bg-success" : "bg-danger"}`}
                                                        >
                                                            {dropdownItems[item.status?.toString()]}
                                                        </Button>
                                                    </DropdownTrigger>
                                                    <DropdownMenu
                                                        aria-label="Single selection example"
                                                        variant="flat"
                                                        disallowEmptySelection
                                                        selectionMode="single"
                                                        selectedKeys={[item.status?.toString()]}
                                                        onSelectionChange={(selected: any) => {
                                                            setItem({ ...item, status: (/true/i).test(selected.currentKey) })
                                                        }}
                                                    >
                                                        {Object.keys(dropdownItems).map((key) => (
                                                            <DropdownItem key={key}>{dropdownItems[key]}</DropdownItem>
                                                        ))}
                                                    </DropdownMenu>
                                                </Dropdown>
                                            </div>
                                        </div>
                                    </div>
                                </CardBody>
                            </form>
                        </Card>
                    </Grid>
                </Grid>
            </Container>
            <Footer />
            <Backdrop open={loading} style={{ zIndex: 9999 }}>
                <CircularProgress color="primary" />
            </Backdrop>
        </>
    );
};

NewSnapPage.getLayout = (page: React.ReactNode) => <SidebarLayout>{page}</SidebarLayout>;

export const getServerSideProps = authMiddleware(async (_context: any) => {
    return {
        props: {},
    };
});

export default NewSnapPage;
