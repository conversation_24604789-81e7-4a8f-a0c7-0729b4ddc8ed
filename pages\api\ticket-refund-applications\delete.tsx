import { PrismaClient } from '@prisma/client';

const prisma = new PrismaClient();

export default async function handler(req, res) {
    if (req.method === 'DELETE') {
        const { id, selectedKeysArray } = req.body;
        const intSelectedKeysArray = selectedKeysArray.map((key) => parseInt(key));

        try {
            if (intSelectedKeysArray.length < 1) {
                await prisma.refundApplication.update({
                    where: { id: parseInt(id) },
                    data: { isDeleted: true },
                });
            } else {
                for (const key of intSelectedKeysArray) {
                    const existingRecord = await prisma.refundApplication.findUnique({
                        where: { id: key },
                    });

                    if (!existingRecord) {
                        throw new Error(`Record with ID ${key} not found.`);
                    }

                    await prisma.refundApplication.update({
                        where: { id: key },
                        data: { isDeleted: true },
                    });
                }
            }

            res.status(200).json({ message: '<PERSON><PERSON> ba<PERSON>vu<PERSON>u başar<PERSON><PERSON><PERSON> bir şekilde silindi!' });
        } catch (error) {
            console.error(error);
            res.status(500).json({ message: 'Internal server error', error });
        }
    } else {
        res.status(405).json({ message: 'Method not allowed' });
    }
}
