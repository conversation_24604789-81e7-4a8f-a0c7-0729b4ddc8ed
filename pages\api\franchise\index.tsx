import { PrismaClient } from '@prisma/client';

const prisma = new PrismaClient();

export default async function handler(req, res) {
  if (req.method !== 'GET') {
    return res.status(405).json({ message: 'Method not allowed' });
  }

  const { id, page = 1, pageSize = 10, filter = "" } = req.query;

  let where = {};

  if (id) {
    where = {
      id: parseInt(id),
    };

    try {
      await prisma.franchise.update({
          where: {
              id: parseInt(id),
          },
          data: {
              isRead: true,
          },
      });
  } catch (error) {
      console.error(error);
      res.status(500).json({ message: 'Internal server error', error });
  }
  } else {
    where = {
      AND: [
        {
          OR: [
            { nameLastName: { contains: filter } },
            { franchise: { contains: filter } },
            { email: { contains: filter } },
          ],
        },
        { isDeleted: false },
      ],
    };
  }

  try {
    const franchises = await prisma.franchise.findMany({
      where,
      orderBy: {
          createdAt: 'desc'
      },
      skip: (parseInt(page) - 1) * parseInt(pageSize),
      take: parseInt(pageSize),
    });

    const totalCount = await prisma.franchise.count({ where });

    const totalCountUnRead = await prisma.franchise.count({ where: { isRead: false, isDeleted: false } });
    // Close the Prisma client connection
    await prisma.$disconnect();

    res.status(200).json({ results: franchises, totalCount, totalCountUnRead });
  } catch (error) {
    console.error(error);
    // Close the Prisma client connection
    await prisma.$disconnect();
    res.status(500).json({ message: 'Internal server error' });
  }
}
