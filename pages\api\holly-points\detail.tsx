import { PrismaClient } from '@prisma/client';
import { startOfYear, startOfMonth, startOfWeek } from 'date-fns';

const prisma = new PrismaClient();

export default async function handler(req, res) {
    if (req.method !== 'GET') {
        return res.status(405).json({ message: 'Method not allowed' });
    }

    const { id, dateRange, filter, vendorId } = req.query;

    const currentDate = new Date();

    let startDate;

    switch (parseInt(dateRange)) {
        case 1: // This month
            startDate = startOfMonth(currentDate);
            break;
        case 2: // This week
            startDate = startOfWeek(currentDate);
            break;
        default: // This year (or any other value)
            startDate = startOfYear(currentDate);
            break;
    }

    try {
        const allEarnings: any = await prisma.hollyPointsEarningHistory.findMany({
            where: {
                userId: parseInt(id),
                vendorId: parseInt(vendorId),
                createdAt: {
                    gte: startDate,
                }
            },
            include: {
                vendor: true
            },
            orderBy: {
                createdAt: 'desc',
            }
        });

        const allSpendings: any = await prisma.hollyPointsSpendHistory.findMany({
            where: {
                userId: parseInt(id),
                vendorId: parseInt(vendorId),
                createdAt: {
                    gte: startDate,
                }
            },
            include: {
                vendor: true
            },
            orderBy: {
                createdAt: 'desc',
            }
        });

        const mergedResults = [...allEarnings, ...allSpendings];
        const sortedResults = mergedResults.sort((a: any, b: any) =>
            new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime()
        );

        // Close the Prisma client connection
        await prisma.$disconnect();

        res.status(200).json({
            results: filter === "0" ? sortedResults : filter === "1" ? allEarnings : allSpendings,
        });
    } catch (error) {
        console.error(error);
        // Close the Prisma client connection even in case of an error
        await prisma.$disconnect();
        res.status(500).json({ message: 'Internal server error' });
    }
}
