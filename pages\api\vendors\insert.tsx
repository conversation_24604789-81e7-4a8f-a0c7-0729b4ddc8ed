import { PrismaClient } from '@prisma/client';

const prisma = new PrismaClient();

export default async function handler(req, res) {
    if (req.method === 'POST') {
        const { invoiceToken, name, cityId, taxOffice, taxNumber, zipCode, address, email, phone, bankAccountName, iban, modules  } = req.body;

        try {
            const createdCendor = await prisma.vendor.create({
                data: {
                    cityId,
                    invoiceToken,
                    name,
                    taxOffice,
                    taxNumber,
                    zipCode,
                    address,
                    email,
                    phone,
                    bankAccountName,
                    iban,
                    modules,
                    isDeleted: false
                },
            });

            res.status(201).json({ message: '<PERSON>i eklendi!', createdItem: createdCendor });
        } catch (error) {
            console.error(error);
            res.status(500).json({ message: 'Internal server error', error });
        }
    } else {
        res.status(405).json({ message: 'Method not allowed' });
    }
}
