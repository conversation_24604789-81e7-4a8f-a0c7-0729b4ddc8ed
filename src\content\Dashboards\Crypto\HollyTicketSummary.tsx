import React, { useState, useEffect } from 'react';
import {
  <PERSON>ton,
  <PERSON>,
  Box,
  Grid,
  Typography,
  useTheme,
  Divider,
  ListItem,
  ListItemText,
  List,
  CardHeader,
} from '@mui/material';
import { Select, SelectItem } from "@nextui-org/select";
import Link from 'next/link';
import { Image } from '@nextui-org/react';
import { Chart } from 'src/components/Chart';
import { Spinner } from '@nextui-org/react';

function HollyTicketSummary() {
  const theme = useTheme();

  const [month, setMonth] = useState(new Date().getMonth());
  const [total, setTotal] = useState(0);
  const [totalIncome, setTotalIncome] = useState(0);
  const [totalRefund, setTotalRefund] = useState(0);
  const [totalCount, setTotalCount] = useState(0);
  const [totalIncomeCount, setTotalIncomeCount] = useState(0);
  const [totalRefundCount, setTotalRefundCount] = useState(0);
  const [totalIncomeVendors, setTotalIncomeVendors]: any = useState([]);
  const [labels, setLabels]: any = useState([]);
  const [chartData, setChartData]: any = useState([]);
  const [bestSellers, setBestSellers]: any = useState({});
  const [loading, setLoading] = useState(true);
  const [chartOptions, setChartOptions] = useState(null);

  const turhishMonths = [
    'Ocak',
    'Şubat',
    'Mart',
    'Nisan',
    'Mayıs',
    'Haziran',
    'Temmuz',
    'Ağustos',
    'Eylül',
    'Ekim',
    'Kasım',
    'Aralık',
  ];

  const loadList = async () => {
    const url = `/api/holly-ticket-summary?month=${month}`;
    setLoading(true);

    try {
      const res = await fetch(url);

      if (res.status === 200) {
        const json = await res.json();
        setTotal(json.total);
        setTotalIncome(json.totalIncome);
        setTotalRefund(json.totalRefund);
        setTotalCount(json.totalCount);
        setTotalIncomeCount(json.totalIncomeCount);
        setTotalRefundCount(json.totalRefundCount);
        setTotalIncomeVendors(json.totalIncomeVendors);
        setBestSellers(json.bestSellers);
        setChartData(json.totalIncomeVendors.map(item => item.totalIncome));
        setLabels([json.totalIncomeVendors.map(item => item.vendorName)]);
      } else {
        console.log("API call failed");
      }
    } catch (error) {
      console.error(error);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    loadList();
  }, [month]);

  useEffect(() => {
    setChartOptions({
      chart: {
        background: 'transparent',
        stacked: false,
        toolbar: {
          show: false
        }
      },
      plotOptions: {
        pie: {
          donut: {
            size: '60%'
          }
        }
      },
      colors: ['#ff9900', '#1c81c2', '#333', '#5c6ac0'],
      dataLabels: {
        enabled: true,
        formatter: function (val) {
          return val.toFixed(2) + '%';
        },
        style: {
          colors: [theme.colors.alpha.trueWhite[100]]
        },
        background: {
          enabled: true,
          foreColor: theme.colors.alpha.trueWhite[100],
          padding: 8,
          borderRadius: 4,
          borderWidth: 0,
          opacity: 0.3,
          dropShadow: {
            enabled: true,
            top: 1,
            left: 1,
            blur: 1,
            color: theme.colors.alpha.black[70],
            opacity: 0.5
          }
        },
        dropShadow: {
          enabled: true,
          top: 1,
          left: 1,
          blur: 1,
          color: theme.colors.alpha.black[50],
          opacity: 0.5
        }
      },
      fill: {
        opacity: 1
      },
      labels: labels,
      legend: {
        labels: {
          colors: theme.colors.alpha.trueWhite[100]
        },
        show: false
      },
      stroke: {
        width: 0
      },
      theme: {
        mode: theme.palette.mode
      }
    });
  }, [labels]);

  return (
    <Card>
      <CardHeader title="Holly Ticket" />
      {
        loading ? (
          <div className="flex">
            <div className="p-4 w-full flex flex-col justify-center items-center">
              <Spinner />
            </div>
          </div>
        ) : (
          <Grid spacing={0} container>
            <Grid item xs={12} md={6}>
              <Box p={4}>
                <Typography
                  sx={{
                    pb: 3
                  }}
                  variant="h4"
                >
                  <Select
                    label="Ay"
                    placeholder="Bir seçim yapın..."
                    defaultSelectedKeys={[month.toString()]}
                    onChange={(e: any) => setMonth(parseInt(e.target.value))}
                  >
                    {turhishMonths.map((month, index) => {
                      return (
                        <SelectItem key={index} value={index}>
                          {month}
                        </SelectItem>
                      )
                    })}
                  </Select>
                </Typography>
                <Box>
                  <div className="w-full mb-5 flex flex-row">
                    <div className="mr-2">
                      <Typography
                        sx={{
                          pb: 3
                        }}
                        variant="h4"
                      >
                        {`Toplam Gelir (${totalIncomeCount})`}
                      </Typography>
                      <Typography variant="h1" gutterBottom sx={{
                        color: "green"
                      }}>
                        {totalIncome ?? 0} ₺
                      </Typography>
                    </div>
                    <div className="ml-2 mr-2">
                      <Typography
                        sx={{
                          pb: 3
                        }}
                        variant="h4"
                      >
                        {`Toplam İade (${totalRefundCount})`}
                      </Typography>
                      <Typography variant="h1" gutterBottom sx={{
                        color: "red"
                      }}>
                        {totalRefund ?? 0} ₺
                      </Typography>
                    </div>
                    <div className="ml-2">
                      <Typography
                        sx={{
                          pb: 3
                        }}
                        variant="h4"
                      >
                        {`Genel Toplam (${totalCount})`}
                      </Typography>
                      <Typography variant="h1" gutterBottom>
                        {total ?? 0} ₺
                      </Typography>
                    </div>
                  </div>
                  {
                    bestSellers.length > 0 ?
                      bestSellers.map((bestSeller, index) => {
                        return (
                          <Box
                            display="flex"
                            sx={{
                              pb: 4
                            }}
                            alignItems="center"
                            key={index}
                          >
                            <Image
                              width={theme.spacing(8)}
                              height={theme.spacing(8)}
                              radius="md"
                              loading="eager"
                              src={bestSeller?.image ? `https://api.hollystone.com.tr/resources/images/${bestSeller?.image}` : "https://api.hollystone.com.tr/resources/images/questionMark.webp"}
                            />
                            <Box sx={{
                              ml: 2
                            }}>
                              <Typography variant="h4">En Çok Bilet Satan Konser</Typography>
                              <Typography variant="subtitle2" noWrap>
                                {bestSeller.vendorName}
                              </Typography>
                            </Box>
                          </Box>
                        )
                      })
                      :
                      <Box
                        display="flex"
                        sx={{
                          pb: 4
                        }}
                        alignItems="center"
                      >
                        <Image
                          width={theme.spacing(8)}
                          height={theme.spacing(8)}
                          radius="md"
                          loading="eager"
                          src={"https://api.hollystone.com.tr/resources/images/questionMark.webp"}
                        />
                        <Box sx={{
                          ml: 2
                        }}>
                          <Typography variant="h4">En Çok Bilet Satan Konser</Typography>
                          <Typography variant="subtitle2" noWrap>

                          </Typography>
                        </Box>
                      </Box>
                  }
                </Box>
                <Grid container spacing={3}>
                  <Grid sm item>
                    <Link href="/concerts/new">
                      <Button fullWidth variant="outlined">
                        Konser Oluştur
                      </Button>
                    </Link>
                  </Grid>
                  <Grid sm item>
                    <Link href="/daily-activities/new">
                      <Button fullWidth variant="contained">
                        Günlük Etkinlik Oluştur
                      </Button>
                    </Link>
                  </Grid>
                </Grid>
              </Box>
            </Grid>
            <Grid
              sx={{
                position: 'relative'
              }}
              display="flex"
              alignItems="center"
              item
              xs={12}
              md={6}
            >
              <Box
                component="span"
                sx={{
                  display: { xs: 'none', md: 'inline-block' }
                }}
              >
                <Divider absolute orientation="vertical" />
              </Box>
              <Box py={4} pr={4} flex={1}>
                <Grid container spacing={0}>
                  <Grid
                    xs={12}
                    sm={5}
                    item
                    display="flex"
                    justifyContent="center"
                    alignItems="center"
                  >
                    <Chart
                      height={250}
                      options={chartOptions}
                      series={chartData}
                      type="donut"
                    />
                  </Grid>
                  <Grid xs={12} sm={7} item display="flex" alignItems="center">
                    <List
                      disablePadding
                      sx={{
                        width: '100%'
                      }}
                    >
                      {
                        totalIncomeVendors.map((item: any, index: number) => {
                          return (
                            <ListItem disableGutters key={index}>
                              <ListItemText
                                primary={item.vendorName}
                                primaryTypographyProps={{ variant: 'h5', noWrap: true }}
                              />
                              <Box>
                                <Typography align="right" variant="h4" noWrap>
                                  {item.totalIncome} ₺
                                </Typography>
                              </Box>
                            </ListItem>
                          )
                        })
                      }
                    </List>
                  </Grid>
                </Grid>
              </Box>
            </Grid>
          </Grid>
        )
      }
    </Card>
  );
}

export default HollyTicketSummary;
