import React, { useState } from "react";

interface AddTicketModalProps {
    visible: boolean;
    onClose: () => void;
    onAdd: (ticket: Ticket) => void;
}

interface Ticket {
    title: string;
    price: string;
    quota: number;
    hollyPoints: number;
    type: boolean;
}

const AddTicketModal: React.FC<AddTicketModalProps> = ({ visible, onClose, onAdd }) => {
    const [ticket, setTicket] = useState<Ticket>({
        title: '',
        price: '',
        quota: 0,
        hollyPoints: 0,
        type: false,
    });

    const handleChange = (field: keyof Ticket, value: any) => {
        setTicket((prev) => ({ ...prev, [field]: value }));
    };

    const handleAdd = () => {
        onAdd(ticket);
        onClose();
    };

    if (!visible) return null;

    return (
        <div className="fixed inset-0 flex items-center justify-center bg-black bg-opacity-50 z-50">
            <div className="bg-white rounded-lg shadow-lg w-full max-w-lg p-6">
                <h2 className="text-xl font-semibold text-gray-800 mb-4">Yeni Bilet Ekle</h2>
                <div className="space-y-4">
                    <div>
                        <label className="block text-sm font-medium text-gray-700">Başlık</label>
                        <input
                            type="text"
                            className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm"
                            placeholder="Bilet Başlığı"
                            value={ticket.title}
                            onChange={(e) => handleChange("title", e.target.value)}
                        />
                    </div>
                    <div>
                        <label className="block text-sm font-medium text-gray-700">Fiyat</label>
                        <input
                            type="number"
                            className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm"
                            placeholder="Fiyat"
                            value={ticket.price}
                            onChange={(e) => handleChange("price", e.target.value)}
                        />
                    </div>
                    <div>
                        <label className="block text-sm font-medium text-gray-700">Kontenjan</label>
                        <input
                            type="number"
                            className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm"
                            placeholder="Kontenjan"
                            value={ticket.quota}
                            onChange={(e) => handleChange("quota", parseInt(e.target.value))}
                        />
                    </div>
                    <div>
                        <label className="block text-sm font-medium text-gray-700">Holly Puan</label>
                        <input
                            type="number"
                            className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm"
                            placeholder="Holly Puan"
                            value={ticket.hollyPoints}
                            onChange={(e) => handleChange("hollyPoints", parseInt(e.target.value))}
                        />
                    </div>
                    <div>
                        <label className="block text-sm font-medium text-gray-700">Bilet Türü</label>
                        <select
                            className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm"
                            onChange={(e) => handleChange("type", e.target.value === "true")}
                        >
                            <option value="true">1+1</option>
                            <option value="false">Normal</option>
                        </select>
                    </div>
                </div>
                <div className="mt-6 flex justify-end space-x-4">
                    <button
                        className="px-4 py-2 bg-gray-200 text-gray-700 rounded-md hover:bg-gray-300"
                        onClick={onClose}
                    >
                        Vazgeç
                    </button>
                    <button
                        className="px-4 py-2 bg-indigo-600 text-white rounded-md hover:bg-indigo-700"
                        onClick={handleAdd}
                    >
                        Ekle
                    </button>
                </div>
            </div>
        </div>
    );
};

export default AddTicketModal;
