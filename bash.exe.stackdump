Stack trace:
Frame         Function      Args
0007FFFFBE20  00021005FE8E (000210285F68, 00021026AB6E, 000000000000, 0007FFFFAD20) msys-2.0.dll+0x1FE8E
0007FFFFBE20  0002100467F9 (000000000000, 000000000000, 000000000000, 0007FFFFC0F8) msys-2.0.dll+0x67F9
0007FFFFBE20  000210046832 (000210286019, 0007FFFFBCD8, 000000000000, 000000000000) msys-2.0.dll+0x6832
0007FFFFBE20  000210068CF6 (000000000000, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x28CF6
0007FFFFBE20  000210068E24 (0007FFFFBE30, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x28E24
0007FFFFC100  00021006A225 (0007FFFFBE30, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x2A225
End of stack trace
Loaded modules:
000100400000 bash.exe
7FFA862A0000 ntdll.dll
7FFA848A0000 KERNEL32.DLL
7FFA83A50000 KERNELBASE.dll
7FFA84970000 USER32.dll
7FFA83E20000 win32u.dll
000210040000 msys-2.0.dll
7FFA84870000 GDI32.dll
7FFA837E0000 gdi32full.dll
7FFA835B0000 msvcp_win.dll
7FFA83E50000 ucrtbase.dll
7FFA852B0000 advapi32.dll
7FFA85BC0000 msvcrt.dll
7FFA84440000 sechost.dll
7FFA841D0000 RPCRT4.dll
7FFA829D0000 CRYPTBASE.DLL
7FFA83920000 bcryptPrimitives.dll
7FFA84B40000 IMM32.DLL
