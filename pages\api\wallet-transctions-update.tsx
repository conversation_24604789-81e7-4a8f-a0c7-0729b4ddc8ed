// API tarafında işlemi onaylayacak endpoint
import { PrismaClient } from '@prisma/client';

const prisma = new PrismaClient();

export default async function handler(req, res) {
    if (req.method !== 'POST') {
        return res.status(405).json({ message: 'Method not allowed' });
    }

    const { transactionId } = req.body;

    try {
        const updatedTransaction = await prisma.transaction.update({
            where: { id: transactionId },
            data: { kasaonay: 1 }
        });

        await prisma.$disconnect();

        res.status(200).json(updatedTransaction);
    } catch (error) {
        await prisma.$disconnect();
        res.status(500).json({ message: 'Internal server error' });
    }
}