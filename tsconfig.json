{"compilerOptions": {"baseUrl": ".", "paths": {"@/*": ["./src/*"], "@/public/*": ["./public/*"], "react": ["./node_modules/@types/react"]}, "allowJs": true, "allowSyntheticDefaultImports": true, "jsx": "preserve", "lib": ["dom", "es2017"], "module": "esnext", "moduleResolution": "node", "noEmit": true, "noUnusedLocals": true, "noUnusedParameters": true, "preserveConstEnums": true, "removeComments": false, "skipLibCheck": true, "sourceMap": true, "strict": false, "strictPropertyInitialization": false, "strictNullChecks": false, "target": "esnext", "forceConsistentCasingInFileNames": true, "esModuleInterop": true, "resolveJsonModule": true, "isolatedModules": true, "noFallthroughCasesInSwitch": true, "incremental": true}, "exclude": ["node_modules"], "include": ["src", "next-env.d.ts", "**/*.ts", "**/*.tsx"]}