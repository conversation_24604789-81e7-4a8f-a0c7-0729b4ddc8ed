import React, { useState, useMemo, useCallback, useEffect } from "react";
import {
    Table,
    TableHeader,
    TableColumn,
    TableBody,
    TableRow,
    TableCell,
    Input,
    Button,
    Pagination,
    Spinner,
    getKeyValue,
    User,
} from "@nextui-org/react";
import { SearchIcon } from "../../src/components/SearchIcon";
import { withRouter } from 'next/router';
import dayjs from "dayjs";
import EditIcon from '@mui/icons-material/Edit';

function HollyPointsWinnersTable({ router }) {
    const [filterValue, setFilterValue] = useState("");
    const [page, setPage] = useState(1);
    const [total, setTotal] = useState(0);
    const [isLoading, setIsLoading] = useState(true);
    const [items, setItems] = useState([]);

    const rowsPerPage = 10;

    const loadList = async (pageInput: number, filter: string) => {
        const url = `/api/holly-points-winners?page=${pageInput}&pageSize=${rowsPerPage}&filter=${filter}`;

        try {
            const res = await fetch(url);

            if (res.status === 200) {
                const json = await res.json();

                setTotal(json.totalCount);
                setIsLoading(false);
                setItems(json.results);
            } else {
                setIsLoading(false);
                setItems([]);
            }
        } catch (error) {
            setIsLoading(false);
            setItems([]);
            console.error(error);
            throw error;
        }
    };

    const handleEdit = (rowId) => {
        router.push('/holly-points-winners/edit?id=' + rowId);
    };

    useEffect(() => {
        loadList(1, filterValue);
    }, []);

    const pages = Math.ceil(total / rowsPerPage);

    const onPaginationChange = useCallback(
        async (pageInput) => {
            setIsLoading(true);
            setPage(pageInput);
            await loadList(pageInput, filterValue);
        },
        []
    );

    const onSearchChange = useCallback(
        (value: string) => {
            if (value) {
                setIsLoading(true);
                setFilterValue(value);
                loadList(page, value);
                setPage(1);
            } else {
                setFilterValue("");
                loadList(page, "");
            }
        },
        []
    );

    const onClear = useCallback(() => {
        setFilterValue("");
        setPage(1);
    }, []);

    const topContent = useMemo(() => {
        return (
            <div className="flex flex-col gap-4">
                <div className="flex justify-between gap-3 items-end">
                    <Input
                        isClearable
                        className="w-full sm:max-w-[44%]"
                        placeholder="Arayın..."
                        startContent={<SearchIcon />}
                        value={filterValue}
                        onClear={() => onClear()}
                        onValueChange={onSearchChange}
                    />
                </div>
            </div>
        );
    }, [
        filterValue,
        onSearchChange,
        onClear,
    ]);

    const renderCell = useCallback((cellValue: any, columnKey: any, rowId: any) => {
        const cell = items.find(item => item.id === rowId);
        const birthDateObj: Date = new Date(cell?.user?.dateOfBirth);
        const currentDate: Date = new Date();
        const ageInMilliseconds: number = currentDate.getTime() - birthDateObj.getTime();
        const ageInYears = Math.floor(ageInMilliseconds / (365.25 * 24 * 60 * 60 * 1000));
        switch (columnKey) {
            case "image":
                return (
                    <User name=""
                        avatarProps={{ radius: "lg", src: `https://api.hollystone.com.tr/resources/images/${cell?.user?.image}` }}
                    >
                    </User>
                );
            case "firstName":
                return (
                    cell?.user?.firstName
                );
            case "lastName":
                return (
                    cell?.user?.lastName
                );
            case "phoneNumber":
                return (
                    cell?.user?.phoneNumber
                );
            case "email":
                return (
                    cell?.user?.email
                );
            case "actions":
                return (
                    <Button
                        onClick={() => handleEdit(cell?.user?.id)}
                        color="primary"
                        startContent={<EditIcon />}
                    >
                        Kazanım Detay
                    </Button>
                );
            case "registrationDate":
                return (
                    dayjs(cell?.createdAt).format("DD/MM/YYYY")
                );
            case "age":
                return (
                    ageInYears
                );
            default:
                return cellValue;
        }
    }, [items]);

    return (
        <Table
            aria-label="Example table with client async pagination"
            topContent={topContent}
            topContentPlacement="inside"
            bottomContent={
                pages > 0 ? (
                    <div className="flex w-full justify-center">
                        <Pagination
                            isCompact
                            showControls
                            showShadow
                            color="primary"
                            page={page}
                            total={pages}
                            onChange={onPaginationChange}
                        />
                    </div>
                ) : null
            }
            classNames={{
                table: "min-h-[400px]",
            }}
        >
            <TableHeader>
                <TableColumn key="image">Fotoğraf</TableColumn>
                <TableColumn key="firstName">İsim</TableColumn>
                <TableColumn key="lastName">Soyisim</TableColumn>
                <TableColumn key="age">Yaş</TableColumn>
                <TableColumn key="phoneNumber">Telefon</TableColumn>
                <TableColumn key="email">E-mail</TableColumn>
                <TableColumn key="hollyPoints">HollyPuan</TableColumn>
                <TableColumn key="actions">İşlemler</TableColumn>
            </TableHeader>
            <TableBody
                isLoading={isLoading && !items.length}
                items={items}
                loadingContent={<Spinner />}
                emptyContent={"Kayıt bulunamadı!"}
            >
                {(item) => (
                    <TableRow key={item.id}>
                        {(columnKey) => (
                            <TableCell key={columnKey + item.id}>{renderCell(getKeyValue(item, columnKey), columnKey, item.id)}</TableCell>
                        )}
                    </TableRow>
                )}
            </TableBody>
        </Table>
    );
}

export default withRouter(HollyPointsWinnersTable);
