export default function handler(req: any, res: any) {

    const { from, to, subject, text, html } = req.body

    let nodemailer = require('nodemailer')
    var transporter = nodemailer.createTransport({
        host: "sandbox.smtp.mailtrap.io",
        port: 2525,
        auth: {
            user: "2e02788538f400",
            pass: "14294c48341743"
        }
    });
    const mailData = {
        from,
        to,
        subject,
        text,
        html
    }

    try {
        transporter.sendMail(mailData, function (err, info) {
            if (err)
                console.log(err)
            else
                console.log(info)
        })
        res.status(200).json({ type: 'success', message: 'Mail gönderildi!' });
    } catch (error) {
        console.error(error);
        res.status(500).json({ message: 'Internal server error' });
    }
}