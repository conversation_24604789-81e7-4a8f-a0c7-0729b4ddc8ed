import React, { useState, useEffect } from 'react';
import {
  <PERSON>ton,
  Card,
  Grid,
  Box,
  CardContent,
  Typography
} from '@mui/material';
import Link from 'next/link';
import AddTwoToneIcon from '@mui/icons-material/AddTwoTone';
import { Spinner } from '@nextui-org/react';

function Vendors() {
  const [vendors, setVendors] = useState([]);
  const [loading, setLoading] = useState(true);

  const date = new Date();
  const turhishMonths = [
    'Ocak',
    'Şubat',
    'Mart',
    '<PERSON><PERSON>',
    'Mayı<PERSON>',
    'Ha<PERSON><PERSON>',
    'Temmuz',
    'A<PERSON>ust<PERSON>',
    '<PERSON><PERSON><PERSON><PERSON>',
    '<PERSON><PERSON>',
    'Kasım',
    'Aralık',
  ];
  const thisMonthTurkish = turhishMonths[date.getMonth()];

  const loadList = async () => {
    const url = `/api/vendorsStatistics`;
    setLoading(true);

    try {
      const res = await fetch(url);

      if (res.status === 200) {
        const json = await res.json();
        setVendors(json.vendors);
      } else {
        console.log("API call failed");
      }
    } catch (error) {
      console.error(error);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    loadList();
  }, []);

  if (loading == true) return <Spinner />

  return (
    <>
      <Box
        display="flex"
        alignItems="center"
        justifyContent="space-between"
        sx={{
          pb: 3
        }}
      >
        <Typography variant="h3">{`Bayiler (${vendors.length})`}</Typography>
        <Link href="/vendors/new">
        <Button
          size="small"
          variant="outlined"
          startIcon={<AddTwoToneIcon fontSize="small" />}
        >
          Bayi oluştur
        </Button>
        </Link>
      </Box>
      <Grid container spacing={3}>
        {
          vendors.map((item: any, index: number) => {
            return (
              <Grid key={index} xs={12} sm={6} md={3} item>
                <Card
                  sx={{
                    px: 1
                  }}
                >
                  <CardContent>
                    <Typography variant="h5" noWrap>
                      {item.vendorName}
                    </Typography>
                    <Typography variant="subtitle1" noWrap>
                      {`${thisMonthTurkish} Ayı Toplam Gelir`}
                    </Typography>
                    <Box
                      sx={{
                        pt: 3
                      }}
                    >
                      <Typography variant="h3" gutterBottom noWrap>
                        {item.combinedIncome ? item.combinedIncome.toFixed(2) : 0} ₺
                      </Typography>
                    </Box>
                  </CardContent>
                </Card>
              </Grid>
            )
          })
        }

      </Grid>
    </>
  );
}

export default Vendors;
