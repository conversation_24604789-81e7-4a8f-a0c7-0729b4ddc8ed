import React, { useState, useEffect } from 'react';
import Head from 'next/head';
import { useRouter } from 'next/router';
import { authMiddleware } from 'middleware';
import Swal from 'sweetalert2';
import { Container, Grid, Typography } from '@mui/material';
import { Card, CardBody, CardHeader, Button, Divider, Snippet } from '@nextui-org/react';
import { Select, SelectItem } from "@nextui-org/select";
import SidebarLayout from '@/layouts/SidebarLayout';
import PageTitleWrapper from '@/components/PageTitleWrapper';
import Footer from '@/components/Footer';
import ArrowBackIcon from '@mui/icons-material/ArrowBack';
import Link from 'next/link';
import dayjs from 'dayjs';

const EditStagePage = () => {
    const router = useRouter();
    const [items, setItems] = useState<Array<any>>([]);
    const [vendors, setVendors] = useState([]);
    const { id } = router.query;
    const [loading, setLoading] = useState(true);
    const [dateRange, setDateRange] = useState<number>(0);
    const [filter, setFilter] = useState<number>(0);
    const [vendorId, setVendorId] = useState<number>(1);
    const dropdownItems = ["Yıllık", "Aylık", "Haftalık"];
    const filterItems = ["Hepsi", "Kazananlar", "Harcayanlar"];

    const loadList = async () => {
        const url = `/api/holly-points/detail?id=${id}&filter=${filter}&dateRange=${dateRange}&vendorId=${vendorId}`;

        try {
            const res = await fetch(url);

            if (res.ok) {
                const json = await res.json();
                setItems(json.results);
            } else {
                throw new Error('API request failed');
            }
        } catch (error) {
            console.error(error);
            Swal.fire({
                icon: 'error',
                title: 'Hata',
                text: 'Bir sorun meydana geldi!',
            });
        } finally {
            setLoading(false);
        }
    };

    const loadVendors = async () => {
        const url = `/api/vendors`;

        try {
            const res = await fetch(url);

            if (res.status === 200) {
                const json = await res.json();
                setVendors(json.results);
            } else {
                Swal.fire({
                    icon: 'error',
                    title: 'Hata',
                    text: 'Bir sorun meydana geldi!',
                });
            }
        } catch (error) {
            console.error(error);
        } finally {
            setLoading(false);
        }
    };
    useEffect(() => {
        loadVendors();
    }, []);

    useEffect(() => {
        loadList();
    }, [dateRange, filter, vendorId]);

    if (loading) {
        return "Loading";
    }

    return (
        <>
            <Head>
                <title>Holly Puan Geçmişi</title>
            </Head>
            <PageTitleWrapper>
                <Grid container alignItems="center">
                    <Grid item>
                        <Typography variant="h3" component="h3" gutterBottom>
                            Holly Puan Geçmişi
                        </Typography>
                    </Grid>
                </Grid>
            </PageTitleWrapper>
            <Container maxWidth="lg">
                <Grid
                    container
                    direction="row"
                    justifyContent="center"
                    alignItems="stretch"
                    spacing={4}
                >
                    <Grid item xs={12}>
                        <Card
                            isBlurred
                            className="border-none bg-white dark:bg-default-100/50"
                            shadow="sm"
                        >
                            <CardHeader className="flex justify-between gap-3">
                                <div className="flex items-center">
                                    <Link href="/holly-points">
                                        <Button type="button" color="default" startContent={<ArrowBackIcon />}>
                                            Holly Puan Listesi
                                        </Button>
                                    </Link>
                                </div>
                                <div>Geçmiş</div>
                                <div className="flex items-center justify-end gap-2">
                                    <Select
                                        placeholder="Bir seçim yapın..."
                                        labelPlacement="outside"
                                        defaultSelectedKeys={[filter?.toString()]}
                                        onSelectionChange={(e: any) => {
                                            setFilter(parseInt(e.currentKey));
                                        }}
                                        style={{
                                            minWidth: "150px"
                                        }}
                                        className="mr-2"
                                    >
                                        {filterItems.map((dItem: any, dIndex: any) => (
                                            <SelectItem key={dIndex} value={dIndex}>
                                                {dItem}
                                            </SelectItem>
                                        ))}
                                    </Select>
                                    <Select
                                        placeholder="Bir seçim yapın..."
                                        labelPlacement="outside"
                                        defaultSelectedKeys={[dateRange?.toString()]}
                                        onSelectionChange={(e: any) => {
                                            setDateRange(parseInt(e.currentKey));
                                        }}
                                        style={{
                                            minWidth: "150px"
                                        }}
                                        className="mr-2"
                                    >
                                        {dropdownItems.map((dItem: any, dIndex: any) => (
                                            <SelectItem key={dIndex} value={dIndex}>
                                                {dItem}
                                            </SelectItem>
                                        ))}
                                    </Select>
                                    <Select
                                        placeholder="Bir seçim yapın..."
                                        labelPlacement="outside"
                                        defaultSelectedKeys={[vendorId?.toString()]}
                                        onSelectionChange={(e: any) => {
                                            setVendorId(parseInt(e.currentKey));
                                        }}
                                        style={{
                                            minWidth: "150px"
                                        }}
                                    >
                                        {vendors.map((dItem: any, dIndex: any) => (
                                            <SelectItem key={dItem.id} value={dIndex}>
                                                {dItem.name}
                                            </SelectItem>
                                        ))}
                                    </Select>
                                </div>
                            </CardHeader>

                            <Divider />

                            <CardBody>
                                <div className="flex">
                                    <div className="p-4 w-full">
                                        {
                                            items.map((item: any) => {
                                                const earnFrom = item.earnFrom == 1 ? "Holly Ticket"
                                                    : item.earnFrom == 2 ? "Holly Store"
                                                        : item.earnFrom == 3 ? "Kasa"
                                                            : "Holly Snap";

                                                const spendTo = item.spendTo == 1 ? "Holly Ticket"
                                                    : item.spendTo == 2 ? "Holly Store"
                                                        : item.spendTo == 3 ? "Kasa"
                                                            : "Holly Snap";

                                                let color;

                                                if (item.earnFrom) {
                                                    color = item.earnFrom == 1 ? "primary"
                                                        : item.earnFrom == 2 ? "success"
                                                            : item.earnFrom == 3 ? "warning"
                                                                : "default";
                                                } else {
                                                    color = item.spendTo == 1 ? "primary"
                                                        : item.spendTo == 2 ? "success"
                                                            : item.spendTo == 3 ? "warning"
                                                                : "default";
                                                }

                                                return (
                                                    <Snippet
                                                        key={item.id}
                                                        symbol=""
                                                        color={color}
                                                        size="lg"
                                                        className="mb-2"
                                                        hideCopyButton
                                                        fullWidth
                                                    >
                                                        {`${item.vendor.name} - ${dayjs(item.createdAt).format("DD.MM.YYYY HH:mm")} @ ${item.earnFrom !== undefined ? "Kazanım" : "Harcama"}: ${item.hollyPoints} points - ${item.earnFrom !== undefined ? earnFrom : spendTo}`}
                                                    </Snippet>
                                                );
                                            })
                                        }
                                    </div>
                                </div>
                            </CardBody>
                        </Card>
                    </Grid>
                </Grid>
            </Container>
            <Footer />
        </>
    );
};

EditStagePage.getLayout = (page: React.ReactNode) => <SidebarLayout>{page}</SidebarLayout>;

export const getServerSideProps = authMiddleware(async (_context: any) => {
    return {
        props: {},
    };
});

export default EditStagePage;
