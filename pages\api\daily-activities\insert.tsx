import { PrismaClient } from '@prisma/client';

const prisma = new PrismaClient();

export default async function handler(req, res) {
    if (req.method === 'POST') {
        const { name, dayOfWeek, gateDate, description, vendorId, stageId, date } = req.body;

        try {
            const createdDailyActivity = await prisma.dailyActivity.create({
                data: {
                    stageId,
                    vendorId,
                    name: name,
                    dayOfWeek,
                    description: description,
                    gateDate: gateDate, 
                    date,
                    image: "",
                    isDeleted: false
                },
            });

            await prisma.dailyActivity.update({
                where: {
                    id: createdDailyActivity.id,
                },
                data: {
                    image: `/daily_activities/${createdDailyActivity.id}.webp`,
                },
            });

            res.status(201).json({ message: 'Günlük etkinlik eklendi!', createdItem: createdDailyActivity });
        } catch (error) {
            console.error(error);
            res.status(500).json({ message: 'Internal server error', error });
        }
    } else {
        res.status(405).json({ message: 'Method not allowed' });
    }
}
